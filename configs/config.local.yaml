env: "local"

server:
  address: ":28000"
  openapi_path: "/api.json"
  swagger_path: "/apidoc"
  data_path: "./data/business-workflow-data"

logger:
  level: "all"
  stdout: true
  file: "business-workflow-local-{Y-m-d}.log"
  path: "./log"

db:
  link: "root:12345678@tcp(127.0.0.1:3306)/test?charset=utf8mb4&parseTime=True&loc=Local"
  max_idle_conns: 10
  max_open_conns: 25
  conn_max_lifetime_seconds: 3600

redis:
  host: "127.0.0.1"
  port: 6379
  password: ""
  database: 0

temporal:
  queue: "goframe-local-task-queue-zy"
  host_port: "temporal-frontend-basic-service.svc.cluster.local:80"
  start_to_close_timeout: 1
  schedule_to_close_timeout: 1440
  initial_interval: 5
  backoff_coefficient: 2.0
  maximum_interval: 65
  maximum_attempts: 300
obs:
  vendor: ali
  access_key: LTAI5tKgvBShWP92WXvDoHpt
  secret_key: ******************************
  oss_arn: 'acs:ram::1538072316762279:role/ramoss-oss-test-ali-bj-xp-allvoice-cn'
  bucket_name: oss-test-ali-bj-xp-allvoice-cn
  endpoint: oss-cn-beijing-internal.aliyuncs.com
  public_end_point:  oss-cn-beijing.aliyuncs.com
  region: cn-north-4
  cdn_name: cdn-allvoice-down-cn-testing.funnycp.com
  object_dir: allvoice

omni_business_grpc: "api-business-cn-basic-service-cn.svc.cluster.local:80"
omni_balance_grpc: "************:50041"
omni_tenant_grpc: "************:50042"
omni_engine_grpc: "omni-engine-cn-basic-service-cn.svc.cluster.local:80"


timer_config:
  config_file: "configs/timer-dye.json"

id_gen:
  address: "omni-openapi-testing.funnycp.com:80"

local_path: "/data/commentary/bisvideos"

# FFmpeg 并发控制配置
# 对于16核CPU，建议设置为8个并发，既能充分利用资源又不会过载
ffmpeg_max_concurrency: 8
