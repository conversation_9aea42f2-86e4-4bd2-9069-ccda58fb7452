package inpainting

import (
	"business-workflow/internal/entity/do"
	"context"
)

type IInpaintingOcrRepo interface {
	// 根据子任务ID查找任务
	GetOcrTaskBySubId(ctx context.Context, subTaskId int64) (*do.WorkflowOcrTask, error)
	GetOcrTaskByBid(ctx context.Context, bid int64) (*do.WorkflowOcrTask, error)
	// 创建任务
	CreateOcrTask(ctx context.Context, task *do.WorkflowOcrTask) error

	DeleteOcrTask(ctx context.Context, taskId int64) error

	// 创建engine任务并更新ocr任务状态
	//CreateEngineTaskAndUpdateOcrTask(ctx context.Context, engineTask *do.BusinessEngineTask, task *do.WorkflowOcrTask) error

	CreateEngineTasksAndUpdateOcrTask(ctx context.Context, engineTask []*do.BusinessEngineTask, task *do.WorkflowOcrTask) error

	UpdateOcrTask(ctx context.Context, task *do.WorkflowOcrTask) error

	UpdateOcrTaskAndSaveNotitleVideoUrl(ctx context.Context, task *do.WorkflowOcrTask, notitleUrl string) error

	UpdateOcrAndCreateChunks(ctx context.Context, task *do.WorkflowOcrTask, chunks []*do.WorkflowEraseChunkTask) error

	// 获取所有的chunks
	GetAllChunks(ctx context.Context, ocrId int64) ([]*do.WorkflowEraseChunkTask, error)
}
