package impl

import (
	"business-workflow/internal/common/db"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/repo/inpainting"
	"context"

	"gorm.io/gorm"
)

type InpaintingOcrTaskImpl struct {
	db *gorm.DB
}

func NewInpaintingOcrTaskImpl() inpainting.IInpaintingOcrRepo {
	return &InpaintingOcrTaskImpl{db: db.GetDB()}
}

func (impl *InpaintingOcrTaskImpl) GetOcrTaskBySubId(ctx context.Context, subTaskId int64) (task *do.WorkflowOcrTask, err error) {
	err = impl.db.WithContext(ctx).Model(&do.WorkflowOcrTask{}).Where("sub_task_id = ?", subTaskId).Last(&task).Error
	// if errors.Is(gorm.ErrRecordNotFound, err) {
	// 	return nil, nil
	// }
	return
}

func (impl *InpaintingOcrTaskImpl) GetOcrTaskByBid(ctx context.Context, bid int64) (*do.WorkflowOcrTask, error) {
	task := &do.WorkflowOcrTask{}
	err := impl.db.WithContext(ctx).Debug().Model(&do.WorkflowOcrTask{}).Where("bid = ?", bid).First(&task).Error
	return task, err
}

// 创建任务
func (impl *InpaintingOcrTaskImpl) CreateOcrTask(ctx context.Context, task *do.WorkflowOcrTask) error {
	return impl.db.WithContext(ctx).Model(&do.WorkflowOcrTask{}).Create(task).Error
}

func (impl *InpaintingOcrTaskImpl) DeleteOcrTask(ctx context.Context, taskId int64) error {
	return impl.db.WithContext(ctx).Delete(&do.WorkflowOcrTask{}, taskId).Error
}

func (impl *InpaintingOcrTaskImpl) CreateEngineTasksAndUpdateOcrTask(ctx context.Context, engineTasks []*do.BusinessEngineTask, task *do.WorkflowOcrTask) error {
	return impl.db.Transaction(func(tx *gorm.DB) error {
		// 创建engine任务
		err := tx.WithContext(ctx).Create(engineTasks).Error
		if err != nil {
			return err
		}
		//fields := []string{"status", "ocr_submit_at", "user_rect"}
		// 更新ocr任务状态
		//err = tx.Model(&do.WorkflowOcrTask{}).Where("id = ?", task.Id).Select(fields).Updates(task).Error
		err = tx.Save(task).Error
		if err != nil {
			return err
		}
		return nil
	})
}

func (impl *InpaintingOcrTaskImpl) UpdateOcrTask(ctx context.Context, task *do.WorkflowOcrTask) error {
	return impl.db.WithContext(ctx).Model(&do.WorkflowOcrTask{}).Where("id = ?", task.Id).Save(task).Error
}

func (impl *InpaintingOcrTaskImpl) UpdateOcrAndCreateChunks(ctx context.Context, task *do.WorkflowOcrTask, chunks []*do.WorkflowEraseChunkTask) error {
	return impl.db.Transaction(func(tx *gorm.DB) error {
		// 创建chunks
		err := tx.WithContext(ctx).Create(chunks).Error
		if err != nil {
			return err
		}
		err = tx.Save(task).Error
		if err != nil {
			return err
		}
		return nil
	})
}

func (impl *InpaintingOcrTaskImpl) GetAllChunks(ctx context.Context, ocrId int64) (res []*do.WorkflowEraseChunkTask, err error) {
	err = impl.db.WithContext(ctx).Model(&do.WorkflowEraseChunkTask{}).Where("ocr_id = ?", ocrId).Find(&res).Error
	return
}

func (impl *InpaintingOcrTaskImpl) UpdateOcrTaskAndSaveNotitleVideoUrl(ctx context.Context, task *do.WorkflowOcrTask, notitleUrl string) error {
	return impl.db.Transaction(func(tx *gorm.DB) error {
		// 保存子任务的
		err := tx.WithContext(ctx).Model(&do.CommentarySubTask{}).Where("id = ?", task.SubTaskId).Updates(map[string]interface{}{
			"no_subtitle_material_highlight_url": notitleUrl,
		}).Error
		if err != nil {
			return err
		}
		return tx.Save(task).Error
	})
}
