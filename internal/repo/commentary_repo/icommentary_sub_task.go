package commentary_repo

import (
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/common"
	"business-workflow/internal/entity/do"
	"context"

	"gorm.io/gorm"
)

// ICommentarySubTaskRepo 解说子任务仓储接口
type ICommentarySubTaskRepo interface {
	// CreateSubTasks 批量创建子任务
	CreateSubTasks(ctx context.Context, subTasks []*do.CommentarySubTask, tx *gorm.DB) error

	// GetSubTaskById 根据ID获取子任务
	GetSubTaskById(ctx context.Context, id int64) (*do.CommentarySubTask, error)

	// GetSubTasksByMainTaskId 根据主任务ID获取子任务列表
	GetSubTasksByMainTaskId(ctx context.Context, mainTaskId int64) ([]*do.CommentarySubTask, error)

	// GetSubTasksByIds 根据ID列表获取子任务
	GetSubTasksByIds(ctx context.Context, ids []int64) ([]*do.CommentarySubTask, error)

	// UpdateSubTask 更新子任务
	UpdateSubTask(ctx context.Context, subTask *do.CommentarySubTask, tx ...*gorm.DB) error

	// UpdateSubTaskStatus 更新子任务状态
	UpdateSubTaskStatus(ctx context.Context, id int64, status int, errMsg string, tx ...*gorm.DB) error

	// UpdateSubTaskBgm 更新子任务BGM URL
	UpdateSubTaskBgm(ctx context.Context, id int64, bgmUrl string) (int64, error)

	// UpdateSubTaskEraseMode 更新子任务擦除模式
	UpdateSubTaskEraseMode(ctx context.Context, id int64, eraseMode int, eraseEdition int) (int64, error)

	// UpdateSubTaskBgmMode 更新子任务BGM模式
	UpdateSubTaskBgmMode(ctx context.Context, id int64, bgmMode int) (int64, error)

	// UpdateSubTaskOcrRectInfo 更新子任务OCR区域信息
	UpdateSubTaskOcrRectInfo(ctx context.Context, id int64, ocrRectInfo interface{}) (int64, error)

	// UpdateSubTaskBgmModeByMainTaskId 根据主任务ID更新所有子任务的BGM模式
	UpdateSubTaskBgmModeByMainTaskId(ctx context.Context, mainTaskId int64, bgmMode int) (int64, error)

	// UpdateSubTaskSubtitleMode 更新子任务字幕模式
	UpdateSubTaskSubtitleMode(ctx context.Context, subTaskId int64, subtitleMode int) (int64, error)

	// UpdateSubTaskPayOrderStatusByMainTaskId 根据主任务ID更新所有子任务的支付状态
	UpdateSubTaskPayOrderStatusByMainTaskId(ctx context.Context, mainTaskId int64, payOrderStatus consts.PayOrderStatus) (int64, error)

	// UpdateSubTasksPayOrderStatusAndDeductionOrder 批量更新子任务的支付状态和扣款订单ID
	UpdateSubTasksPayOrderStatusAndDeductionOrder(ctx context.Context, payOrderStatus consts.PayOrderStatus, subTaskStatus consts.CommentarySubTaskStatus, subTasks []*bo.CommentarySubTaskBO) (int64, error)

	// DeleteSubTask 删除子任务
	DeleteSubTask(ctx context.Context, id int64) error

	// UpdateSubTaskStatusAndAuditStatusByMainTaskId 根据主任务ID更新所有子任务的状态和审核状态
	UpdateSubTaskStatusAndAuditStatusByMainTaskId(ctx context.Context, mainTaskId int64, status consts.CommentarySubTaskStatus, auditStatus consts.AuditStatus, errMsg string) (int64, error)

	// UpdateSubTaskStatusByMainTaskId 根据主任务ID更新所有子任务的状态
	UpdateSubTaskStatusByMainTaskId(ctx context.Context, mainTaskId int64, status consts.CommentarySubTaskStatus, errMsg string) (int64, error)

	// UpdateSubTaskFields 通用更新
	UpdateSubTaskFields(ctx context.Context, id int64, fields map[string]interface{}, tx ...*gorm.DB) error

	// UpdateSubTaskCommentaryAgentRes 更新解说模型返回结果
	UpdateSubTaskCommentaryAgentRes(ctx context.Context, id int64, commentaryAgentRes *common.QueryCommentaryQiFeiRespData, tx ...*gorm.DB) error

	// UpdateSubTaskWithSelectFields 通用更新，只更新指定字段
	UpdateSubTaskWithSelectFields(ctx context.Context, m *do.CommentarySubTask, fields []string, tx ...*gorm.DB) error

	// MarkNeedMerge 标记子任务需要重新合成
	MarkNeedMerge(ctx context.Context, id int64, tx ...*gorm.DB) error

	// MarkMergeSuccess 标记子任务合成成功
	MarkMergeSuccess(ctx context.Context, id int64, mergeVersion int16, tx ...*gorm.DB) error
}
