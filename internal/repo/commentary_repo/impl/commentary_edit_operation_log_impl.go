package impl

import (
	"business-workflow/internal/common/db"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/repo/commentary_repo"
	"context"
	"fmt"

	"gorm.io/gorm"
)

type CommentaryEditOperationLogRepoImpl struct {
	db *gorm.DB
}

func NewCommentaryEditOperationLogRepoImpl() commentary_repo.ICommentaryEditOperationLogRepo {
	return &CommentaryEditOperationLogRepoImpl{db: db.GetDB()}
}

// CreateEditOperationLog 创建编辑操作流水记录
func (r *CommentaryEditOperationLogRepoImpl) CreateEditOperationLog(ctx context.Context, log *do.CommentaryEditOperationLog, tx ...*gorm.DB) error {
	if log == nil {
		return fmt.Errorf("编辑操作流水记录不能为空")
	}

	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	if err := db.WithContext(ctx).Create(log).Error; err != nil {
		return fmt.Errorf("创建编辑操作流水记录失败: %w", err)
	}
	return nil
}

// GetEditOperationLogsByMainTaskId 根据主任务ID获取编辑操作流水列表
func (r *CommentaryEditOperationLogRepoImpl) GetEditOperationLogsByMainTaskId(ctx context.Context, mainTaskId int64) ([]*do.CommentaryEditOperationLog, error) {
	var logs []*do.CommentaryEditOperationLog
	if err := r.db.WithContext(ctx).Where("main_task_id = ?", mainTaskId).Order("created_at DESC").Find(&logs).Error; err != nil {
		return nil, fmt.Errorf("根据主任务ID获取编辑操作流水失败: %w", err)
	}
	return logs, nil
}

// GetEditOperationLogsBySubTaskId 根据子任务ID获取编辑操作流水列表
func (r *CommentaryEditOperationLogRepoImpl) GetEditOperationLogsBySubTaskId(ctx context.Context, subTaskId int64) ([]*do.CommentaryEditOperationLog, error) {
	var logs []*do.CommentaryEditOperationLog
	if err := r.db.WithContext(ctx).Where("sub_task_id = ?", subTaskId).Order("created_at DESC").Find(&logs).Error; err != nil {
		return nil, fmt.Errorf("根据子任务ID获取编辑操作流水失败: %w", err)
	}
	return logs, nil
}

// GetEditOperationLogsByDeductOrderIds 根据扣费订单ID列表批量获取编辑操作流水
func (r *CommentaryEditOperationLogRepoImpl) GetEditOperationLogsByDeductOrderIds(ctx context.Context, deductOrderIds []string) ([]*do.CommentaryEditOperationLog, error) {
	if len(deductOrderIds) == 0 {
		return []*do.CommentaryEditOperationLog{}, nil
	}

	var logs []*do.CommentaryEditOperationLog
	if err := r.db.WithContext(ctx).Where("deduct_order_id IN ?", deductOrderIds).Order("created_at DESC").Find(&logs).Error; err != nil {
		return nil, fmt.Errorf("根据扣费订单ID列表批量获取编辑操作流水失败: %w", err)
	}
	return logs, nil
}
