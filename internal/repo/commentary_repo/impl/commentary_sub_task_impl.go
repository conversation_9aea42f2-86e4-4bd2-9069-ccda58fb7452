package impl

import (
	"business-workflow/internal/common/db"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/common"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/repo/commentary_repo"
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"

	"gorm.io/gorm"
)

type CommentarySubTaskRepoImpl struct {
	db *gorm.DB
}

func NewCommentarySubTaskRepoImpl() commentary_repo.ICommentarySubTaskRepo {
	return &CommentarySubTaskRepoImpl{db: db.GetDB()}
}

// CreateSubTasks 批量创建子任务
func (r *CommentarySubTaskRepoImpl) CreateSubTasks(ctx context.Context, subTasks []*do.CommentarySubTask, tx *gorm.DB) error {
	if len(subTasks) == 0 {
		return fmt.Errorf("子任务列表不能为空")
	}

	db := r.db
	if tx != nil {
		db = tx
	}

	if err := db.WithContext(ctx).CreateInBatches(subTasks, 100).Error; err != nil {
		return fmt.Errorf("批量创建子任务失败: %w", err)
	}
	return nil
}

// GetSubTaskById 根据ID获取子任务
func (r *CommentarySubTaskRepoImpl) GetSubTaskById(ctx context.Context, id int64) (*do.CommentarySubTask, error) {
	var subTask do.CommentarySubTask
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&subTask).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("获取子任务失败: %w", err)
	}
	return &subTask, nil
}

// GetSubTasksByMainTaskId 根据主任务ID获取子任务列表
func (r *CommentarySubTaskRepoImpl) GetSubTasksByMainTaskId(ctx context.Context, mainTaskId int64) ([]*do.CommentarySubTask, error) {
	var subTasks []*do.CommentarySubTask
	if err := r.db.WithContext(ctx).Where("main_task_id = ?", mainTaskId).Order("id ASC").Find(&subTasks).Error; err != nil {
		return nil, fmt.Errorf("根据主任务ID获取子任务列表失败: %w", err)
	}
	return subTasks, nil
}

// GetSubTasksByIds 根据ID列表获取子任务
func (r *CommentarySubTaskRepoImpl) GetSubTasksByIds(ctx context.Context, ids []int64) ([]*do.CommentarySubTask, error) {
	if len(ids) == 0 {
		return []*do.CommentarySubTask{}, nil
	}

	var subTasks []*do.CommentarySubTask
	if err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&subTasks).Error; err != nil {
		return nil, fmt.Errorf("根据ID列表获取子任务失败: %w", err)
	}

	return subTasks, nil
}

// UpdateSubTask 更新子任务
func (r *CommentarySubTaskRepoImpl) UpdateSubTask(ctx context.Context, subTask *do.CommentarySubTask, tx ...*gorm.DB) error {
	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}
	if err := db.WithContext(ctx).Save(subTask).Error; err != nil {
		return fmt.Errorf("更新子任务失败: %w", err)
	}
	return nil
}

// UpdateSubTaskStatus 更新子任务状态
func (r *CommentarySubTaskRepoImpl) UpdateSubTaskStatus(ctx context.Context, id int64, status int, errMsg string, tx ...*gorm.DB) error {
	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}
	updates := map[string]interface{}{
		"status": status,
	}
	if errMsg != "" {
		updates["err_msg"] = errMsg
	}

	if err := db.WithContext(ctx).Model(&do.CommentarySubTask{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新子任务状态失败: %w", err)
	}
	return nil
}

// UpdateSubTaskBgm 更新子任务BGM URL
func (r *CommentarySubTaskRepoImpl) UpdateSubTaskBgm(ctx context.Context, id int64, bgmUrl string) (int64, error) {
	updates := map[string]interface{}{
		"bgm_url": bgmUrl,
	}

	result := r.db.WithContext(ctx).Model(&do.CommentarySubTask{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return 0, fmt.Errorf("更新子任务BGM失败: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// UpdateSubTaskEraseMode 更新子任务擦除模式
func (r *CommentarySubTaskRepoImpl) UpdateSubTaskEraseMode(ctx context.Context, id int64, eraseMode int, eraseEdition int) (int64, error) {
	updates := map[string]interface{}{
		"erase_mode":    eraseMode,
		"erase_edition": eraseEdition,
	}

	result := r.db.WithContext(ctx).Model(&do.CommentarySubTask{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return 0, fmt.Errorf("更新子任务擦除模式失败: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// UpdateSubTaskBgmMode 更新子任务BGM模式
func (r *CommentarySubTaskRepoImpl) UpdateSubTaskBgmMode(ctx context.Context, id int64, bgmMode int) (int64, error) {
	updates := map[string]interface{}{
		"bgm_mode": bgmMode,
	}

	result := r.db.WithContext(ctx).Model(&do.CommentarySubTask{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return 0, fmt.Errorf("更新子任务BGM模式失败: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// UpdateSubTaskOcrRectInfo 更新子任务OCR区域信息
func (r *CommentarySubTaskRepoImpl) UpdateSubTaskOcrRectInfo(ctx context.Context, id int64, ocrRectInfo interface{}) (int64, error) {
	updates := map[string]interface{}{
		"ocr_rect_info": ocrRectInfo,
	}

	result := r.db.WithContext(ctx).Model(&do.CommentarySubTask{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return 0, fmt.Errorf("更新子任务OCR区域信息失败: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// UpdateSubTaskBgmModeByMainTaskId 根据主任务ID更新所有子任务的BGM模式
func (r *CommentarySubTaskRepoImpl) UpdateSubTaskBgmModeByMainTaskId(ctx context.Context, mainTaskId int64, bgmMode int) (int64, error) {
	updates := map[string]interface{}{
		"bgm_mode": bgmMode,
	}

	result := r.db.WithContext(ctx).Model(&do.CommentarySubTask{}).Where("main_task_id = ?", mainTaskId).Updates(updates)
	if result.Error != nil {
		return 0, fmt.Errorf("根据主任务ID更新子任务BGM模式失败: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// UpdateSubTaskSubtitleMode 更新子任务字幕模式
func (r *CommentarySubTaskRepoImpl) UpdateSubTaskSubtitleMode(ctx context.Context, subTaskId int64, subtitleMode int) (int64, error) {
	updates := map[string]interface{}{
		"subtitle_mode": subtitleMode,
	}

	result := r.db.WithContext(ctx).Model(&do.CommentarySubTask{}).Where("id = ?", subTaskId).Updates(updates)
	if result.Error != nil {
		return 0, fmt.Errorf("更新子任务字幕模式失败: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// UpdateSubTaskPayOrderStatusByMainTaskId 根据主任务ID更新所有子任务的支付状态
func (r *CommentarySubTaskRepoImpl) UpdateSubTaskPayOrderStatusByMainTaskId(ctx context.Context, mainTaskId int64, payOrderStatus consts.PayOrderStatus) (int64, error) {
	updates := map[string]interface{}{
		"pay_order_status": payOrderStatus,
	}

	result := r.db.WithContext(ctx).Model(&do.CommentarySubTask{}).Where("main_task_id = ?", mainTaskId).Updates(updates)
	if result.Error != nil {
		return 0, fmt.Errorf("根据主任务ID更新子任务支付状态失败: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// UpdateSubTasksPayOrderStatusAndDeductionOrder 批量更新子任务的支付状态和扣款订单ID
// 参数:
//   - ctx: 上下文
//   - payOrderStatus: 支付订单状态
//   - subTasks: 子任务BO列表，包含ID和PayOrderId信息
//
// 返回值:
//   - int64: 受影响的行数
//   - error: 错误信息
func (r *CommentarySubTaskRepoImpl) UpdateSubTasksPayOrderStatusAndDeductionOrder(ctx context.Context, payOrderStatus consts.PayOrderStatus, subTaskStatus consts.CommentarySubTaskStatus, subTasks []*bo.CommentarySubTaskBO) (int64, error) {
	if len(subTasks) == 0 {
		return 0, nil
	}

	// 使用事务确保数据一致性
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return 0, fmt.Errorf("开始事务失败: %w", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()

	var totalAffected int64

	// 逐个更新每个子任务，确保PayOrderId正确设置
	for _, subTask := range subTasks {
		result := tx.Model(&do.CommentarySubTask{}).
			Where("id = ?", subTask.Id).
			Updates(map[string]interface{}{
				"pay_order_status": payOrderStatus,
				"pay_order_id":     subTask.PayOrderId,
				"status":           subTaskStatus,
			})

		if result.Error != nil {
			tx.Rollback()
			return 0, fmt.Errorf("更新子任务ID %d失败: %w", subTask.Id, result.Error)
		}

		totalAffected += result.RowsAffected
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return 0, fmt.Errorf("提交事务失败: %w", err)
	}

	return totalAffected, nil
}

// UpdateSubTaskStatusAndAuditStatusByMainTaskId 根据主任务ID更新所有子任务的状态和审核状态
func (r *CommentarySubTaskRepoImpl) UpdateSubTaskStatusAndAuditStatusByMainTaskId(ctx context.Context, mainTaskId int64, status consts.CommentarySubTaskStatus, auditStatus consts.AuditStatus, errMsg string) (int64, error) {
	updates := map[string]interface{}{
		"status":       int(status),      // 确保类型转换
		"audit_status": int(auditStatus), // 确保类型转换
	}
	if errMsg != "" {
		updates["err_msg"] = errMsg
	}

	result := r.db.WithContext(ctx).Model(&do.CommentarySubTask{}).Where("main_task_id = ?", mainTaskId).Updates(updates)
	if result.Error != nil {
		g.Log().Errorf(ctx, "UpdateSubTaskStatusAndAuditStatusByMainTaskId failed, mainTaskId: %d, updates: %+v, error: %v", mainTaskId, updates, result.Error)
		return 0, fmt.Errorf("根据主任务ID更新子任务状态和审核状态失败: %w", result.Error)
	}

	return result.RowsAffected, nil
}

func (r *CommentarySubTaskRepoImpl) UpdateSubTaskStatusByMainTaskId(ctx context.Context, mainTaskId int64, status consts.CommentarySubTaskStatus, errMsg string) (int64, error) {
	updates := map[string]interface{}{
		"status": int(status),
	}
	if errMsg != "" {
		updates["err_msg"] = errMsg
	}

	result := r.db.WithContext(ctx).Model(&do.CommentarySubTask{}).Where("main_task_id = ?", mainTaskId).Updates(updates)
	if result.Error != nil {
		return 0, fmt.Errorf("根据主任务ID更新子任务状态失败: %w", result.Error)
	}
	return result.RowsAffected, nil
}

// DeleteSubTask 删除子任务
func (r *CommentarySubTaskRepoImpl) DeleteSubTask(ctx context.Context, id int64) error {
	if err := r.db.WithContext(ctx).Delete(&do.CommentarySubTask{}, id).Error; err != nil {
		return fmt.Errorf("删除子任务失败: %w", err)
	}
	return nil
}

func (r *CommentarySubTaskRepoImpl) UpdateSubTaskFields(ctx context.Context, id int64, fields map[string]interface{}, tx ...*gorm.DB) error {
	if len(fields) == 0 {
		return fmt.Errorf("更新字段不能为空")
	}

	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	if err := db.WithContext(ctx).Model(&do.CommentarySubTask{}).Where("id = ?", id).Updates(fields).Error; err != nil {
		return fmt.Errorf("更新子任务字段失败: %w", err)
	}
	return nil
}

func (r *CommentarySubTaskRepoImpl) UpdateSubTaskWithSelectFields(ctx context.Context, m *do.CommentarySubTask, fields []string, tx ...*gorm.DB) error {
	if len(fields) == 0 {
		return fmt.Errorf("更新字段不能为空")
	}

	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	// 更新时间戳
	m.UpdatedAt = time.Now()

	// 确保更新时间字段在字段列表中
	fieldsWithTime := append(fields, "updated_at")

	if err := db.WithContext(ctx).Model(m).Select(fieldsWithTime).Omit("id").Updates(m).Error; err != nil {
		return fmt.Errorf("更新子任务字段失败: %w", err)
	}
	return nil
}

// UpdateSubTaskCommentaryAgentRes 更新解说模型返回结果
func (r *CommentarySubTaskRepoImpl) UpdateSubTaskCommentaryAgentRes(ctx context.Context, id int64, commentaryAgentRes *common.QueryCommentaryQiFeiRespData, tx ...*gorm.DB) error {
	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	// 创建一个临时的子任务对象，只更新 commentary_agent_res 字段
	subTask := &do.CommentarySubTask{
		Id:                 id,
		CommentaryAgentRes: commentaryAgentRes,
	}

	if err := db.WithContext(ctx).Model(subTask).Select("commentary_agent_res").Where("id = ?", id).Updates(subTask).Error; err != nil {
		return fmt.Errorf("更新子任务解说模型返回结果失败: %w", err)
	}
	return nil
}

func (r *CommentarySubTaskRepoImpl) MarkNeedMerge(ctx context.Context, id int64, tx ...*gorm.DB) error {
	// 修改 need_merge =1  最新版本号 + 1
	return r.UpdateSubTaskFields(ctx, id, map[string]interface{}{"need_merge": 1, "latest_version": gorm.Expr("latest_version + 1")}, tx...)
}

func (r *CommentarySubTaskRepoImpl) MarkMergeSuccess(ctx context.Context, id int64, mergeVersion int16, tx ...*gorm.DB) error {

	return r.UpdateSubTaskFields(ctx, id, map[string]interface{}{"need_merge": 0, "merged_version": mergeVersion, "merge_status": consts.MergeStatusCompleted}, tx...)
}
