package commentary_repo

import (
	"business-workflow/internal/entity/do"
	"context"

	"gorm.io/gorm"
)

// ICommentaryEditOperationLogRepo 解说编辑操作流水仓储接口
type ICommentaryEditOperationLogRepo interface {
	// CreateEditOperationLog 创建编辑操作流水记录
	CreateEditOperationLog(ctx context.Context, log *do.CommentaryEditOperationLog, tx ...*gorm.DB) error

	// GetEditOperationLogsByMainTaskId 根据主任务ID获取编辑操作流水列表
	GetEditOperationLogsByMainTaskId(ctx context.Context, mainTaskId int64) ([]*do.CommentaryEditOperationLog, error)

	// GetEditOperationLogsBySubTaskId 根据子任务ID获取编辑操作流水列表
	GetEditOperationLogsBySubTaskId(ctx context.Context, subTaskId int64) ([]*do.CommentaryEditOperationLog, error)

	// GetEditOperationLogsByDeductOrderIds 根据扣费订单ID列表批量获取编辑操作流水
	GetEditOperationLogsByDeductOrderIds(ctx context.Context, deductOrderIds []string) ([]*do.CommentaryEditOperationLog, error)
}
