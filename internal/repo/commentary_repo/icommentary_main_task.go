package commentary_repo

import (
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/do"
	"context"
	"time"

	"gorm.io/gorm"
)

type UpdateTaskStatusExtraParams struct {
	FstDoneStatus *consts.CommentaryMainTaskStatus
	FstDoneTime   *time.Time
}

// ICommentaryMainTaskRepo 解说主任务仓储接口
type ICommentaryMainTaskRepo interface {
	// CreateTask 创建解说主任务
	CreateTask(ctx context.Context, task *do.CommentaryMainTask, tx ...*gorm.DB) error

	// GetTaskById 根据ID获取任务
	GetTaskById(ctx context.Context, id int64) (*do.CommentaryMainTask, error)

	// GetTasksByTenantId 根据租户ID获取任务列表
	GetTasksByTenantId(ctx context.Context, tenantId int64, page, pageSize int) ([]*do.CommentaryMainTask, error)

	// GetTasksByIds 根据ID列表获取任务
	GetTasksByIds(ctx context.Context, ids []int64) ([]*do.CommentaryMainTask, error)

	// GetTasksByOrderIds 根据订单ID列表获取任务
	GetTasksByOrderIds(ctx context.Context, orderIds []string) ([]*do.CommentaryMainTask, error)

	// UpdateTaskStatus 更新任务状态
	UpdateTaskStatus(ctx context.Context, id int64, status consts.CommentaryMainTaskStatus, errMsg string, extraParams *UpdateTaskStatusExtraParams) error

	// DeleteTask 删除任务
	DeleteTask(ctx context.Context, id int64) error

	// 根据条件筛选任务
	GetTasksByCond(ctx context.Context, filterFields map[string][]interface{}, page, pageSize int) ([]*do.CommentaryMainTask, error)

	// UpdateTaskFields 通用更新
	UpdateTaskFields(ctx context.Context, id int64, fields map[string]interface{}, tx ...*gorm.DB) error
}
