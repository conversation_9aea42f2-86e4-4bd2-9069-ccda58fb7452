package temporal

import (
	"business-workflow/internal/activities/commentary/audit"
	"business-workflow/internal/activities/commentary/batch_download"
	"business-workflow/internal/activities/commentary/commentary"
	"business-workflow/internal/activities/commentary/concat"
	"business-workflow/internal/activities/commentary/deduction"
	"business-workflow/internal/activities/commentary/erase"
	"business-workflow/internal/activities/commentary/finalize"
	"business-workflow/internal/activities/commentary/merge"
	"business-workflow/internal/activities/commentary/ocr"
	posterase "business-workflow/internal/activities/commentary/post_erase"
	preerase "business-workflow/internal/activities/commentary/pre_erase"
	"business-workflow/internal/activities/commentary/refund"
	"business-workflow/internal/activities/commentary/subtitle_audit"
	subtitlegenerate "business-workflow/internal/activities/commentary/subtitle_generate"
	texttranslate "business-workflow/internal/activities/commentary/text_translate"
	"business-workflow/internal/activities/commentary/transcoding"
	"business-workflow/internal/activities/commentary/trimclip"
	"business-workflow/internal/activities/commentary/tts"
	"business-workflow/internal/common/config"
	"business-workflow/internal/workflows"
	"log"

	"go.temporal.io/sdk/client"
	"go.temporal.io/sdk/worker"
)

var (
	TemporalClient client.Client
)

func InitTemporal() {
	// 1. 创建 Temporal 客户端
	var err error
	TemporalClient, err = client.Dial(client.Options{
		HostPort:  config.GetConfig().Temporal.HostPort, // 从配置读取
		Namespace: "default",
	})
	if err != nil {
		log.Fatal("Failed to create Temporal client", err)
	}

	// 2. 注册 Worker
	registerWorker()
}

func registerWorker() {
	// 创建 Worker
	w := worker.New(TemporalClient, config.GetConfig().Temporal.Queue, worker.Options{})

	// 注册 Workflow
	w.RegisterWorkflow(workflows.CommentaryWorkflow)
	w.RegisterWorkflow(workflows.GenerateVoiceWorkflow)
	w.RegisterWorkflow(workflows.TextTranslateWorkflow)
	w.RegisterWorkflow(workflows.VideoBatchMergeWorkflow)
	w.RegisterActivity(finalize.FinalizeProcess)
	w.RegisterActivity(audit.AuditProcess)
	w.RegisterActivity(transcoding.TransCodingProcess)
	w.RegisterActivity(commentary.CommentaryProcess)
	w.RegisterActivity(subtitle_audit.SubtitleAuditProcess)
	w.RegisterActivity(subtitle_audit.SubtitleAuditForMerge)
	w.RegisterActivity(tts.TTSProcess)
	w.RegisterActivity(trimclip.TrimClipProcess)
	w.RegisterActivity(subtitlegenerate.SubtitleGenerateProcess)
	w.RegisterActivity(ocr.OCRProcess)
	w.RegisterActivity(preerase.PreEraseProcess)
	w.RegisterActivity(erase.EraseProcess)
	w.RegisterActivity(posterase.PostEraseProcess)
	w.RegisterActivity(merge.MergeBeforeProcess)
	w.RegisterActivity(merge.MergeProcess)
	w.RegisterActivity(concat.ConcatProcess)
	w.RegisterActivity(texttranslate.TextTranslateProcess)
	w.RegisterActivity(deduction.DeductionProcess)
	w.RegisterActivity(deduction.VideoBatchSynthesisDeduction)
	w.RegisterActivity(refund.RefundProcess)
	w.RegisterActivity(refund.VideoBatchSynthesisRefund)
	w.RegisterActivity(tts.TTSDeductProcess)
	w.RegisterActivity(tts.TTSCleanUpProcess)
	w.RegisterActivity(texttranslate.TextTranslateDeductProcess)
	w.RegisterActivity(texttranslate.TextTranslatCleanUpProcess)
	w.RegisterActivity(merge.MergeAfterProcess)
	w.RegisterActivity(batch_download.BatchDownloadStartProcess)
	w.RegisterActivity(batch_download.BatchDownloadEndProcess)
	w.RegisterActivity(batch_download.BatchDownloadErrorProcess)
	w.RegisterActivity(audit.BgmAuditProcess)
	w.RegisterWorkflow(workflows.MockInpaint)
	w.RegisterWorkflow(workflows.MockSubtitleMerge)

	// 启动 Worker
	go func() {
		err := w.Run(worker.InterruptCh())
		if err != nil {
			log.Fatal("Failed to start worker", err)
		}
	}()
}
