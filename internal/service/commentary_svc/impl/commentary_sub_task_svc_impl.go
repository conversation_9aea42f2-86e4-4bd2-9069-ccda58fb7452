package impl

import (
	"business-workflow/internal/common/config"
	"business-workflow/internal/common/db"
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/common/uerrors"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/processor/processor_util"
	"business-workflow/internal/repo/commentary_repo"
	"business-workflow/internal/repo/commentary_repo/impl"
	"business-workflow/internal/service/commentary_svc"
	"business-workflow/internal/temporal"
	"business-workflow/internal/util/id_generator"
	"business-workflow/internal/util/media"
	"business-workflow/internal/util/obs"
	"context"
	"fmt"
	"net/url"
	"path/filepath"
	"runtime/debug"
	"strconv"
	"strings"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"go.temporal.io/sdk/client"

	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/concurrent"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/utils/oss_util"
	omniBalance "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_balance"

	"gorm.io/gorm"
)

type CommentarySubTaskServiceImpl struct {
	commentarySubTaskRepo      commentary_repo.ICommentarySubTaskRepo
	commentarySubtitleItemRepo commentary_repo.ICommentarySubtitleItemRepo
	mainTaskRepo               commentary_repo.ICommentaryMainTaskRepo
}

func NewCommentarySubTaskServiceImpl(commentarySubTaskRepo commentary_repo.ICommentarySubTaskRepo, commentarySubtitleItemRepo commentary_repo.ICommentarySubtitleItemRepo, mainTaskRepo commentary_repo.ICommentaryMainTaskRepo) commentary_svc.ICommentarySubTaskService {
	return &CommentarySubTaskServiceImpl{
		commentarySubTaskRepo:      commentarySubTaskRepo,
		commentarySubtitleItemRepo: commentarySubtitleItemRepo,
		mainTaskRepo:               mainTaskRepo,
	}
}

// GetSubTasksByMainTaskId 根据主任务ID获取子任务列表
func (s *CommentarySubTaskServiceImpl) GetSubTasksByMainTaskId(ctx context.Context, mainTaskId int64) ([]*bo.CommentarySubTaskBO, error) {
	if mainTaskId <= 0 {
		g.Log().Errorf(ctx, "GetSubTasksByMainTaskId, mainTaskId: %d", mainTaskId)
		return nil, fmt.Errorf("主任务ID无效")
	}

	subTasksDO, err := s.commentarySubTaskRepo.GetSubTasksByMainTaskId(ctx, mainTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "GetSubTasksByMainTaskId, mainTaskId: %d, err: %v", mainTaskId, err)
		return nil, fmt.Errorf("查询子任务列表失败: %w", err)
	}

	// 转换 DO 为 BO
	subTasksBOs := make([]*bo.CommentarySubTaskBO, len(subTasksDO))
	for i, subTaskDO := range subTasksDO {
		subTasksBOs[i] = conv.CommentarySubTaskDOToBO(subTaskDO)
	}

	g.Log().Infof(ctx, "GetSubTasksByMainTaskId, 数量: %d", len(subTasksBOs))
	return subTasksBOs, nil
}

// GetSubTaskById 根据ID获取子任务详情
func (s *CommentarySubTaskServiceImpl) GetSubTaskById(ctx context.Context, id int64) (*bo.CommentarySubTaskBO, error) {
	g.Log().Infof(ctx, "GetSubTaskById, id: %d", id)
	if id <= 0 {
		g.Log().Errorf(ctx, "GetSubTaskById, id: %d", id)
		return nil, fmt.Errorf("子任务ID无效")
	}

	subTaskDO, err := s.commentarySubTaskRepo.GetSubTaskById(ctx, id)
	if err != nil {
		g.Log().Errorf(ctx, "GetSubTaskById, id: %d, err: %v", id, err)
		return nil, fmt.Errorf("查询子任务失败: %w", err)
	}

	if subTaskDO == nil {
		g.Log().Errorf(ctx, "GetSubTaskById, id: %d, subTaskDO is nil", id)
		return nil, fmt.Errorf("子任务不存在")
	}

	// 转换 DO 为 BO
	subTaskBO := conv.CommentarySubTaskDOToBO(subTaskDO)
	g.Log().Infof(ctx, "GetSubTaskById, id: %d, subTaskBO: %v", id, subTaskBO)
	return subTaskBO, nil
}

func (s *CommentarySubTaskServiceImpl) GetSubTaskListByIds(ctx context.Context, ids []int64) ([]*bo.CommentarySubTaskBO, error) {
	if len(ids) <= 0 {
		return nil, fmt.Errorf("子任务ID无效")
	}

	// 获取子任务详情
	subTaskDOs, err := s.commentarySubTaskRepo.GetSubTasksByIds(ctx, ids)
	if err != nil {
		return nil, fmt.Errorf("查询子任务失败: %w", err)
	}

	if subTaskDOs == nil {
		return nil, fmt.Errorf("子任务不存在")
	}
	res := []*bo.CommentarySubTaskBO{}
	for _, subTaskDO := range subTaskDOs {
		subTaskBO := conv.CommentarySubTaskDOToBO(subTaskDO)
		res = append(res, subTaskBO)
	}

	return res, nil
}

// GetSubTaskWithSubtitles 根据ID获取子任务详情及字幕项列表
func (s *CommentarySubTaskServiceImpl) GetSubTaskWithSubtitles(ctx context.Context, id int64) (*bo.CommentarySubTaskBO, error) {
	defer func() {
		if err := recover(); err != nil {
			g.Log().Errorf(ctx, "GetSubTaskWithSubtitles panic, err: %v, stack: %s", err, string(debug.Stack()))
		}
	}()
	g.Log().Infof(ctx, "GetSubTaskWithSubtitles start, id: %d", id)
	// 获取子任务详情
	subTaskDO, err := s.commentarySubTaskRepo.GetSubTaskById(ctx, id)
	if err != nil {
		g.Log().Errorf(ctx, "查询子任务失败, id: %d, err: %v", id, err)
		return nil, fmt.Errorf("查询子任务失败: %w", err)
	}

	if subTaskDO == nil {
		g.Log().Errorf(ctx, "子任务不存在, id: %d", id)
		return nil, fmt.Errorf("子任务不存在")
	}

	g.Log().Infof(ctx, "GetSubTaskWithSubtitles, subTaskDO start: %v", subTaskDO)
	subTaskBO := conv.CommentarySubTaskDOToBO(subTaskDO)
	g.Log().Infof(ctx, "GetSubTaskWithSubtitles, subTaskBO: %v", subTaskBO)
	// 获取字幕项列表
	subtitleItems, err := s.commentarySubtitleItemRepo.GetSubtitleItemsBySubTaskId(ctx, subTaskDO.Id)
	if err != nil {
		g.Log().Errorf(ctx, "查询字幕项列表失败, subTaskId: %d, err: %v", subTaskDO.Id, err)
		return nil, fmt.Errorf("查询字幕项列表失败: %w", err)
	}
	g.Log().Infof(ctx, "GetSubTaskWithSubtitles, GetSubtitleItemsBySubTaskId end: %v", subtitleItems)
	for _, item := range subtitleItems {
		subTaskBO.SubtitleItemList = append(subTaskBO.SubtitleItemList, conv.CommentarySubtitleItemDOToBO(item))
	}
	g.Log().Infof(ctx, "GetSubTaskWithSubtitles, CommentarySubtitleItemDOToBO end: %v", subTaskBO)
	return subTaskBO, nil
}

// UpdateSubTaskStatus 更新子任务状态
func (s *CommentarySubTaskServiceImpl) UpdateSubTaskStatus(ctx context.Context, req *bo.UpdateSubTaskStatusReqBO) (*bo.UpdateSubTaskStatusResBO, error) {
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	if req.SubTaskId <= 0 {
		return nil, fmt.Errorf("子任务ID无效")
	}

	if err := s.commentarySubTaskRepo.UpdateSubTaskStatus(ctx, req.SubTaskId, req.Status, req.ErrMsg); err != nil {
		return nil, fmt.Errorf("更新子任务状态失败: %w", err)
	}

	g.Log().Infof(ctx, "更新子任务状态成功, ID: %d, 状态: %d", req.SubTaskId, req.Status)
	return &bo.UpdateSubTaskStatusResBO{Success: true}, nil
}

// DeleteSubTask 删除子任务
func (s *CommentarySubTaskServiceImpl) DeleteSubTask(ctx context.Context, req *bo.DeleteSubTaskReqBO) (*bo.DeleteSubTaskResBO, error) {
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	if req.SubTaskId <= 0 {
		return nil, fmt.Errorf("子任务ID无效")
	}

	if err := s.commentarySubTaskRepo.DeleteSubTask(ctx, req.SubTaskId); err != nil {
		return nil, fmt.Errorf("删除子任务失败: %w", err)
	}

	g.Log().Infof(ctx, "删除子任务成功, ID: %d", req.SubTaskId)
	return &bo.DeleteSubTaskResBO{Success: true}, nil
}

func (s *CommentarySubTaskServiceImpl) UpdateSubTaskBgm(ctx context.Context, req *bo.UpdateSubTaskBgmReqBO) (*bo.UpdateSubTaskBgmResBO, error) {
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	if req.SubTaskId <= 0 {
		return nil, fmt.Errorf("子任务ID无效")
	}

	if req.BgmUrl == "" {
		return nil, fmt.Errorf("BGM URL不能为空")
	}

	bgmUrl := obs.GetCdnUrlByObjectName(req.BgmUrl)
	// 调用 repository 更新BGM URL
	rowsAffected, err := s.commentarySubTaskRepo.UpdateSubTaskBgm(ctx, req.SubTaskId, bgmUrl)
	if err != nil {
		return nil, fmt.Errorf("更新子任务BGM失败: %w", err)
	}

	if rowsAffected == 0 {
		return nil, fmt.Errorf("子任务不存在或BGM URL未发生变化")
	}
	concurrent.GoSafe(func() {
		err := s.MarkNeedMerge(ctx, req.SubTaskId)
		if err != nil {
			return
		}
	})

	g.Log().Infof(ctx, "更新子任务BGM成功, ID: %d, BGM URL: %s, 影响行数: %d", req.SubTaskId, req.BgmUrl, rowsAffected)
	return &bo.UpdateSubTaskBgmResBO{Success: true}, nil
}

// UpdateSubTaskEraseMode 更新子任务擦除模式
func (s *CommentarySubTaskServiceImpl) UpdateSubTaskEraseMode(ctx context.Context, req *bo.UpdateSubTaskEraseModeReqBO) (*bo.UpdateSubTaskEraseModeResBO, error) {
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	if req.SubTaskId <= 0 {
		return nil, fmt.Errorf("子任务ID无效")
	}

	// 调用 repository 更新擦除模式
	rowsAffected, err := s.commentarySubTaskRepo.UpdateSubTaskEraseMode(ctx, req.SubTaskId, req.EraseMode, req.EraseEdition)
	if err != nil {
		return nil, fmt.Errorf("更新子任务擦除模式失败: %w", err)
	}

	if rowsAffected == 0 {
		return nil, fmt.Errorf("子任务不存在或擦除模式未发生变化")
	}

	g.Log().Infof(ctx, "更新子任务擦除模式成功, ID: %d, 擦除模式: %d, 擦除版本: %d, 影响行数: %d", req.SubTaskId, req.EraseMode, req.EraseEdition, rowsAffected)
	return &bo.UpdateSubTaskEraseModeResBO{Success: true}, nil
}

// UpdateSubTaskBgmMode 更新子任务BGM模式
func (s *CommentarySubTaskServiceImpl) UpdateSubTaskBgmMode(ctx context.Context, req *bo.UpdateSubTaskBgmModeReqBO) (*bo.UpdateSubTaskBgmModeResBO, error) {
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	if req.SubTaskId <= 0 {
		return nil, fmt.Errorf("子任务ID无效")
	}

	// 调用 repository 更新BGM模式
	rowsAffected, err := s.commentarySubTaskRepo.UpdateSubTaskBgmMode(ctx, req.SubTaskId, req.BgmMode)
	if err != nil {
		return nil, fmt.Errorf("更新子任务BGM模式失败: %w", err)
	}

	if rowsAffected == 0 {
		return nil, fmt.Errorf("子任务不存在或BGM模式未发生变化")
	}
	err = s.MarkNeedMerge(ctx, req.SubTaskId)

	g.Log().Infof(ctx, "更新子任务BGM模式成功, ID: %d, BGM模式: %d, 影响行数: %d", req.SubTaskId, req.BgmMode, rowsAffected)
	return &bo.UpdateSubTaskBgmModeResBO{Success: true}, nil
}

// UpdateSubTaskOcrRectInfo 更新子任务OCR区域信息
func (s *CommentarySubTaskServiceImpl) UpdateSubTaskOcrRectInfo(ctx context.Context, req *bo.UpdateSubTaskOcrRectInfoReqBO) (*bo.UpdateSubTaskOcrRectInfoResBO, error) {
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	if req.SubTaskId <= 0 {
		return nil, fmt.Errorf("子任务ID无效")
	}

	if req.OcrRectInfo == nil {
		return nil, fmt.Errorf("OCR区域信息不能为空")
	}

	// 调用 repository 更新OCR区域信息
	rowsAffected, err := s.commentarySubTaskRepo.UpdateSubTaskOcrRectInfo(ctx, req.SubTaskId, req.OcrRectInfo)
	if err != nil {
		return nil, fmt.Errorf("更新子任务OCR区域信息失败: %w", err)
	}

	if rowsAffected == 0 {
		return nil, fmt.Errorf("子任务不存在或OCR区域信息未发生变化")
	}

	g.Log().Infof(ctx, "更新子任务OCR区域信息成功, ID: %d, 影响行数: %d", req.SubTaskId, rowsAffected)
	return &bo.UpdateSubTaskOcrRectInfoResBO{Success: true}, nil
}

// UpdateSubTaskBgmModeByMainTaskId 根据主任务ID更新所有子任务的BGM模式
func (s *CommentarySubTaskServiceImpl) UpdateSubTaskBgmModeByMainTaskId(ctx context.Context, req *bo.UpdateSubTaskBgmModeByMainTaskIdReqBO) (*bo.UpdateSubTaskBgmModeByMainTaskIdResBO, error) {
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	if req.MainTaskId <= 0 {
		return nil, fmt.Errorf("主任务ID无效")
	}

	// 调用 repository 根据主任务ID更新BGM模式
	rowsAffected, err := s.commentarySubTaskRepo.UpdateSubTaskBgmModeByMainTaskId(ctx, req.MainTaskId, req.BgmMode)
	if err != nil {
		return nil, fmt.Errorf("根据主任务ID更新子任务BGM模式失败: %w", err)
	}

	if rowsAffected == 0 {
		return nil, fmt.Errorf("没有找到相关子任务或BGM模式未发生变化")
	}

	g.Log().Infof(ctx, "根据主任务ID更新子任务BGM模式成功, 主任务ID: %d, BGM模式: %d, 影响行数: %d", req.MainTaskId, req.BgmMode, rowsAffected)
	return &bo.UpdateSubTaskBgmModeByMainTaskIdResBO{Success: true}, nil
}

func (s *CommentarySubTaskServiceImpl) MarkNeedMerge(ctx context.Context, id int64) error {
	err := s.commentarySubTaskRepo.MarkNeedMerge(ctx, id)
	if err != nil {
		g.Log().Errorf(ctx, "mark need merge failed, err: %v", err)
		return err
	}
	return nil
}

func (s *CommentarySubTaskServiceImpl) SaveGlobalSubtitleStyle(ctx context.Context, req *bo.SaveGlobalSubtitleStyleReqBO) (*bo.SaveGlobalSubtitleStyleResBO, error) {
	g.Log().Infof(ctx, "SaveGlobalSubtitleStyle start, req: %+v", req)

	// 获取子任务信息
	subTask, err := s.commentarySubTaskRepo.GetSubTaskById(ctx, req.SubTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "SaveGlobalSubtitleStyle failed, GetSubTaskById error, err: %v, req: %v", err, req)
		return nil, uerrors.ErrBusy
	}
	if subTask == nil {
		g.Log().Errorf(ctx, "SaveGlobalSubtitleStyle failed, subTask not found, req: %v", req)
		return nil, uerrors.ErrSubtitleItemNoExist
	}
	subTask.CustomSubtitleStyle = req.CustomSubtitleStyle
	// 保存到数据库
	err = s.commentarySubTaskRepo.UpdateSubTaskWithSelectFields(ctx, subTask, []string{"custom_subtitle_style"})
	if err != nil {
		g.Log().Errorf(ctx, "SaveGlobalSubtitleStyle failed, UpdateSubTaskFields error, err: %v, req: %v", err, req)
		return nil, uerrors.ErrBusy
	}

	// 在事务中获取要修改的字幕项
	itemIds := make([]int64, 0, len(req.Subtitles))

	for _, val := range req.Subtitles {
		if val.ItemId <= 0 {
			g.Log().Errorf(ctx, "SaveGlobalSubtitleStyle failed, ItemId is 0, val: %v", val)
			return nil, uerrors.ErrParam
		}
		itemIds = append(itemIds, val.ItemId)
	}
	g.Log().Infof(ctx, "SaveGlobalSubtitleStyle GetTranslateSubtitleItemByIds start")
	subtitleItemList, err := s.commentarySubtitleItemRepo.GetSubtitleItemsByIdsWithSelectFields(ctx, req.SubTaskId, itemIds, []string{"id", "sub_item_list"})
	if err != nil {
		g.Log().Errorf(ctx, "GetTranslateSubtitleItemByIds failed in transaction, err: %v, req: %+v", err, req)
		return nil, uerrors.ErrBusy
	}
	g.Log().Infof(ctx, "SaveGlobalSubtitleStyle GetTranslateSubtitleItemByIds  end subtitleItemList len: %d", len(subtitleItemList))
	itemEntityMap := make(map[int64]*do.CommentarySubtitleItem)
	for _, item := range subtitleItemList {
		itemEntityMap[item.Id] = item
	}
	modifyItemList := make([]*do.CommentarySubtitleItem, 0, 0)
	for _, itemBO := range req.Subtitles {
		itemEntity := itemEntityMap[itemBO.ItemId]
		if itemEntity == nil {
			g.Log().Errorf(ctx, "itemEntity is nil,itemBO.ItemId:%d", itemBO.ItemId)
			continue
		}
		modifyItemList = append(modifyItemList, &do.CommentarySubtitleItem{
			Id:          itemEntity.Id,
			SubItemList: itemBO.SubItemList,
		})
	}

	g.Log().Infof(ctx, "SaveGlobalSubtitleStyle UpdateTranslateSubtitleItemsWithCase start")
	err = s.commentarySubtitleItemRepo.UpdateSubtitleItemsWithCase(ctx, modifyItemList, []string{"sub_item_list"}, 1000)
	if err != nil {
		g.Log().Errorf(ctx, "UpdateTranslateSubtitleItemsWithCase failed in transaction, err: %v, req: %+v", err, req)
		return nil, uerrors.ErrBusy
	}
	g.Log().Infof(ctx, "SaveGlobalSubtitleStyle UpdateTranslateSubtitleItemsWithCase end")

	// 标记子任务需要重新合成
	err = s.MarkNeedMerge(ctx, req.SubTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "SaveGlobalSubtitleStyle failed, MarkNeedMerge error, err: %v, subTaskId: %v", err, req.SubTaskId)
		// 这里不返回错误，因为主要操作已经成功，只是标记合成失败
	}

	g.Log().Infof(ctx, "SaveGlobalSubtitleStyle success, subTaskId: %v", req.SubTaskId)

	// 构建响应结果
	resp := &bo.SaveGlobalSubtitleStyleResBO{}
	return resp, nil
}

// SaveSubtitleMode 保存字幕模式
func (s *CommentarySubTaskServiceImpl) SaveSubtitleMode(ctx context.Context, req *bo.SaveSubtitleModeReqBO) (*bo.SaveSubtitleModeResBO, error) {
	g.Log().Infof(ctx, "SaveSubtitleMode start, req: %+v", req)

	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	if req.SubTaskId <= 0 {
		return nil, fmt.Errorf("子任务ID无效")
	}

	// 验证字幕模式参数
	if req.SubtitleMode != 0 && req.SubtitleMode != 1 {
		return nil, fmt.Errorf("字幕模式参数无效，只能是0(开启)或1(关闭)")
	}

	// 调用 repository 更新子任务字幕模式
	rowsAffected, err := s.commentarySubTaskRepo.UpdateSubTaskSubtitleMode(ctx, req.SubTaskId, int(req.SubtitleMode))
	if err != nil {
		g.Log().Errorf(ctx, "SaveSubtitleMode failed, UpdateSubTaskSubtitleMode error, err: %v, req: %v", err, req)
		return nil, fmt.Errorf("更新子任务字幕模式失败: %w", err)
	}
	if rowsAffected == 0 {
		return nil, fmt.Errorf("子任务不存在或字幕模式未发生变化")
	}
	err = s.MarkNeedMerge(ctx, req.SubTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "SaveSubtitleMode failed, MarkNeedMerge error, err: %v, req: %v", err, req)
	}

	g.Log().Infof(ctx, "SaveSubtitleMode success, 子任务ID: %d, 字幕模式: %d, 影响行数: %d", req.SubTaskId, req.SubtitleMode, rowsAffected)
	return &bo.SaveSubtitleModeResBO{Success: true}, nil
}

func (s *CommentarySubTaskServiceImpl) RenameSubtitle(ctx context.Context, id int64, newName string) (*bo.CommentarySubTaskBO, error) {
	err := s.commentarySubTaskRepo.UpdateSubTaskFields(ctx, id, map[string]interface{}{"name": newName})
	if err != nil {
		g.Log().Errorf(ctx, "RenameSubtitle failed, err: %v", err)
		return nil, err
	}
	return &bo.CommentarySubTaskBO{}, nil
}

// checkBalanceSufficient 检查余额是否充足
// 通过调用 AVoiceBatchSynthesisEstimateCostRequest 计算合成所需费用，再根据当前余额判断是否足够
func (s *CommentarySubTaskServiceImpl) checkBalanceSufficient(ctx context.Context, appId, tenantId int64, businessType int32, subTaskBOs []*bo.CommentarySubTaskBO) (bool, error) {
	// 1. 构建任务信息列表
	taskInfoList := make([]*omniBalance.TaskInfo, 0, len(subTaskBOs))
	for _, subTask := range subTaskBOs {
		taskInfo := &omniBalance.TaskInfo{
			TaskId:     strconv.FormatInt(subTask.Id, 10),
			Duration:   int64(subTask.VideoDuration), // 使用 VideoDuration 字段，单位为秒
			PayOrderId: fmt.Sprintf("subTask_%d", subTask.Id),
		}
		taskInfoList = append(taskInfoList, taskInfo)
	}

	// 2. 调用价格预估接口
	balanceClient := omni_engine.GetOmniBalanceAVLBalance()
	if balanceClient == nil {
		g.Log().Errorf(ctx, "[checkBalanceSufficient] balance client is nil")
		return false, fmt.Errorf("balance client is not initialized")
	}

	estimateReq := &omniBalance.AVoiceBatchSynthesisEstimateCostRequest{
		AppId:            appId,
		TenantId:         tenantId,
		BusinessType:     businessType,
		TaskDurationList: taskInfoList,
	}

	estimateResp, err := balanceClient.AVoiceBatchSynthesisEstimateCost(ctx, estimateReq)
	if err != nil {
		g.Log().Errorf(ctx, "[checkBalanceSufficient] 调用价格预估接口失败: %v", err)
		return false, fmt.Errorf("调用价格预估接口失败: %w", err)
	}

	// 3. 计算总预估费用
	var totalEstimatedCost int64
	for _, freeInfo := range estimateResp.FreeRemainingSynthInfo {
		// 如果有免费次数，则不需要扣费
		if freeInfo.FreeRmainingSynth > 0 {
			continue
		}
		// 累加需要扣费的任务费用
		totalEstimatedCost += freeInfo.CreditsCost
	}

	// 4. 获取当前余额
	balanceReq := &omniBalance.AVoiceGetTenantBalanceRequest{
		AppId:       appId,
		TenantId:    tenantId,
		BalanceType: 1, // 积分类型
	}

	balanceResp, err := balanceClient.AVoiceGetTenantBalance(ctx, balanceReq)
	if err != nil {
		g.Log().Errorf(ctx, "[checkBalanceSufficient] 调用余额查询接口失败: %v", err)
		return false, fmt.Errorf("调用余额查询接口失败: %w", err)
	}

	// 5. 计算总余额
	var totalBalance int64
	for _, balance := range balanceResp.TenantBalanceList {
		totalBalance += balance.Balance
	}

	// 6. 判断余额是否充足
	isBalanceSufficient := totalBalance >= totalEstimatedCost

	g.Log().Infof(ctx, "[checkBalanceSufficient] 余额检查完成，总余额: %d, 预估费用: %d, 余额充足: %v",
		totalBalance, totalEstimatedCost, isBalanceSufficient)

	return isBalanceSufficient, nil
}

func (s *CommentarySubTaskServiceImpl) MergeBatch(ctx context.Context, appId int64, tenantId int64, requestId string, ids []int64) error {
	// 根据id 批量查询
	subTasks, err := s.commentarySubTaskRepo.GetSubTasksByIds(ctx, ids)
	if err != nil {
		g.Log().Errorf(ctx, "MergeBatch failed, err: %v", err)
		return err
	}

	subTaskBOs := lo.Map(subTasks, func(subTask *do.CommentarySubTask, _ int) *bo.CommentarySubTaskBO {
		return conv.CommentarySubTaskDOToBO(subTask)
	})

	isBalanceSufficient, err := s.checkBalanceSufficient(ctx, appId, tenantId, int32(consts.BUSINESS_VIDEO_MERGE_COMPOSITION), subTaskBOs)
	if err != nil {
		g.Log().Errorf(ctx, "MergeBatch failed, checkBalanceSufficient failed, err: %v", err)
		return err
	}
	if !isBalanceSufficient {
		g.Log().Errorf(ctx, "MergeBatch failed, 余额不足")
		return uerrors.ErrBalanceInsufficient
	}

	// 转map
	mainIds := lo.SliceToMap(subTaskBOs, func(item *bo.CommentarySubTaskBO) (int64, struct{}) {
		return item.MainTaskId, struct{}{}
	})
	if len(mainIds) > 1 {
		g.Log().Errorf(ctx, "MergeBatch failed, 跨主任务合并, mainIds: %v", mainIds)
		return uerrors.ErrMainTaskNotMatch
	}
	mainTask, err := s.mainTaskRepo.GetTaskById(ctx, subTaskBOs[0].MainTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "MergeBatch failed, GetTaskById failed, err: %v", err)
		return uerrors.ErrBusy
	}
	// 标记合成中
	needMergeSubTasks := []*bo.CommentarySubTaskBO{}
	err = db.GetDB().Transaction(func(tx *gorm.DB) error {
		for _, subTask := range subTasks {
			g.Log().Debugf(ctx, "MergeBatch, subTask: %+v", subTask)
			if subTask.NeedMerge != 1 {
				g.Log().Infof(ctx, "MergeBatch skip, subTask.NeedMerge != 1, subTaskId: %v", subTask.Id)
				continue
			}
			if subTask.MergeStatus == consts.MergeStatusProcessing {
				g.Log().Infof(ctx, "MergeBatch skip, subTask.MergeStatus == MergeStatusProcessing, subTaskId: %v", subTask.Id)
				continue
			}
			err = s.commentarySubTaskRepo.UpdateSubTaskFields(ctx, subTask.Id, map[string]interface{}{
				"merge_status": int(consts.MergeStatusProcessing),
			}, tx)
			if err != nil {
				return err
			}
			needMergeSubTasks = append(needMergeSubTasks, conv.CommentarySubTaskDOToBO(subTask))
		}
		return nil
	})
	if len(needMergeSubTasks) == 0 {
		g.Log().Infof(ctx, "MergeBatch skip, no need merge sub task")
		return nil
	}
	if err != nil {
		g.Log().Errorf(ctx, "MergeBatch failed, UpdateSubTaskFields failed, err: %v", err)
		return uerrors.ErrBusy
	}
	workflowOptions := client.StartWorkflowOptions{
		ID:        fmt.Sprintf("%v_commentary_batch_merge_workflow", requestId),
		TaskQueue: config.GetConfig().Temporal.Queue,
	}
	workflowRun, err := temporal.TemporalClient.ExecuteWorkflow(ctx, workflowOptions, "VideoBatchMergeWorkflow", mainTask, needMergeSubTasks)
	if err != nil {
		g.Log().Errorf(ctx, "MergeBatch failed, err: %v", err)
		return uerrors.ErrBusy
	}

	g.Log().Infof(ctx, "start VideoBatchMergeWorkflow workflow, run_id: %+v, workflow_id:%v", workflowRun.GetRunID(), workflowRun.GetID())
	return nil
}

func (s *CommentarySubTaskServiceImpl) MockInpaint(ctx context.Context, videoUrls []string) (taskIds []string, err error) {
	g.Log().Infof(ctx, "CommentarySubTaskServiceImpl MockInpaint, videoUrls:%+v", videoUrls)
	//tasksBO := make([]*bo.CommentarySubTaskBO, 0, len(videoUrls))
	tasksDo := make([]*do.CommentarySubTask, 0, len(videoUrls))
	for _, val := range videoUrls {
		// 创建数据库记录
		// 创建BO
		taskDo := &do.CommentarySubTask{}
		taskDo.Id, err = id_generator.GenerateId()
		if err != nil {
			g.Log().Errorf(ctx, "CommentarySubTaskServiceImpl MockInpaint, id_generator failed, err: %v", err)
			return nil, uerrors.ErrBusy
		}
		objectName, err := processor_util.GetObjectNameByHttpsUrl(val)
		if err != nil {
			g.Log().Errorf(ctx, "CommentarySubTaskServiceImpl MockInpaint, GetObjectNameByHttpsUrl failed, err: %v", err)
			return nil, uerrors.ErrBusy
		}
		_, directUrl, err := obs.GetOsClient().GetObjectUrl(objectName)
		if err != nil {
			g.Log().Errorf(ctx, "CommentarySubTaskServiceImpl MockInpaint, GetObjectUrl failed, err: %v", err)
			return nil, uerrors.ErrBusy
		}
		avInfo, err := media.GetAudioVideoInfo(directUrl)
		if err != nil {
			g.Log().Errorf(ctx, "CommentarySubTaskServiceImpl MockInpaint, GetAudioVideoInfo failed, url: %v, err: %v", directUrl, err)
			return nil, uerrors.ErrBusy
		}
		taskDo.VideoDuration = avInfo.VideoInfo.DurationFloat
		taskDo.MaterialHighlightUrl = val
		taskDo.TenantId = 273297330659131394
		taskDo.EraseEdition = consts.EraseEditionNormal
		taskDo.AppId = 2
		taskDo.MainTaskId = 12345
		taskDo.Name = fmt.Sprintf("mock_inpaint_%v", taskDo.Id)

		tasksDo = append(tasksDo, taskDo)
	}

	err = impl.NewCommentarySubTaskRepoImpl().CreateSubTasks(ctx, tasksDo, nil)
	if err != nil {
		g.Log().Errorf(ctx, "CommentarySubTaskServiceImpl MockInpaint, CreateSubTasks failed, err: %v", err)
		return nil, uerrors.ErrBusy
	}
	taskIds = lo.Map(tasksDo, func(item *do.CommentarySubTask, _ int) string {
		return strconv.FormatInt(item.Id, 10)
	})
	tasksBO := lo.Map(tasksDo, func(item *do.CommentarySubTask, _ int) *bo.CommentarySubTaskBO {
		return conv.CommentarySubTaskDOToBO(item)
	})
	workflowOptions := client.StartWorkflowOptions{
		ID:        fmt.Sprintf("%v_commentary_mock_inpaint_workflow", uuid.New().String()),
		TaskQueue: config.GetConfig().Temporal.Queue,
	}
	workflowRun, err := temporal.TemporalClient.ExecuteWorkflow(context.TODO(), workflowOptions, "MockInpaint", tasksBO)
	if err != nil {
		g.Log().Errorf(ctx, "MockInpaint failed, err: %v", err)
		return nil, uerrors.ErrBusy
	}
	g.Log().Infof(ctx, "start MockInpaint workflow, run_id: %+v, workflow_id:%v", workflowRun.GetRunID(), workflowRun.GetID())

	return taskIds, nil
}

// GetVideoPostUrl
func (s *CommentarySubTaskServiceImpl) GetVideoPostUrl(ctx context.Context, subTaskId int64) (string, error) {
	if subTaskId <= 0 {
		return "", fmt.Errorf("子任务ID无效")
	}
	task, err := s.commentarySubTaskRepo.GetSubTaskById(ctx, subTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "GetTranslateSubtitleTaskById failed, err: %v, subTaskId: %+v", err, subTaskId)
		return "", err
	}

	postUrl := getPresignUrlByTaskWithTimeout(ctx, task, true, 60*60)
	g.Log().Infof(ctx, "GetVideoPostUrl success, subTaskId: %d", subTaskId)
	if postUrl != "" {
		return postUrl, nil
	}
	return "", fmt.Errorf("子任务后处理视频地址为空")
}

func getPresignUrlByTaskWithTimeout(ctx context.Context, task *do.CommentarySubTask, useCdn bool, timeout int32) string {
	postUrl := task.MergedVideoUrl
	objName, err := obs.GetObjectNameByHttpsUrl(postUrl)
	if err != nil {
		g.Log().Errorf(ctx, "GetObjectNameByHttpsUrl failed, taskId: %v, err: %s", task.Id, err.Error())
	} else {
		// 返回预签名链接
		ext := filepath.Ext(task.Name)
		projectName := strings.Replace(task.Name, ext, "", -1)
		targetExt := filepath.Ext(postUrl)
		encodedFileName := url.QueryEscape(projectName + targetExt)
		g.Log().Infof(ctx, "getPresignUrlByTaskWithTimeout, taskId: %v, projectName: %s, targetExt: %s, encodedFileName: %s",
			task.Id, projectName, targetExt, encodedFileName)
		sign := fmt.Sprintf(`attachment;filename="%s";filename*=UTF-8''%s`, encodedFileName, encodedFileName)
		presignUrl, err := obs.GetOsClient().Presign(objName, oss_util.WithContentDisposition(sign), oss_util.WithExpires(timeout))
		if err != nil {
			g.Log().Errorf(ctx, "Presign failed, taskId: %v, err: %s", task.Id, err.Error())
		} else {
			if useCdn {
				return obs.ReplaceUrlToCdn(presignUrl)
			}
			return presignUrl
		}
	}
	return postUrl
}

func (s *CommentarySubTaskServiceImpl) MockSubtitleMerge(ctx context.Context, taskIds []int64) (err error) {
	g.Log().Infof(ctx, "MockSubtitleMerge, taskIds: %+v", taskIds)
	for _, taskId := range taskIds {
		subTaskDo, err := s.commentarySubTaskRepo.GetSubTaskById(ctx, taskId)
		if err != nil {
			g.Log().Errorf(ctx, "MockSubtitleMerge failed, taskId: %v, err: %s", taskId, err.Error())
			return uerrors.ErrBusy
		}
		g.Log().Infof(ctx, "MockSubtitleMerge success, taskId: %v, mainId: %v", taskId, subTaskDo.MainTaskId)
		mainTask, err := s.mainTaskRepo.GetTaskById(ctx, subTaskDo.MainTaskId)
		if err != nil {
			g.Log().Errorf(ctx, "MockSubtitleMerge failed, get main task failed, taskId: %v, err: %s", subTaskDo.MainTaskId, err.Error())
			return uerrors.ErrBusy
		}

		workflowOptions := client.StartWorkflowOptions{
			ID:        fmt.Sprintf("%v_commentary_mock_subtitle_merge_workflow", uuid.New().String()),
			TaskQueue: config.GetConfig().Temporal.Queue,
		}
		taskBo := conv.CommentaryMainTaskDOToBO(mainTask)
		subTaskBo := conv.CommentarySubTaskDOToBO(subTaskDo)
		workflowRun, err := temporal.TemporalClient.ExecuteWorkflow(context.TODO(), workflowOptions,
			"MockSubtitleMerge", taskBo, subTaskBo)
		if err != nil {
			g.Log().Errorf(ctx, "MockSubtitleMerge failed, err: %v", err)
			return uerrors.ErrBusy
		}
		g.Log().Infof(ctx, "start MockSubtitleMerge workflow, run_id: %+v, workflow_id:%v", workflowRun.GetRunID(), workflowRun.GetID())
	}
	return nil
}

// GetEditOperationLogsByDeductOrderIds 通过扣费订单ID列表批量获取编辑操作流水
func (s *CommentarySubTaskServiceImpl) GetEditOperationLogsByDeductOrderIds(ctx context.Context, deductOrderIds []string) ([]*bo.CommentaryEditOperationLogBO, error) {
	g.Log().Infof(ctx, "GetEditOperationLogsByDeductOrderIds start, deductOrderIds: %v", deductOrderIds)

	// 参数验证
	if len(deductOrderIds) == 0 {
		return []*bo.CommentaryEditOperationLogBO{}, nil
	}

	// 初始化编辑操作流水仓储
	editOperationLogRepo := impl.NewCommentaryEditOperationLogRepoImpl()

	// 调用仓储层获取编辑操作流水
	editOperationLogDOs, err := editOperationLogRepo.GetEditOperationLogsByDeductOrderIds(ctx, deductOrderIds)
	if err != nil {
		g.Log().Errorf(ctx, "GetEditOperationLogsByDeductOrderIds failed, deductOrderIds: %v, err: %v", deductOrderIds, err)
		return nil, fmt.Errorf("批量获取编辑操作流水失败: %w", err)
	}

	// 转换为BO列表
	results := make([]*bo.CommentaryEditOperationLogBO, 0, len(editOperationLogDOs))
	for _, editOperationLogDO := range editOperationLogDOs {
		editOperationLogBO := &bo.CommentaryEditOperationLogBO{
			Id:            editOperationLogDO.Id,
			MainTaskId:    editOperationLogDO.MainTaskId,
			SubTaskId:     editOperationLogDO.SubTaskId,
			SubTaskName:   editOperationLogDO.SubTaskName,
			BizMode:       editOperationLogDO.BizMode,
			DeductOrderId: editOperationLogDO.DeductOrderId,
			OperationType: editOperationLogDO.OperationType,
			OperationDesc: editOperationLogDO.OperationDesc,
			CreatedAt:     editOperationLogDO.CreatedAt,
			UpdatedAt:     editOperationLogDO.UpdatedAt,
		}
		results = append(results, editOperationLogBO)
	}

	g.Log().Infof(ctx, "GetEditOperationLogsByDeductOrderIds success, deductOrderIds: %v, count: %d", deductOrderIds, len(results))
	return results, nil
}
