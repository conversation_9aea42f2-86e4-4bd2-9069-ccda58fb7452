package impl

import (
	"business-workflow/internal/common/config"
	"business-workflow/internal/common/db"
	"business-workflow/internal/common/uerrors"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/common"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/processor/sub_item_processor"
	"business-workflow/internal/repo/commentary_repo"
	commentary_repo_impl "business-workflow/internal/repo/commentary_repo/impl"
	"business-workflow/internal/service/commentary_svc"
	"business-workflow/internal/temporal"
	"business-workflow/internal/util/id_generator"
	"business-workflow/internal/util/srt_util"
	"context"
	"fmt"
	"time"

	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	"go.uber.org/zap"

	"github.com/gogf/gf/v2/frame/g"
	"go.temporal.io/sdk/client"
	"gorm.io/gorm"
)

type CommentarySubtitleItemServiceImpl struct {
	taskrepo         commentary_repo.ICommentaryMainTaskRepo
	subtaskRepo      commentary_repo.ICommentarySubTaskRepo
	subtitleItemRepo commentary_repo.ICommentarySubtitleItemRepo
	subTaskSvc       commentary_svc.ICommentarySubTaskService
}

func NewCommentarySubtitleItemServiceImpl(subTaskSvc commentary_svc.ICommentarySubTaskService) commentary_svc.ICommentarySubtitleItemService {
	return &CommentarySubtitleItemServiceImpl{
		taskrepo:         commentary_repo_impl.NewCommentaryMainTaskRepoImpl(),
		subtaskRepo:      commentary_repo_impl.NewCommentarySubTaskRepoImpl(),
		subtitleItemRepo: commentary_repo_impl.NewCommentarySubtitleItemRepoImpl(),
		subTaskSvc:       subTaskSvc,
	}
}

// MergeSubtitle 合并字幕片段
func (s *CommentarySubtitleItemServiceImpl) MergeSubtitle(ctx context.Context, req *bo.MergeSubtitleReqBO) (*bo.MergeSubtitleResBO, error) {
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	if req.SubTaskId <= 0 {
		return nil, fmt.Errorf("子任务ID无效")
	}

	if len(req.ItemIdxList) < 2 {
		return nil, fmt.Errorf("合并片段至少需要2个")
	}

	// TODO: 实现合并字幕片段逻辑
	// 1. 参数验证：检查合并片段列表是否有效（至少2个片段）
	// 2. 使用事务处理合并操作
	// 3. 获取要合并的字幕项信息
	// 4. 合并文本内容和时间范围
	// 5. 更新第一个字幕项，删除其他字幕项
	// 6. 调整后续字幕项的索引
	// 7. 返回合并后的字幕项信息

	var resultSubtitleItem *bo.CommentarySubtitleItemBO
	err := db.GetDB().Debug().Transaction(func(tx *gorm.DB) error {
		// TODO: 在事务中实现合并逻辑
		return fmt.Errorf("合并字幕片段功能待实现")
	})

	if err != nil {
		return nil, err
	}

	return &bo.MergeSubtitleResBO{
		SubtitleItem: resultSubtitleItem,
	}, nil
}

// SplitSubtitle 拆分片段
func (s *CommentarySubtitleItemServiceImpl) SplitSubtitle(ctx context.Context, req *bo.SplitSubtitleReqBO) (*bo.SplitSubtitleResBO, error) {
	if req.SubTaskId <= 0 {
		return nil, fmt.Errorf("子任务ID无效")
	}

	if req.OriginTextUp == "" || req.OriginTextDown == "" {
		return nil, fmt.Errorf("拆分文本不能为空")
	}

	// 同一个任务的创建/编辑/删除字幕操作需要加锁
	lock, lockErr := lockEditSubtitle(ctx, req.SubTaskId)
	if lockErr != nil {
		g.Log().Errorf(ctx, "SplitSubtitle failed, lockEditSubtitle error, err: %v, req: %v", lockErr, req)
		return nil, lockErr
	}

	defer func() {
		lockErr := lock.UnLock()
		if lockErr != nil {
			g.Log().Errorf(ctx, "unlock edit subtitle lock failed, err: %v, subTaskId: %d", lockErr, req.SubTaskId)
		}
	}()

	g.Log().Infof(ctx, "SplitSubtitle, req: %+v", req)

	// 获取子任务信息
	subTask, err := s.subtaskRepo.GetSubTaskById(ctx, req.SubTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "SplitSubtitle failed, GetSubTaskById error, err: %v, req: %v", err, req)
		return nil, uerrors.ErrSubtitleItemNoExist
	}
	if subTask == nil {
		g.Log().Errorf(ctx, "SplitSubtitle failed, subTask not found, req: %v", req)
		return nil, uerrors.ErrSubtitleItemNoExist
	}

	// 获取要拆分的字幕项
	originalItem, err := s.subtitleItemRepo.GetSubtitleItemByIdAndSubTaskId(ctx, req.SubtitleItemId, req.SubTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "SplitSubtitle failed, GetSubtitleItemByIdAndSubTaskId error, err: %v, req: %v", err, req)
		return nil, uerrors.ErrBusy
	}
	if originalItem == nil {
		g.Log().Errorf(ctx, "SplitSubtitle failed, originalItem not found, req: %v", req)
		return nil, uerrors.ErrSubtitleItemNoExist
	}

	// 先判断当前是否在生成中
	if s.isSubtitleProcessing(originalItem) {
		// 如果正在生成中，则返回错误
		g.Log().Errorf(ctx, "SplitSubtitle failed, subtitle is processing, subtitleItemId: %d", req.SubtitleItemId)
		return nil, uerrors.ErrGenerateVoiceBusy
	}

	g.Log().Infof(ctx, "SplitSubtitle, original item: id=%v, text=%v, startStr=%v, endStr=%v", originalItem.Id, originalItem.TargetSubtitle, originalItem.SubtitleStartStr, originalItem.SubtitleEndStr)

	// 编辑拆分前的字幕（第一部分）
	subtitleUp := originalItem
	subtitleUp.OriginSubtitle = req.OriginTextUp // 更新原文为拆分后的第一段
	subtitleUp.TextTranslateStatus = consts.SubtitleItemRegenerateStatusNeed

	// 生成新字幕项的分布式ID
	newSubtitleItemId, err := id_generator.GenerateId()
	if err != nil {
		g.Log().Errorf(ctx, "SplitSubtitle failed, GenerateId error, err: %v, req: %v", err, req)
		return nil, fmt.Errorf("生成字幕项ID失败: %w", err)
	}

	// 创建新字幕项（第二部分）
	subtitleDown := &do.CommentarySubtitleItem{
		Id:                  newSubtitleItemId,
		SubTaskId:           subtitleUp.SubTaskId,
		MainTaskId:          subtitleUp.MainTaskId,
		ItemIdx:             subtitleUp.ItemIdx + 1,  // 新片段的索引为原片段索引加1
		TargetSubtitle:      "",                      //新字幕译文为空
		OriginSubtitle:      req.OriginTextDown,      // 保持原始字幕
		SourceLangId:        subtitleUp.SourceLangId, // 保持源语言
		TargetLangId:        subtitleUp.TargetLangId, // 保持目标语言
		AudioConfig:         subtitleUp.AudioConfig,  // 保持音频配置不变
		SpeakerId:           subtitleUp.SpeakerId,    // 保持说话人不变
		VoiceInfo:           subtitleUp.VoiceInfo,    // 保持音色信息不变
		GenerateVoiceStatus: consts.SubtitleItemRegenerateStatusNone,
		TextTranslateStatus: consts.SubtitleItemReTranslateStatusNeed,
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
	}

	// 拆分原文时间戳
	s.splitOriginStr(ctx, subtitleUp.OriginSubtitle, req.OriginTextUp, req.OriginTextDown, subtitleUp, subtitleDown)

	// 第一段字幕片段清空TTS信息
	subtitleUp.SubtitleStartStr = subtitleUp.OriginSubtitleStartStr
	subtitleUp.SubtitleEndStr = subtitleUp.OriginSubtitleEndStr
	subtitleUp.AudioConfig = common.NewDefaultAudioConfig()
	subtitleUp.TTSUrl = ""
	subtitleUp.TTSWords = []*common.WordInfo{}

	// 第二段字幕片段清空TTS信息
	subtitleDown.SubtitleStartStr = subtitleDown.OriginSubtitleStartStr
	subtitleDown.SubtitleEndStr = subtitleDown.OriginSubtitleEndStr
	subtitleDown.AudioConfig = common.NewDefaultAudioConfig()
	subtitleDown.TTSUrl = ""
	subtitleDown.TTSWords = []*common.WordInfo{}

	sub_item_processor.UpdateSubtitleItemSubList(ctx, subTask, []*do.CommentarySubtitleItem{subtitleUp, subtitleDown})
	// 使用事务处理拆分操作
	var resultItems []*do.CommentarySubtitleItem
	err = db.GetDB().Debug().Transaction(func(tx *gorm.DB) error {
		// 1. 更新原字幕项（第一部分）
		fields := []string{
			"origin_subtitle",           // 原文
			"subtitle_start_str",        // 字幕开始时间
			"subtitle_end_str",          // 字幕结束时间
			"origin_subtitle_start_str", // 原文开始时间
			"origin_subtitle_end_str",   // 原文结束时间
			"audio_config",              // 音频配置（重置）
			"tts_url",                   // TTS音频地址（清空）
			"tts_words",                 // TTS词汇信息（清空）
			"generate_voice_status",     // 生成语音状态
			"text_translate_status",     // 翻译状态
		}
		err = s.subtitleItemRepo.UpdateCommentarySubtitleItemTx(ctx, tx, subtitleUp, fields)
		if err != nil {
			g.Log().Errorf(ctx, "SplitSubtitle failed, update subtitleUp error, err: %v, itemId: %v", err, subtitleUp.Id)
			return fmt.Errorf("更新原字幕项失败: %w", err)
		}

		// 2. 更新后续字幕项的索引（将后面的字幕项索引加1）
		err = s.subtitleItemRepo.UpdateTranslateSubtitleItemIdxRecurrence(ctx, req.SubTaskId, subtitleDown.ItemIdx, 1, tx)
		if err != nil {
			g.Log().Errorf(ctx, "SplitSubtitle failed, update item index error, err: %v, subTaskId: %v, itemIdx: %v", err, req.SubTaskId, subtitleUp.ItemIdx)
			return fmt.Errorf("更新字幕项索引失败: %w", err)
		}

		// 3. 创建新字幕项（第二部分）
		err = s.subtitleItemRepo.CreateSubtitleItem(ctx, subtitleDown, tx)
		if err != nil {
			g.Log().Errorf(ctx, "SplitSubtitle failed, create subtitleDown error, err: %v", err)
			return fmt.Errorf("创建新字幕项失败: %w", err)
		}

		resultItems = []*do.CommentarySubtitleItem{subtitleUp, subtitleDown}
		return nil
	})
	if err != nil {
		g.Log().Errorf(ctx, "SplitSubtitle failed, transaction error, err: %v, req: %v", err, req)
		return nil, fmt.Errorf("拆分字幕失败: %w", err)
	}

	// 标记子任务需要重新合成
	err = s.subTaskSvc.MarkNeedMerge(ctx, req.SubTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "SplitSubtitle failed, MarkNeedMerge error, err: %v, subTaskId: %v", err, req.SubTaskId)
		// 这里不返回错误，因为主要操作已经成功，只是标记合成失败
	}

	// 构建响应结果
	resp := &bo.SplitSubtitleResBO{}
	for _, item := range resultItems {
		itemBO := conv.CommentarySubtitleItemDOToBO(item)
		resp.SubtitleItems = append(resp.SubtitleItems, itemBO)
	}

	g.Log().Infof(ctx, "SplitSubtitle success, created %d items, subTaskId: %v, originalItemId: %v", len(resp.SubtitleItems), req.SubTaskId, req.SubtitleItemId)
	return resp, nil
}

// DeleteSubtitleItem 删除片段
func (s *CommentarySubtitleItemServiceImpl) DeleteSubtitleItem(ctx context.Context, subTaskId int64, subtitleItemId int64) error {
	// 同一个任务的创建/编辑/删除片段操作需要加锁
	lock, lockErr := lockEditSubtitle(ctx, subTaskId)
	if lockErr != nil {
		g.Log().Errorf(ctx, "DeleteSubtitleItem failed, lockEditSubtitle error, err: %v, subTaskId: %v", lockErr, subTaskId)
		return lockErr
	}
	defer func() {
		lockErr := lock.UnLock()
		if lockErr != nil {
			g.Log().Errorf(ctx, "unlock edit subtitle lock failed, err: %v, subTaskId: %d", lockErr, subTaskId)
		}
	}()

	g.Log().Infof(ctx, "DeleteSubtitleItem, subTaskId: %v, subtitleItemId: %v", subTaskId, subtitleItemId)

	// 获取片段信息
	item, err := s.subtitleItemRepo.GetSubtitleItemByIdAndSubTaskId(ctx, subtitleItemId, subTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "DeleteSubtitleItem failed, GetSubtitleItemByIdAndSubTaskId error, err: %v, subTaskId: %v, subtitleItemId: %v", err, subTaskId, subtitleItemId)
		return uerrors.ErrBusy
	}
	if item == nil {
		g.Log().Errorf(ctx, "DeleteSubtitleItem failed, subtitleItem not found, subTaskId: %v, subtitleItemId: %v", subTaskId, subtitleItemId)
		return uerrors.ErrSubtitleItemNoExist
	}

	// 使用事务包装写操作
	err = db.GetDB().Transaction(func(tx *gorm.DB) error {
		// 删除片段
		err = s.subtitleItemRepo.DeleteSubtitleItem(ctx, item.Id, tx)
		if err != nil {
			g.Log().Errorf(ctx, "DeleteSubtitleItem failed, DeleteSubtitleItem error, err: %v, subtitleItemId: %v", err, subtitleItemId)
			return err
		}

		// 后继节点索引回退（将后面的字幕项索引减1）
		err = s.subtitleItemRepo.UpdateTranslateSubtitleItemIdxRecurrence(ctx, subTaskId, item.ItemIdx, -1, tx)
		if err != nil {
			g.Log().Errorf(ctx, "DeleteSubtitleItem failed, UpdateTranslateSubtitleItemIdxRecurrence error, err: %v, subTaskId: %v, itemIdx: %v", err, subTaskId, item.ItemIdx)
			return err
		}

		return nil
	})

	if err != nil {
		g.Log().Errorf(ctx, "DeleteSubtitleItem transaction failed, err: %v, subTaskId: %v, subtitleItemId: %v", err, subTaskId, subtitleItemId)
		return uerrors.ErrBusy
	}

	// 标记子任务需要重新合成
	err = s.subTaskSvc.MarkNeedMerge(ctx, subTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "DeleteSubtitleItem failed, MarkNeedMerge error, err: %v, subTaskId: %v", err, subTaskId)
		// 这里不返回错误，因为主要操作已经成功，只是标记合成失败
	}

	g.Log().Infof(ctx, "DeleteSubtitleItem success, subTaskId: %v, subtitleItemId: %v, itemIdx: %v", subTaskId, subtitleItemId, item.ItemIdx)
	return nil
}

// ModifySubtitle 修改片段
func (s *CommentarySubtitleItemServiceImpl) ModifySubtitle(ctx context.Context, req *bo.ModifySubtitleReqBO) (*bo.ModifySubtitleResBO, error) {
	modifyBegin := time.Now()

	// 同一个任务的创建/编辑/删除字幕操作需要加锁
	lock, lockErr := lockEditSubtitle(ctx, req.SubTaskId)
	if lockErr != nil {
		g.Log().Errorf(ctx, "ModifySubtitle failed, lockEditSubtitle error, err: %v, req: %v", lockErr, req)
		return nil, lockErr
	}
	defer func() {
		lockErr := lock.UnLock()
		if lockErr != nil {
			g.Log().Errorf(ctx, "unlock edit subtitle lock failed, err: %v, subTaskId: %d", lockErr, req.SubTaskId)
		}
	}()

	g.Log().Infof(ctx, "ModifySubtitle, req: %+v", req)

	// 获取子任务信息
	subTask, err := s.subtaskRepo.GetSubTaskById(ctx, req.SubTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "ModifySubtitle failed, GetSubTaskById error, err: %v, req: %v", err, req)
		return nil, uerrors.ErrBusy
	}
	if subTask == nil {
		g.Log().Errorf(ctx, "ModifySubtitle failed, subTask not found, req: %v", req)
		return nil, uerrors.ErrSubtitleItemNoExist
	}

	// 获取字幕项信息
	subtitleItem, err := s.subtitleItemRepo.GetSubtitleItemByIdAndSubTaskId(ctx, req.SubtitleItemId, req.SubTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "ModifySubtitle failed, GetSubtitleItemByIdAndSubTaskId error, err: %v, req: %v", err, req)
		return nil, uerrors.ErrBusy
	}
	if subtitleItem == nil {
		g.Log().Errorf(ctx, "ModifySubtitle failed, subtitleItem not found, req: %v", req)
		return nil, uerrors.ErrSubtitleItemNoExist
	}

	// 先判断当前是否在生成中
	if s.isSubtitleProcessing(subtitleItem) {
		g.Log().Errorf(ctx, "ModifySubtitle failed, subtitle is processing, subtitleItemId: %d", req.SubtitleItemId)
		return nil, uerrors.ErrGenerateVoiceBusy
	}

	// 解析时间参数
	startMs, err := srt_util.SrtStringTime2MsDot(req.SubtitleStartStr)
	if err != nil {
		g.Log().Errorf(ctx, "ModifySubtitle failed, parse SubtitleStartStr error, SubtitleStartStr: %s, err: %v", req.SubtitleStartStr, err)
		return nil, uerrors.ErrParam
	}
	endMs, err := srt_util.SrtStringTime2MsDot(req.SubtitleEndStr)
	if err != nil {
		g.Log().Errorf(ctx, "ModifySubtitle failed, parse SubtitleEndStr error, SubtitleEndStr: %s, err: %v", req.SubtitleEndStr, err)
		return nil, uerrors.ErrParam
	}

	// 检查时间合法性
	if startMs >= endMs {
		g.Log().Errorf(ctx, "ModifySubtitle failed, invalid time range, startMs: %d, endMs: %d", startMs, endMs)
		return nil, uerrors.ErrParam
	}

	// 获取旧的时间用于比较
	oldTargetStartMs, err := srt_util.SrtStringTime2MsDot(subtitleItem.SubtitleStartStr)
	if err != nil {
		g.Log().Errorf(ctx, "ModifySubtitle failed, parse original StartMs error, err: %v", err)
		return nil, uerrors.ErrParam
	}
	oldTargetEndMs, err := srt_util.SrtStringTime2MsDot(subtitleItem.SubtitleEndStr)
	if err != nil {
		g.Log().Errorf(ctx, "ModifySubtitle failed, parse original EndMs error, err: %v", err)
		return nil, uerrors.ErrParam
	}

	// 检查是否有变化
	hasTargetTimeChange := oldTargetStartMs != startMs || oldTargetEndMs != endMs
	hasTargetTextChange := subtitleItem.TargetSubtitle != req.TargetSubtitle

	// 判断是否需要重新翻译
	subtitleItem.OriginSubtitle = req.OriginSubtitle
	needReTranslate := s.isSubtitleNeedReTranslate(subtitleItem)
	subtitleItem.TextTranslateStatus = consts.SubtitleItemReTranslateStatusNone
	if needReTranslate {
		subtitleItem.TextTranslateStatus = consts.SubtitleItemReTranslateStatusNeed
	}

	// 更新字幕项信息
	if hasTargetTextChange {
		subtitleItem.TTSWords = []*common.WordInfo{}
	}
	if hasTargetTimeChange {
		// 更新时间
		subtitleItem.TTSWords = []*common.WordInfo{}
	}
	// 新判断是否需要配音
	subtitleItem.TargetSubtitle = req.TargetSubtitle
	subtitleItem.SubtitleStartStr = req.SubtitleStartStr
	subtitleItem.SubtitleEndStr = req.SubtitleEndStr
	needRegenerate := s.isSubtitleItemNeedRegenerate(subtitleItem)
	subtitleItem.GenerateVoiceStatus = consts.SubtitleItemRegenerateStatusNone
	if needRegenerate {
		subtitleItem.GenerateVoiceStatus = consts.SubtitleItemRegenerateStatusNeed
	}

	if req.Speed > 0 {
		if subtitleItem.AudioConfig == nil {
			subtitleItem.AudioConfig = common.NewDefaultAudioConfig()
		}
		subtitleItem.AudioConfig.Speed = req.Speed
	}
	err = sub_item_processor.UpdateSubtitleItemSubList(ctx, subTask, []*do.CommentarySubtitleItem{subtitleItem})
	if err != nil {
		g.Log().Errorf(ctx, "ModifySubtitle, WrapSubtitleIfNeeded failed, err: %v, req: %v", err, req)
		return nil, err
	}
	// 使用事务处理更新操作
	err = db.GetDB().Debug().Transaction(func(tx *gorm.DB) error {
		// 确定需要更新的字段
		var fields []string
		fields = append(fields, "text_translate_status")
		fields = append(fields, "generate_voice_status")
		fields = append(fields, "origin_subtitle")
		fields = append(fields, "sub_item_list")
		if hasTargetTextChange {
			fields = append(fields, "target_subtitle")
		}

		if hasTargetTimeChange {
			fields = append(fields, "subtitle_start_str", "subtitle_end_str")
		}
		if hasTargetTextChange || hasTargetTimeChange {
			fields = append(fields, "tts_words")
		}
		if req.Speed > 0 {
			fields = append(fields, "audio_config")
		}

		if len(fields) > 0 {
			err = s.subtitleItemRepo.UpdateCommentarySubtitleItemTx(ctx, tx, subtitleItem, fields)
			if err != nil {
				g.Log().Errorf(ctx, "ModifySubtitle failed, UpdateCommentarySubtitleItemTx error, err: %v, subtitleItemId: %v", err, subtitleItem.Id)
				return fmt.Errorf("更新字幕项失败: %w", err)
			}
		}

		return nil
	})
	if err != nil {
		g.Log().Errorf(ctx, "ModifySubtitle failed, Transaction error, err: %v, req: %v", err, req)
		return nil, fmt.Errorf("修改字幕失败: %w", err)
	}

	// 如果有变化，标记子任务需要重新合成
	if hasTargetTimeChange || hasTargetTextChange {
		err = s.subTaskSvc.MarkNeedMerge(ctx, req.SubTaskId)
		if err != nil {
			g.Log().Errorf(ctx, "ModifySubtitle failed, MarkNeedMerge error, err: %v, subTaskId: %v", err, req.SubTaskId)
			return nil, err
		}
	}

	g.Log().Infof(ctx, "ModifySubtitle, cost time: %vms, subTaskId: %v", time.Since(modifyBegin).Milliseconds(), req.SubTaskId)

	// 构建响应结果
	resp := &bo.ModifySubtitleResBO{
		SubtitleItem: conv.CommentarySubtitleItemDOToBO(subtitleItem),
	}

	g.Log().Infof(ctx, "ModifySubtitle success, subTaskId: %v, subtitleItemId: %v, hasTargetTimeChange: %v, hasTargetTextChange: %v, needReTranslate: %v, needRegenerate: %v",
		req.SubTaskId, req.SubtitleItemId, hasTargetTimeChange, hasTargetTextChange, needReTranslate, needRegenerate)
	return resp, nil
}

// AddSubtitle 添加片段
func (s *CommentarySubtitleItemServiceImpl) AddSubtitle(ctx context.Context, req *bo.AddSubtitleReqBO) (*bo.AddSubtitleResBO, error) {
	tid := req.SubTaskId
	// 同一个任务的创建/编辑/删除字幕操作需要加锁
	lock, lockErr := lockEditSubtitle(ctx, tid)
	if lockErr != nil {
		return nil, lockErr
	}
	defer func() {
		lockErr := lock.UnLock()
		if lockErr != nil {
			g.Log().Errorf(ctx, "unlock edit subtitle lock failed, err: %v, taskId: %d", lockErr, tid)
		}
	}()

	var err error
	originStartMs, err := srt_util.SrtStringTime2MsDot(req.OriginSubtitleStartStr)
	if err != nil {
		g.Log().Errorf(ctx, "AddSubtitle failed, SrtStringTime2MsDot error, err: %v, req: %v", err, req)
		return nil, uerrors.ErrParam
	}
	originEndMs, err := srt_util.SrtStringTime2MsDot(req.OriginSubtitleEndStr)
	if err != nil {
		g.Log().Errorf(ctx, "AddSubtitle failed, SrtStringTime2MsDot error, err: %v, req: %v", err, req)
		return nil, uerrors.ErrParam
	}
	if originEndMs <= originStartMs {
		g.Log().Errorf(ctx, "AddSubtitle failed, originEndMs should be greater than originStartMs, req: %v", req)
		return nil, uerrors.ErrParam
	}
	// 获取子任务信息
	task, err := s.subtaskRepo.GetSubTaskById(ctx, tid)
	if err != nil {
		g.Log().Error(ctx, "AddSubtitle failed, GetSubTaskById error", zap.Error(err), zap.Any("req", req))
		return nil, uerrors.ErrSubtitleItemNoExist
	}

	// 生成新字幕项的分布式ID
	newSubtitleItemId, err := id_generator.GenerateId()
	if err != nil {
		g.Log().Errorf(ctx, "AddSubtitle failed, GenerateId error, err: %v, req: %v", err, req)
		return nil, fmt.Errorf("生成字幕项ID失败: %w", err)
	}

	newSubtitle := &do.CommentarySubtitleItem{}
	newSubtitle.Id = newSubtitleItemId
	newSubtitle.ItemIdx = req.ItemIdx
	newSubtitle.SubTaskId = tid
	newSubtitle.MainTaskId = task.MainTaskId
	newSubtitle.TargetSubtitle = req.TargetSubtitle
	newSubtitle.OriginSubtitle = req.OriginSubtitle
	newSubtitle.SourceLangId = task.SourceLangId
	newSubtitle.TargetLangId = task.TargetLangId
	newSubtitle.GenerateVoiceStatus = consts.SubtitleItemRegenerateStatusNone
	newSubtitle.TextTranslateStatus = consts.SubtitleItemReTranslateStatusNone
	newSubtitle.OriginSubtitleStartStr = req.OriginSubtitleStartStr
	newSubtitle.OriginSubtitleEndStr = req.OriginSubtitleEndStr
	newSubtitle.VoiceInfo = req.VoiceInfo
	newSubtitle.SubtitleStartStr = req.SubtitleStartStr
	newSubtitle.SubtitleEndStr = req.SubtitleEndStr
	newSubtitle.SpeakerId = req.SpeakerId
	newSubtitle.AudioConfig = common.NewDefaultAudioConfig()

	// 事务前执行：更新字幕项子列表
	sub_item_processor.UpdateSubtitleItemSubList(ctx, task, []*do.CommentarySubtitleItem{newSubtitle})

	// 使用事务包装写操作
	var resp *bo.AddSubtitleResBO
	err = db.GetDB().Transaction(func(tx *gorm.DB) error {
		// 后继节点偏移
		err = s.subtitleItemRepo.UpdateTranslateSubtitleItemIdxRecurrence(ctx, tid, req.ItemIdx, 1, tx)
		if err != nil {
			g.Log().Error(ctx, "AddSubtitle UpdateTranslateSubtitleItemIdxRecurrence failed", zap.Error(err), zap.Any("req", req))
			return err
		}

		// 创建新字幕项
		err = s.subtitleItemRepo.CreateSubtitleItem(ctx, newSubtitle, tx)
		if err != nil {
			g.Log().Error(ctx, "AddSubtitle CreateSubtitleItem failed", zap.Error(err), zap.Any("req", req))
			return err
		}

		// 构建响应
		resp = &bo.AddSubtitleResBO{
			SubtitleItem: conv.CommentarySubtitleItemDOToBO(newSubtitle),
		}

		return nil
	})

	if err != nil {
		g.Log().Error(ctx, "AddSubtitle transaction failed", zap.Error(err), zap.Any("req", req))
		return nil, uerrors.ErrBusy
	}

	s.subTaskSvc.MarkNeedMerge(ctx, tid)
	g.Log().Infof(ctx, "AddSubtitle success, taskId: %v, req: %v", tid, req)
	return resp, nil
}

// ModifySubtitleSpeed 修改片段语速
func (s *CommentarySubtitleItemServiceImpl) ModifySubtitleSpeed(ctx context.Context, req *bo.ModifySubtitleSpeedReqBO) (*bo.ModifySubtitleSpeedResBO, error) {
	modifyBegin := time.Now()

	// 同一个任务的创建/编辑/删除字幕操作需要加锁
	lock, lockErr := lockEditSubtitle(ctx, req.SubTaskId)
	if lockErr != nil {
		g.Log().Errorf(ctx, "ModifySubtitleSpeed failed, lockEditSubtitle error, err: %v, req: %v", lockErr, req)
		return nil, lockErr
	}
	defer func() {
		lockErr := lock.UnLock()
		if lockErr != nil {
			g.Log().Errorf(ctx, "unlock edit subtitle lock failed, err: %v, taskId: %d", lockErr, req.SubTaskId)
		}
	}()

	g.Log().Infof(ctx, "ModifySubtitleSpeed, req: %+v", req)

	// 获取子任务信息
	subTask, err := s.subtaskRepo.GetSubTaskById(ctx, req.SubTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "ModifySubtitleSpeed failed, GetSubTaskById error, err: %v, req: %v", err, req)
		return nil, uerrors.ErrSubtitleItemNoExist
	}
	if subTask == nil {
		g.Log().Errorf(ctx, "ModifySubtitleSpeed failed, subTask not found, req: %v", req)
		return nil, uerrors.ErrSubtitleItemNoExist
	}

	// 验证语速参数
	if req.Speed < consts.SpeedMin || req.Speed > consts.SpeedMax {
		g.Log().Errorf(ctx, "ModifySubtitleSpeed failed, invalid speed: %f", req.Speed)
		return nil, uerrors.ErrParam
	}

	// 根据是否应用全部片段，获取对应的片段
	var modifySubtitleItems []*do.CommentarySubtitleItem
	if req.ApplyToAll {
		modifySubtitleItems, err = s.subtitleItemRepo.GetSubtitleItemsBySubTaskId(ctx, req.SubTaskId)
		if err != nil {
			g.Log().Errorf(ctx, "ModifySubtitleSpeed failed, GetSubtitleItemsBySubTaskId error, err: %v, req: %v", err, req)
			return nil, uerrors.ErrBusy
		}
		if len(modifySubtitleItems) == 0 {
			g.Log().Errorf(ctx, "ModifySubtitleSpeed failed, no subtitle items found, req: %v", req)
			return nil, uerrors.ErrSubtitleItemNoExist
		}
	} else {
		subtitleItem, err := s.subtitleItemRepo.GetSubtitleItemByIdAndSubTaskId(ctx, req.SubtitleItemId, req.SubTaskId)
		if err != nil {
			g.Log().Errorf(ctx, "ModifySubtitleSpeed failed, GetSubtitleItemByIdAndSubTaskId error, err: %v, req: %v", err, req)
			return nil, uerrors.ErrBusy
		}
		if subtitleItem == nil {
			g.Log().Errorf(ctx, "ModifySubtitleSpeed failed, subtitleItem not found, req: %v", req)
			return nil, uerrors.ErrSubtitleItemNoExist
		}
		modifySubtitleItems = append(modifySubtitleItems, subtitleItem)
	}

	// 修改字幕项的语速配置
	for _, subtitleItem := range modifySubtitleItems {
		err := s.setItemSpeedChange(subtitleItem, req, ctx)
		if err != nil {
			g.Log().Errorf(ctx, "ModifySubtitleSpeed failed, setItemSpeedChange error, err: %v, req: %v", err, req)
			return nil, fmt.Errorf("设置语速变化失败: %w", err)
		}
	}

	// 更新字幕项的子项列表（换行信息）
	g.Log().Infof(ctx, "UpdateSubtitleItemSubList start, subTaskId: %v", req.SubTaskId)
	err = sub_item_processor.UpdateSubtitleItemSubList(ctx, subTask, modifySubtitleItems)
	g.Log().Infof(ctx, "UpdateSubtitleItemSubList end, subTaskId: %v", req.SubTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "UpdateSubtitleItemSubList failed, err: %v, subTaskId: %d", err, req.SubTaskId)
		return nil, fmt.Errorf("更新字幕子项列表失败: %w", err)
	}

	// 使用事务处理更新操作
	err = db.GetDB().Debug().Transaction(func(tx *gorm.DB) error {
		// 批量更新字幕项，只更新指定字段
		fields := []string{"audio_config", "subtitle_start_str", "subtitle_end_str", "sub_item_list"}
		for _, subtitleItem := range modifySubtitleItems {
			err = s.subtitleItemRepo.UpdateCommentarySubtitleItemTx(ctx, tx, subtitleItem, fields)
			if err != nil {
				g.Log().Errorf(ctx, "ModifySubtitleSpeed failed, UpdateCommentarySubtitleItemTx error, err: %v, subtitleItem: %v", err, subtitleItem.Id)
				return fmt.Errorf("更新字幕项失败: %w", err)
			}
		}

		return nil
	})
	if err != nil {
		g.Log().Errorf(ctx, "ModifySubtitleSpeed failed, Transaction error, err: %v, req: %v", err, req)
		return nil, fmt.Errorf("修改语速失败: %w", err)
	}

	// 标记子任务需要重新合成
	err = s.subTaskSvc.MarkNeedMerge(ctx, req.SubTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "ModifySubtitleSpeed failed, MarkNeedMerge error, err: %v, subTaskId: %v", err, req.SubTaskId)
		// 这里不返回错误，因为主要操作已经成功，只是标记合成失败
	}

	g.Log().Infof(ctx, "ModifySubtitleSpeed, cost time: %vms, subTaskId: %v", time.Since(modifyBegin).Milliseconds(), req.SubTaskId)

	// 构建响应结果
	resp := &bo.ModifySubtitleSpeedResBO{}
	for _, subtitleItem := range modifySubtitleItems {
		itemBO := conv.CommentarySubtitleItemDOToBO(subtitleItem)
		resp.SubtitleItems = append(resp.SubtitleItems, itemBO)
	}

	g.Log().Infof(ctx, "ModifySubtitleSpeed success, modified %d items, subTaskId: %v, speed: %v, applyToAll: %v", len(resp.SubtitleItems), req.SubTaskId, req.Speed, req.ApplyToAll)
	return resp, nil
}

// TextTranslate 文本翻译
func (s *CommentarySubtitleItemServiceImpl) TextTranslate(ctx context.Context, subTaskId int64, subtitmeItemId int64, customPrompt string, translateBack bool) error {
	subTask, err := s.subtaskRepo.GetSubTaskById(ctx, subTaskId)
	if err != nil {
		return fmt.Errorf("获取子任务失败: %w", err)
	}
	if subTask == nil {
		return fmt.Errorf("子任务不存在")
	}
	task, err := s.taskrepo.GetTaskById(ctx, subTask.MainTaskId)
	if err != nil {
		return fmt.Errorf("获取主任务失败: %w", err)
	}
	if task == nil {
		return fmt.Errorf("主任务不存在")
	}
	item, err := s.subtitleItemRepo.GetSubtitleItemById(ctx, subtitmeItemId)
	if err != nil {
		return fmt.Errorf("获取字幕项失败: %w", err)
	}
	if item == nil {
		return fmt.Errorf("字幕项不存在")
	}

	if !translateBack {
		item.CustomPrompt = customPrompt
		item.TextTranslateStatus = consts.SubtitleItemReTranslateStatusProcessing
		err = s.subtitleItemRepo.UpdateSubtitleItem(ctx, item)
		if err != nil {
			g.Log().Errorf(ctx, "更新翻译状态失败, itemId: %v, err: %v", item.Id, err)
			return fmt.Errorf("更新翻译状态失败: %w", err)
		}
	}

	workflowOptions := client.StartWorkflowOptions{
		ID:        fmt.Sprintf("%v_text_translate", item.Id),
		TaskQueue: config.GetConfig().Temporal.Queue,
	}
	taskBO := conv.CommentaryMainTaskDOToBO(task)
	taskBO.RequestID = ctx.Value(trace.ReqId).(string)
	subTaskBO := conv.CommentarySubTaskDOToBO(subTask)

	workflowRun, err := temporal.TemporalClient.ExecuteWorkflow(ctx, workflowOptions, "TextTranslateWorkflow", taskBO, subTaskBO, subtitmeItemId, translateBack)
	if err != nil {
		return err
	}
	g.Log().Infof(ctx, "start workflow, run_id: %+v, workflow_id:%v", workflowRun.GetRunID(), workflowRun.GetID())

	return nil
}

// GetBatchTextTranslateStatus 获取批量翻译状态
func (s *CommentarySubtitleItemServiceImpl) GetBatchSubtitleItems(ctx context.Context, subTaskId int64, subtitleItemIds []int64) ([]*bo.CommentarySubtitleItemBO, error) {
	items, err := s.subtitleItemRepo.GetSubtitleItemsBySubtitleItemIds(ctx, subtitleItemIds)
	if err != nil {
		return nil, fmt.Errorf("获取批量字幕项失败: %w", err)
	}
	ret := make([]*bo.CommentarySubtitleItemBO, 0, len(items))
	for _, item := range items {
		ret = append(ret, conv.CommentarySubtitleItemDOToBO(item))
	}

	return ret, nil
}

// GenerateVoice 生成配音
func (s *CommentarySubtitleItemServiceImpl) GenerateVoice(ctx context.Context, subTaskId int64, subtitmeItemId int64) error {
	// 同一个任务的创建/编辑/删除字幕操作需要加锁
	lock, lockErr := lockEditSubtitle(ctx, subTaskId)
	if lockErr != nil {
		g.Log().Errorf(ctx, "ModifySubtitleVolume failed, lockEditSubtitle error, err: %v, subTaskId: %v", lockErr, subTaskId)
		return lockErr
	}
	defer func() {
		lockErr := lock.UnLock()
		if lockErr != nil {
			g.Log().Errorf(ctx, "unlock edit subtitle lock failed, err: %v, subTaskId: %d", lockErr, subTaskId)
		}
	}()

	subTask, err := s.subtaskRepo.GetSubTaskById(ctx, subTaskId)
	if err != nil {
		return fmt.Errorf("获取子任务失败: %w", err)
	}
	if subTask == nil {
		return fmt.Errorf("子任务不存在")
	}
	task, err := s.taskrepo.GetTaskById(ctx, subTask.MainTaskId)
	if err != nil {
		return fmt.Errorf("获取主任务失败: %w", err)
	}
	if task == nil {
		return fmt.Errorf("主任务不存在")
	}
	item, err := s.subtitleItemRepo.GetSubtitleItemById(ctx, subtitmeItemId)
	if err != nil {
		return fmt.Errorf("获取字幕项失败: %w", err)
	}
	if item == nil {
		return fmt.Errorf("字幕项不存在")
	}

	workflowOptions := client.StartWorkflowOptions{
		ID:        fmt.Sprintf("%v_tts", item.Id),
		TaskQueue: config.GetConfig().Temporal.Queue,
	}
	taskBO := conv.CommentaryMainTaskDOToBO(task)
	taskBO.RequestID = ctx.Value(trace.ReqId).(string)
	subTaskBO := conv.CommentarySubTaskDOToBO(subTask)
	itemBO := conv.CommentarySubtitleItemDOToBO(item)

	workflowRun, err := temporal.TemporalClient.ExecuteWorkflow(ctx, workflowOptions, "GenerateVoiceWorkflow", taskBO, subTaskBO, itemBO)
	if err != nil {
		return err
	}
	g.Log().Infof(ctx, "start workflow, run_id: %+v, workflow_id:%v", workflowRun.GetRunID(), workflowRun.GetID())
	err = s.subtitleItemRepo.UpdateGenerateVoiceStatus(ctx, item.Id, consts.SubtitleItemRegenerateStatusProcessing)
	if err != nil {
		g.Log().Errorf(ctx, "更新tts状态失败, itemId: %v, err: %v", item.Id, err)
		return fmt.Errorf("更新tts状态失败: %w", err)
	}
	return nil
}

// ModifySubtitleVolume 修改片段音量
func (s *CommentarySubtitleItemServiceImpl) ModifySubtitleVolume(ctx context.Context, req *bo.ModifySubtitleVolumeReqBO) (*bo.ModifySubtitleVolumeResBO, error) {
	modifyBegin := time.Now()

	// 同一个任务的创建/编辑/删除字幕操作需要加锁
	lock, lockErr := lockEditSubtitle(ctx, req.SubTaskId)
	if lockErr != nil {
		g.Log().Errorf(ctx, "ModifySubtitleVolume failed, lockEditSubtitle error, err: %v, req: %v", lockErr, req)
		return nil, lockErr
	}
	defer func() {
		lockErr := lock.UnLock()
		if lockErr != nil {
			g.Log().Errorf(ctx, "unlock edit subtitle lock failed, err: %v, taskId: %d", lockErr, req.SubTaskId)
		}
	}()

	g.Log().Infof(ctx, "ModifySubtitleVolume, req: %+v", req)

	// 获取子任务信息
	subTask, err := s.subtaskRepo.GetSubTaskById(ctx, req.SubTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "ModifySubtitleVolume failed, GetSubTaskById error, err: %v, req: %v", err, req)
		return nil, uerrors.ErrSubtitleItemNoExist
	}
	if subTask == nil {
		g.Log().Errorf(ctx, "ModifySubtitleVolume failed, subTask not found, req: %v", req)
		return nil, uerrors.ErrSubtitleItemNoExist
	}

	// 验证音量参数
	if req.VolumeGainDB < -60.0 || req.VolumeGainDB > 10.0 {
		g.Log().Errorf(ctx, "ModifySubtitleVolume failed, invalid volume_gain_db: %f", req.VolumeGainDB)
		return nil, uerrors.ErrParam
	}

	// 根据是否应用全部片段，获取对应的片段
	var modifySubtitleItems []*do.CommentarySubtitleItem
	if req.ApplyToAll {
		modifySubtitleItems, err = s.subtitleItemRepo.GetSubtitleItemsBySubTaskId(ctx, req.SubTaskId)
		if err != nil {
			g.Log().Errorf(ctx, "ModifySubtitleVolume failed, GetSubtitleItemsBySubTaskId error, err: %v, req: %v", err, req)
			return nil, uerrors.ErrBusy
		}
		if len(modifySubtitleItems) == 0 {
			g.Log().Errorf(ctx, "ModifySubtitleVolume failed, no subtitle items found, req: %v", req)
			return nil, uerrors.ErrSubtitleItemNoExist
		}
	} else {
		subtitleItem, err := s.subtitleItemRepo.GetSubtitleItemByIdAndSubTaskId(ctx, req.SubtitleItemId, req.SubTaskId)
		if err != nil {
			g.Log().Errorf(ctx, "ModifySubtitleVolume failed, GetSubtitleItemByIdAndSubTaskId error, err: %v, req: %v", err, req)
			return nil, uerrors.ErrBusy
		}
		if subtitleItem == nil {
			g.Log().Errorf(ctx, "ModifySubtitleVolume failed, subtitleItem not found, req: %v", req)
			return nil, uerrors.ErrSubtitleItemNoExist
		}
		modifySubtitleItems = append(modifySubtitleItems, subtitleItem)
	}

	// 修改字幕项的音量配置
	for _, subtitleItem := range modifySubtitleItems {
		if subtitleItem.AudioConfig == nil {
			subtitleItem.AudioConfig = common.NewDefaultAudioConfig()
		}
		subtitleItem.AudioConfig.VolumeGainDB = req.VolumeGainDB
	}

	// 使用事务处理更新操作
	err = db.GetDB().Debug().Transaction(func(tx *gorm.DB) error {
		// 批量更新字幕项，只更新指定字段
		fields := []string{"audio_config"}
		for _, subtitleItem := range modifySubtitleItems {
			err = s.subtitleItemRepo.UpdateCommentarySubtitleItemTx(ctx, tx, subtitleItem, fields)
			if err != nil {
				g.Log().Errorf(ctx, "ModifySubtitleVolume failed, UpdateCommentarySubtitleItemTx error, err: %v, subtitleItem: %v", err, subtitleItem.Id)
				return fmt.Errorf("更新字幕项失败: %w", err)
			}
		}

		return nil
	})
	if err != nil {
		g.Log().Errorf(ctx, "ModifySubtitleVolume failed, Transaction error, err: %v, req: %v", err, req)
		return nil, fmt.Errorf("修改音量失败: %w", err)
	}

	// 标记子任务需要重新合成
	err = s.subTaskSvc.MarkNeedMerge(ctx, req.SubTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "ModifySubtitleVolume failed, MarkNeedMerge error, err: %v, subTaskId: %v", err, req.SubTaskId)
		// 这里不返回错误，因为主要操作已经成功，只是标记合成失败
	}

	g.Log().Infof(ctx, "ModifySubtitleVolume, cost time: %vms, subTaskId: %v", time.Since(modifyBegin).Milliseconds(), req.SubTaskId)

	// 构建响应结果
	resp := &bo.ModifySubtitleVolumeResBO{}
	for _, subtitleItem := range modifySubtitleItems {
		itemBO := conv.CommentarySubtitleItemDOToBO(subtitleItem)
		resp.SubtitleItems = append(resp.SubtitleItems, itemBO)
	}

	g.Log().Infof(ctx, "ModifySubtitleVolume success, modified %d items, subTaskId: %v, volumeGainDB: %v, applyToAll: %v", len(resp.SubtitleItems), req.SubTaskId, req.VolumeGainDB, req.ApplyToAll)
	return resp, nil
}

// setItemSpeedChange 设置字幕项语速变化
func (s *CommentarySubtitleItemServiceImpl) setItemSpeedChange(subtitleItem *do.CommentarySubtitleItem, req *bo.ModifySubtitleSpeedReqBO, ctx context.Context) error {
	if subtitleItem.AudioConfig == nil {
		subtitleItem.AudioConfig = common.NewDefaultAudioConfig()
	}

	// 语速改变后需要计算新的endTime，基于新语速和旧语速计算新的endTime
	newSpeed := req.Speed
	oldSpeed := subtitleItem.AudioConfig.Speed
	oldStartStr := subtitleItem.SubtitleStartStr
	oldEndStr := subtitleItem.SubtitleEndStr

	// 根据语速计算新的结束时间
	newEndStr, err := s.computeSubtitleNewEndTimeStrBySpeed(ctx, oldStartStr, oldEndStr, oldSpeed, newSpeed)
	if err != nil {
		g.Log().Errorf(ctx, "ModifySubtitleSpeed failed, computeSubtitleNewEndTimeStrBySpeed error, err: %v, req: %v", err, req)
		return fmt.Errorf("计算新结束时间失败: %w", err)
	}

	subtitleItem.SubtitleEndStr = newEndStr
	subtitleItem.AudioConfig.Speed = newSpeed

	return nil
}

// computeSubtitleNewEndTimeStrBySpeed 根据语速计算新的结束时间
func (s *CommentarySubtitleItemServiceImpl) computeSubtitleNewEndTimeStrBySpeed(ctx context.Context, oldStartStr string, oldEndStr string, oldSpeed float32, newSpeed float32) (res string, err error) {
	oldStartMs, err := srt_util.SrtStringTime2MsDot(oldStartStr)
	if err != nil {
		g.Log().Errorf(ctx, "split SubtitleStartStrs: %s, err: %v", oldStartStr, err)
		return
	}
	oldEndMs, err := srt_util.SrtStringTime2MsDot(oldEndStr)
	if err != nil {
		g.Log().Errorf(ctx, "split SubtitleEndStrs: %s, err: %v", oldEndStr, err)
		return
	}

	oldDuration := oldEndMs - oldStartMs
	newDuration := (float64(oldSpeed) / float64(newSpeed)) * float64(oldDuration)
	newEndMs := oldStartMs + int64(newDuration)
	newEndStr := srt_util.Ms2CommaStringTime(newEndMs)

	g.Log().Infof(ctx, "computeSubtitleNewEndTimeStrBySpeed success, oldStartStr: %s, oldEndStr: %s, newEndStr: %s, oldSpeed: %f, newSpeed: %f", oldStartStr, oldEndStr, newEndStr, oldSpeed, newSpeed)
	return newEndStr, nil
}

// 拆分时间戳片段
func (s *CommentarySubtitleItemServiceImpl) splitOriginStr(ctx context.Context, originText, before, after string, subtitleUp *do.CommentarySubtitleItem, subtitleDown *do.CommentarySubtitleItem) {
	if subtitleUp.OriginSubtitleStartStr == "" || subtitleUp.OriginSubtitleEndStr == "" {
		// 如果原文起止时间为空，直接赋值译文的起止时间戳
		subtitleUp.OriginSubtitleStartStr = subtitleUp.SubtitleStartStr
		subtitleUp.OriginSubtitleEndStr = subtitleUp.SubtitleEndStr
	}

	// 根据百分比计算时间戳
	startMs, err := srt_util.SRTTimeStrToFloatSeconds(subtitleUp.OriginSubtitleStartStr)
	if err != nil {
		g.Log().Errorf(ctx, "SRTTimeStrToFloatSeconds error: %v", err)
	}
	endMs, err := srt_util.SRTTimeStrToFloatSeconds(subtitleUp.OriginSubtitleEndStr)
	if err != nil {
		g.Log().Errorf(ctx, "SRTTimeStrToFloatSeconds error: %v", err)
	}
	midMs := srt_util.SplitOriginByTextMiddleTime(before, after, startMs, endMs)
	subtitleUp.OriginSubtitleStartStr = srt_util.FloatSecondsToSRTTimeStr(startMs)
	subtitleUp.OriginSubtitleEndStr = srt_util.FloatSecondsToSRTTimeStr(midMs)
	subtitleDown.OriginSubtitleStartStr = srt_util.FloatSecondsToSRTTimeStr(midMs)
	subtitleDown.OriginSubtitleEndStr = srt_util.FloatSecondsToSRTTimeStr(endMs)

	g.Log().Infof(ctx, "splitOriginStr result: subtitleUp[%s-%s], subtitleDown[%s-%s]",
		subtitleUp.OriginSubtitleStartStr, subtitleUp.OriginSubtitleEndStr,
		subtitleDown.OriginSubtitleStartStr, subtitleDown.OriginSubtitleEndStr)
}

// isSubtitleProcessing 判断字幕项是否正在处理中
func (s *CommentarySubtitleItemServiceImpl) isSubtitleProcessing(subtitle *do.CommentarySubtitleItem) bool {
	if subtitle.GenerateVoiceStatus == consts.SubtitleItemRegenerateStatusProcessing {
		return true
	}
	return false
}

// isSubtitleNeedReTranslate 判断字幕项是否需要重新翻译
func (s *CommentarySubtitleItemServiceImpl) isSubtitleNeedReTranslate(subtitle *do.CommentarySubtitleItem) bool {
	// 如果没有最新翻译信息，说明是第一次，需要翻译
	if subtitle.LatestTextTranslateInfo == nil {
		return subtitle.OriginSubtitle != ""
	}

	// 如果原文发生变化，需要重新翻译
	if subtitle.LatestTextTranslateInfo.LatestOriginSubtitle != subtitle.OriginSubtitle {
		return true
	}

	return false
}

// isSubtitleItemNeedRegenerate 判断字幕项是否需要重新配音
func (s *CommentarySubtitleItemServiceImpl) isSubtitleItemNeedRegenerate(subtitle *do.CommentarySubtitleItem) bool {
	// 如果没有最新TTS信息，说明是第一次，需要配音
	if subtitle.LatestTTSInfo == nil || subtitle.LatestTTSInfo.IsEmpty() {
		return true
	}

	// 如果译文发生变化，需要重新配音
	if subtitle.LatestTTSInfo.TargetSubtitle != subtitle.TargetSubtitle {
		return true
	}

	return false
}

// isVoiceInfoEqual 比较两个音色信息是否相等
func (s *CommentarySubtitleItemServiceImpl) isVoiceInfoEqual(voice1, voice2 *common.VoiceInfo) bool {
	if voice1 == nil && voice2 == nil {
		return true
	}
	if voice1 == nil || voice2 == nil {
		return false
	}

	return voice1.VoiceId == voice2.VoiceId &&
		voice1.VoiceSource == voice2.VoiceSource &&
		voice1.Name == voice2.Name &&
		voice1.Url == voice2.Url
}
