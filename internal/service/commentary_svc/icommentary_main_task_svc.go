package commentary_svc

import (
	"business-workflow/internal/entity/bo"
	"context"
)

// ICommentaryMainTaskService 解说主任务服务接口
type ICommentaryMainTaskService interface {
	// CreateTask 创建解说主任务
	CreateTask(ctx context.Context, req *bo.CommentaryMainTaskCreateReqBO) (*bo.CommentaryMainTaskCreateResBO, error)

	// GetCommentaryTaskHistory 获取解说任务信息
	GetCommentaryTaskHistory(ctx context.Context, req *bo.GetCommentaryTaskHistoryReqBO) ([]*bo.CommentaryTaskHistoryBO, error)

	// GetTaskById 根据ID获取任务详情
	GetTaskById(ctx context.Context, id int64) (*bo.CommentaryMainTaskDetailBO, error)

	// DeleteTask 删除任务
	DeleteTask(ctx context.Context, id int64) error

	// BatchGetCommentaryTasksByOrderIds 根据订单ID批量获取解说任务
	BatchGetCommentaryTasksByOrderIds(ctx context.Context, req *bo.BatchGetCommentaryTasksByOrderIdsReqBO) (*bo.BatchGetCommentaryTasksByOrderIdsResBO, error)
}
