package commentary_svc

import (
	"business-workflow/internal/entity/bo"
	"context"
)

// ICommentarySubTaskService 解说子任务服务接口
type ICommentarySubTaskService interface {

	// GetSubTasksByMainTaskId 根据主任务ID获取子任务列表
	GetSubTasksByMainTaskId(ctx context.Context, mainTaskId int64) ([]*bo.CommentarySubTaskBO, error)

	// GetSubTaskById 根据ID获取子任务详情
	GetSubTaskById(ctx context.Context, id int64) (*bo.CommentarySubTaskBO, error)

	// GetSubTaskListByIds 批量更加ID获取子任务详情
	GetSubTaskListByIds(ctx context.Context, idList []int64) ([]*bo.CommentarySubTaskBO, error)

	// GetSubTaskWithSubtitles 根据ID获取子任务详情及字幕项列表
	GetSubTaskWithSubtitles(ctx context.Context, id int64) (*bo.CommentarySubTaskBO, error)

	// UpdateSubTaskStatus 更新子任务状态
	UpdateSubTaskStatus(ctx context.Context, req *bo.UpdateSubTaskStatusReqBO) (*bo.UpdateSubTaskStatusResBO, error)

	// DeleteSubTask 删除子任务
	DeleteSubTask(ctx context.Context, req *bo.DeleteSubTaskReqBO) (*bo.DeleteSubTaskResBO, error)

	// UpdateSubTaskBgm 更新子任务背景音
	UpdateSubTaskBgm(ctx context.Context, req *bo.UpdateSubTaskBgmReqBO) (*bo.UpdateSubTaskBgmResBO, error)

	// UpdateSubTaskBgmMode 更新子任务BGM模式
	UpdateSubTaskBgmMode(ctx context.Context, req *bo.UpdateSubTaskBgmModeReqBO) (*bo.UpdateSubTaskBgmModeResBO, error)

	// GetEditOperationLogsByDeductOrderIds 通过扣费订单ID列表批量获取编辑操作流水
	GetEditOperationLogsByDeductOrderIds(ctx context.Context, deductOrderIds []string) ([]*bo.CommentaryEditOperationLogBO, error)

	// MarkNeedMerge 标记子任务需要重新合成
	MarkNeedMerge(ctx context.Context, subTaskId int64) error

	// SaveGlobalSubtitleStyle 保存全局字幕样式
	SaveGlobalSubtitleStyle(ctx context.Context, req *bo.SaveGlobalSubtitleStyleReqBO) (*bo.SaveGlobalSubtitleStyleResBO, error)

	// SaveSubtitleMode 保存字幕模式
	SaveSubtitleMode(ctx context.Context, req *bo.SaveSubtitleModeReqBO) (*bo.SaveSubtitleModeResBO, error)

	// RenameSubtitle 重命名任务
	RenameSubtitle(ctx context.Context, id int64, newName string) (*bo.CommentarySubTaskBO, error)

	//MergeBatch 批量合成
	MergeBatch(ctx context.Context, appId int64, tenantId int64, requestId string, ids []int64) error

	MockInpaint(ctx context.Context, videoUrls []string) (taskIds []string, err error)

	// GetVideoPostUrl 获取视频上传地址
	GetVideoPostUrl(ctx context.Context, id int64) (string, error)

	MockSubtitleMerge(ctx context.Context, taskIds []int64) (err error)
}
