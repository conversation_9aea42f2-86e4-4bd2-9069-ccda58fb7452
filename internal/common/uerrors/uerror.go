package uerrors

import "errors"

type CommentaryErr struct {
	Msg     string
	RetCode int
}

func (e *CommentaryErr) Error() string {
	return e.Msg
}

func NewGfError(text string, code int) *CommentaryErr {
	return &CommentaryErr{Msg: text, RetCode: code}
}

var (
	// ErrBusy = &CommentaryErr{Err: VideoTranslateCode{Msg: "服务繁忙，请重试", RetCode: 1002}}
	ErrBusy                    = NewGfError("The service is busy", 1002)
	ErrParam                   = NewGfError("Parameter error", 1003)
	ErrRemoteObjectNotExist    = NewGfError("Object does not exist", 1005)
	ErrRemoteObjectSetPublic   = NewGfError("Failed to set public access", 1006)
	ErrUnSupportLangId         = NewGfError("Unsupported language", 1007)
	ErrTaskNoExist             = NewGfError("Task does not exist", 1008)
	ErrTaskMergeForbidden      = NewGfError("The current state cannot be synthesized", 1009)
	ErrSubtitleItemNoExist     = NewGfError("Subtitles not present", 1010)
	ErrTranslateBusy           = NewGfError("Translation service busy", 1011)
	ErrMergeServiceBusy        = NewGfError("Video synthesis busy", 1012)
	ErrTaskOpForbidden         = NewGfError("The operation is not allowed in the current state", 1013)
	ErrTaskUnsupportedLanguage = NewGfError("Unsupported language types", 1015)
	ErrTaskTooManySubDirs      = NewGfError("Too many subdirectories", 1016)
	ErrDirNoExist              = NewGfError("Directory does not exist", 1017)
	ErrTaskInComplete          = NewGfError("An unfinished task cannot be deleted", 1018)
	ErrTaskLangInConsistent    = NewGfError("The target language is inconsistent", 1019)
	ErrTaskDirInConsistent     = NewGfError("The destination directory is inconsistent", 1020)
	ErrTaskLandIdNotExist      = NewGfError("Target language not found", 1021)
	ErrTaskResTooMuch          = NewGfError("Too many results", 1022)
	ErrUnSupportModel          = NewGfError("Model not supported", 1023)
	ErrUnSupportFullErase      = NewGfError("The current version does not support full screen erasure", 1025)
	ErrNewLangCounterNone      = NewGfError("There are no transferable tasks", 1026)
	ErrParamUserIdNotMatch     = NewGfError("Parameter error 3", 1027)
	ErrAuditFailed             = NewGfError("Audit violation", 1028)
	ErrGenerateAudioFailed     = NewGfError("Failed to generate audio", 1029)
	ErrTimeout                 = NewGfError("request timeout", 1030)
	ErrGenerateVoiceBusy       = NewGfError("Generated audio busy", 1031)
	ErrNotFoundSubtitleItem    = NewGfError("No subtitle entry found", 1032)
	ErrAuditRefused            = NewGfError("The uploaded content contains sensitive material. Please review and regenerate", 1033)
	ErrTaskMergeTimeout        = NewGfError("Task composition timeout", 1034)
	ErrTaskVideoMerging        = NewGfError("The video is being synthesized", 1035)
	ErrTaskVideoAuditTimeout   = NewGfError("Video audit timeout", 1036)
	ErrAuditErrorOccur         = NewGfError("Audit error", 1037)
	ErrRegenerating            = NewGfError("Being regenerated", 1038)
	// file data format is incorrect
	ErrFileDataFormat        = NewGfError("File data format is incorrect", 1039)
	ErrSpeakerSegmentOverlap = NewGfError("Speaker segment overlap", 1040) // 相同说话人新建配音片段与已有片段重叠
	ErrAuthorizeFailed       = NewGfError("Authorization failed", 1041)    // 授权失败

	ErrVideoWithoutSubtitle = NewGfError("The selected video has no subtitles, Please edit and try again.", 1042)
	ErrSubtitleToLong       = NewGfError("merged subtitle too long", 1043)

	ErrNoPermission  = NewGfError("No access", 4000)
	ErrTokenNotFound = NewGfError("Token illegal", 4001)

	ErrNotEnoughBalance = NewGfError("Description Failed to create a task because the available duration exceeded", 2002)
	//
	ErrNotEnoughBalancePartialFailure = NewGfError("Some tasks failed to be created because the available duration exceeded. Procedure", 2001)
	ErrDiskUsageIntercept             = NewGfError("insufficient disk space", 2003)

	ErrBalanceInsufficient = NewGfError("Insufficient balance", 2004)
	ErrPostUrlNotFound     = NewGfError("Post url not found", 2005)
	ErrTooManyRequests     = NewGfError("Too many requests, please try again later", 2008)
	ErrCantMoveDirToSelf   = NewGfError("Operation failed, cannot move directory to itself or its child directory", 2007) // 操作失败，无法将目录移动到自身及其子目录下
	ErrSearchKeyTooLong    = NewGfError("Search key too long", 2009)                                                      // 搜索关键字长度超过50
	ErrMainTaskNotMatch    = NewGfError("跨任务合并", 40001)                                                                   // 跨任务合并
	ErrForbiddenForTenant  = NewGfError("任务不存在", 40002)                                                                   // 禁止访问他人租户
	ErrTaskNotFound        = NewGfError("任务不存在", 40003)                                                                   // 任务不存在
)

const (
	ENGINE_ERR_BALANCE_INSUFFICIENT = 4006 // 余额不足
)

func (e *CommentaryErr) Is(target error) bool {
	if te, ok := target.(*CommentaryErr); ok {
		return e.RetCode == te.RetCode
	}
	return false
}
func ConvertError(err error) *CommentaryErr {
	var e *CommentaryErr
	if errors.As(err, &e) {
		return e
	}
	return NewGfError(err.Error(), 1000)
}
