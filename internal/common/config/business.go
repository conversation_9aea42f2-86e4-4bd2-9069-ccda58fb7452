package config

import (
	"context"
	"sync/atomic"

	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/tyr/x/configuration"
)

type BusinessConfig struct {
	OpenPhonemeAlign            bool `json:"open_phoneme_align"`
	OpenPreprocessOmniEngine    bool `json:"open_preprocess_omni_engine"`
	OpenTTSOmniEngine           bool `json:"open_tts_omni_engine"`
	OpenTTSCorrectionOmniEngine bool `json:"open_tts_correction_omni_engine"`
}

var loader *atomic.Value

func InitBusinessConfig() error {
	// Initialize the configuration for the business logic
	bc := &BusinessConfig{}
	var err error
	loader, err = configuration.SmartLoad("/data/cicd-dy-conf/ser/business-workflow.json", bc)
	if err != nil {
		return err
	}
	g.Log().Infof(context.TODO(), "load business config success, config: %v", GetBC())
	return nil
}

func GetBC() *BusinessConfig {
	config := loader.Load().(*BusinessConfig)
	if config == nil {
		return &BusinessConfig{}
	}
	return config
}
