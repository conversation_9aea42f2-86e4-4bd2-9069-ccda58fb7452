package hls

import (
	"business-workflow/internal/common/config"
	"business-workflow/internal/util"
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/samber/lo"
	"strconv"
	"strings"
)

func CallVideoTranslateBatchVideoConvertHls(ctx context.Context, req interface{}, res interface{}, timeoutInSeconds int) error {
	commonHeaders := map[string]string{
		"Content-Type": "application/json",
	}
	g.Log().Infof(ctx, "CallVideoTranslateBatchVideoConvertHls, req: %+v", req)
	return util.HttpPostReqWithResponseWithTimeout(ctx, config.GetConfig().VideoTranslate.Address+"/v1/translate_api/subtitle_translate/batch_video_convert_hls", req, res, commonHeaders, timeoutInSeconds)
}

type VideoConvertHlsReq struct {
	Token   string   `json:"token"`
	Tids    []uint64 `json:"tids"`
	BizType int      `json:"biz_type"`
}

func SendHlsRequest(ctx context.Context, ids []string) error {
	res := map[string]any{}
	req := &VideoConvertHlsReq{
		Token: "feishu_video_convert_hls_&_1.2.0",
		Tids: lo.Map(ids, func(item string, index int) uint64 {
			tid, err := strconv.ParseUint(strings.TrimSpace(item), 10, 64)
			if err != nil {
				return 0
			}
			return tid
		}),
		BizType: 1,
	}
	err := CallVideoTranslateBatchVideoConvertHls(ctx, req, &res, 60)
	if err != nil {
		return err
	}
	return nil
}
