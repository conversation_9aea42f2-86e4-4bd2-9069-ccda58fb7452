package ffmpeg

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"
)

// TestConcurrencyControl 测试并发控制功能
func TestConcurrencyControl(t *testing.T) {
	// 重置信号量以确保测试的独立性
	ffmpegSemaphore = nil
	initOnce = sync.Once{}
	maxConcurrency = 0

	ctx := context.Background()

	// 测试基本的获取和释放
	t.Run("BasicAcquireRelease", func(t *testing.T) {
		err := acquireSemaphore(ctx, 1*time.Second)
		if err != nil {
			t.Fatalf("获取信号量失败: %v", err)
		}
		releaseSemaphore()
	})

	// 测试并发限制
	t.Run("ConcurrencyLimit", func(t *testing.T) {
		var wg sync.WaitGroup
		successCount := 0
		timeoutCount := 0
		var mu sync.Mutex

		// 获取当前的并发限制数量
		initSemaphore()
		currentLimit := maxConcurrency
		t.Logf("当前并发限制: %d", currentLimit)

		// 启动 currentLimit+2 个goroutine，但只有 currentLimit 个应该能同时获得许可
		totalGoroutines := currentLimit + 2
		for i := 0; i < totalGoroutines; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()

				err := acquireSemaphore(ctx, 100*time.Millisecond)
				if err != nil {
					mu.Lock()
					timeoutCount++
					mu.Unlock()
					t.Logf("Goroutine %d 超时: %v", id, err)
					return
				}

				mu.Lock()
				successCount++
				mu.Unlock()

				t.Logf("Goroutine %d 获得许可", id)

				// 模拟工作
				time.Sleep(200 * time.Millisecond)

				releaseSemaphore()
				t.Logf("Goroutine %d 释放许可", id)
			}(i)
		}

		wg.Wait()

		// 应该有4个成功，2个超时
		if successCount != 4 {
			t.Errorf("期望4个成功，实际: %d", successCount)
		}
		if timeoutCount != 2 {
			t.Errorf("期望2个超时，实际: %d", timeoutCount)
		}
	})

	// 测试超时功能
	t.Run("TimeoutTest", func(t *testing.T) {
		// 先占满所有许可
		for i := 0; i < 4; i++ {
			err := acquireSemaphore(ctx, 1*time.Second)
			if err != nil {
				t.Fatalf("获取信号量失败: %v", err)
			}
		}

		// 现在尝试获取第5个许可，应该超时
		start := time.Now()
		err := acquireSemaphore(ctx, 500*time.Millisecond)
		elapsed := time.Since(start)

		if err == nil {
			t.Error("期望超时错误，但获取成功了")
		}

		if elapsed < 400*time.Millisecond || elapsed > 600*time.Millisecond {
			t.Errorf("超时时间不正确，期望约500ms，实际: %v", elapsed)
		}

		// 释放所有许可
		for i := 0; i < 4; i++ {
			releaseSemaphore()
		}
	})
}

// BenchmarkConcurrencyControl 性能测试
func BenchmarkConcurrencyControl(b *testing.B) {
	// 重置信号量
	ffmpegSemaphore = make(chan struct{}, 4)
	initOnce = sync.Once{}

	ctx := context.Background()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			err := acquireSemaphore(ctx, 1*time.Second)
			if err != nil {
				b.Errorf("获取信号量失败: %v", err)
				continue
			}
			// 模拟短暂工作
			time.Sleep(1 * time.Millisecond)
			releaseSemaphore()
		}
	})
}

// TestConcurrencyControlWithRealScenario 模拟真实场景的测试
func TestConcurrencyControlWithRealScenario(t *testing.T) {
	// 重置信号量
	ffmpegSemaphore = make(chan struct{}, 4)
	initOnce = sync.Once{}

	ctx := context.Background()

	// 模拟10个视频剪切任务同时提交
	var wg sync.WaitGroup
	results := make([]string, 10)

	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(taskId int) {
			defer wg.Done()

			start := time.Now()
			err := acquireSemaphore(ctx, 2*time.Minute)
			waitTime := time.Since(start)

			if err != nil {
				results[taskId] = fmt.Sprintf("Task %d: 超时 (等待时间: %v)", taskId, waitTime)
				return
			}

			// 模拟ffmpeg处理时间
			processingTime := time.Duration(100+taskId*50) * time.Millisecond
			time.Sleep(processingTime)

			releaseSemaphore()

			totalTime := time.Since(start)
			results[taskId] = fmt.Sprintf("Task %d: 成功 (等待: %v, 处理: %v, 总计: %v)",
				taskId, waitTime, processingTime, totalTime)
		}(i)
	}

	wg.Wait()

	// 打印结果
	for _, result := range results {
		t.Log(result)
	}

	// 验证没有任务超时（在2分钟内应该都能完成）
	for i, result := range results {
		if result == "" {
			t.Errorf("Task %d 没有结果", i)
		}
		if fmt.Sprintf("Task %d: 超时", i) == result[:len(fmt.Sprintf("Task %d: 超时", i))] {
			t.Errorf("Task %d 意外超时: %s", i, result)
		}
	}
}
