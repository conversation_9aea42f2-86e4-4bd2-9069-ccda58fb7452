package ffmpeg

import (
	"errors"
	"testing"
)

func TestErrorHandling(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected string
	}{
		{
			name:     "并发超时错误",
			err:      errors.New("获取ffmpeg并发许可超时，等待时间: 5m0s"),
			expected: "concurrency_timeout",
		},
		{
			name:     "资源不足错误 - signal killed",
			err:      errors.New("ffmpeg 执行失败: signal: killed"),
			expected: "resource_shortage",
		},
		{
			name:     "资源不足错误 - exit status 137",
			err:      errors.New("ffmpeg 执行失败: exit status 137"),
			expected: "resource_shortage",
		},
		{
			name:     "资源不足错误 - out of memory",
			err:      errors.New("ffmpeg 执行失败: out of memory"),
			expected: "resource_shortage",
		},
		{
			name:     "普通FFmpeg错误",
			err:      errors.New("ffmpeg 执行失败: invalid format"),
			expected: "other",
		},
		{
			name:     "空错误",
			err:      nil,
			expected: "none",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var result string

			if tt.err == nil {
				result = "none"
			} else if IsConcurrencyTimeoutErr(tt.err) {
				result = "concurrency_timeout"
			} else if IsResourceShortageErr(tt.err) {
				result = "resource_shortage"
			} else {
				result = "other"
			}

			if result != tt.expected {
				t.Errorf("错误类型判断错误，期望: %s, 实际: %s, 错误: %v", tt.expected, result, tt.err)
			}
		})
	}
}

func TestIsConcurrencyTimeoutErr(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{
			name:     "并发超时错误",
			err:      errors.New("获取ffmpeg并发许可超时，等待时间: 5m0s"),
			expected: true,
		},
		{
			name:     "其他错误",
			err:      errors.New("ffmpeg 执行失败"),
			expected: false,
		},
		{
			name:     "空错误",
			err:      nil,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsConcurrencyTimeoutErr(tt.err)
			if result != tt.expected {
				t.Errorf("IsConcurrencyTimeoutErr() = %v, 期望 %v", result, tt.expected)
			}
		})
	}
}

func TestIsResourceShortageErr(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{
			name:     "signal killed",
			err:      errors.New("signal: killed"),
			expected: true,
		},
		{
			name:     "exit status 137",
			err:      errors.New("exit status 137"),
			expected: true,
		},
		{
			name:     "Killed",
			err:      errors.New("Killed"),
			expected: true,
		},
		{
			name:     "out of memory",
			err:      errors.New("out of memory"),
			expected: true,
		},
		{
			name:     "Cannot allocate memory",
			err:      errors.New("Cannot allocate memory"),
			expected: true,
		},
		{
			name:     "其他错误",
			err:      errors.New("invalid format"),
			expected: false,
		},
		{
			name:     "空错误",
			err:      nil,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsResourceShortageErr(tt.err)
			if result != tt.expected {
				t.Errorf("IsResourceShortageErr() = %v, 期望 %v", result, tt.expected)
			}
		})
	}
}

func TestIsCutTimeoutErr(t *testing.T) {
	// 测试向后兼容性
	err := errors.New("获取ffmpeg并发许可超时，等待时间: 5m0s")

	// IsCutTimeoutErr 应该和 IsConcurrencyTimeoutErr 返回相同结果
	result1 := IsCutTimeoutErr(err)
	result2 := IsConcurrencyTimeoutErr(err)

	if result1 != result2 {
		t.Errorf("IsCutTimeoutErr() 和 IsConcurrencyTimeoutErr() 结果不一致: %v vs %v", result1, result2)
	}

	if !result1 {
		t.Errorf("IsCutTimeoutErr() 应该识别超时错误")
	}
}
