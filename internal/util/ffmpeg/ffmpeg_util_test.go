package ffmpeg

import (
	"fmt"
	"os"
	"os/exec"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

// 测试辅助函数：创建临时测试视频文件
func createTestVideoFile(filename string, hasAudio bool) error {
	// 创建一个简单的测试视频文件
	var cmd *exec.Cmd
	if hasAudio {
		// 创建带音频的测试视频（1秒，黑色画面，440Hz音频）
		cmd = exec.Command("ffmpeg",
			"-f", "lavfi",
			"-i", "color=c=black:s=320x240:d=1",
			"-f", "lavfi",
			"-i", "sine=frequency=440:duration=1",
			"-c:v", "libx264",
			"-c:a", "aac",
			"-shortest",
			"-y", filename)
	} else {
		// 创建无音频的测试视频（1秒，黑色画面）
		cmd = exec.Command("ffmpeg",
			"-f", "lavfi",
			"-i", "color=c=black:s=320x240:d=1",
			"-c:v", "libx264",
			"-y", filename)
	}

	return cmd.Run()
}

// 测试辅助函数：创建临时测试音频文件
func createTestAudioFile(filename string, durationSec float64) error {
	// 创建一个简单的测试音频文件（440Hz正弦波）
	cmd := exec.Command("ffmpeg",
		"-f", "lavfi",
		"-i", fmt.Sprintf("sine=frequency=440:duration=%.3f", durationSec),
		"-c:a", "aac",
		"-y", filename)

	return cmd.Run()
}

// 测试辅助函数：检查文件是否存在
func fileExists(filename string) bool {
	_, err := os.Stat(filename)
	return !os.IsNotExist(err)
}

// 测试辅助函数：清理测试文件
func cleanupTestFiles(files ...string) {
	for _, file := range files {
		os.Remove(file)
	}
}

// 测试辅助函数：检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 ||
		func() bool {
			for i := 0; i <= len(s)-len(substr); i++ {
				if s[i:i+len(substr)] == substr {
					return true
				}
			}
			return false
		}())
}

// 测试辅助函数：检查浮点数是否近似相等
func almostEqual(a, b, tolerance float64) bool {
	diff := a - b
	if diff < 0 {
		diff = -diff
	}
	return diff <= tolerance
}

//
//func TestMergeVideosByConcat(t *testing.T) {
//	t.Run("空输入文件列表", func(t *testing.T) {
//		output := "empty_output.mp4"
//		defer cleanupTestFiles(output)
//
//		// 执行测试
//		inputFiles := []string{}
//		err := MergeVideosByConcat(inputFiles, output)
//
//		// 验证结果
//		if err == nil {
//			t.Error("期望返回错误，但得到 nil")
//		}
//		if err != nil && !contains(err.Error(), "输入文件列表为空") {
//			t.Errorf("期望错误信息包含 '输入文件列表为空'，但得到: %v", err)
//		}
//		if fileExists(output) {
//			t.Error("期望输出文件不存在，但文件存在")
//		}
//	})
//
//	t.Run("不存在的输入文件", func(t *testing.T) {
//		output := "nonexistent_output.mp4"
//		defer cleanupTestFiles(output)
//
//		// 执行测试
//		inputFiles := []string{"nonexistent_file.mp4"}
//		err := MergeVideosByConcat(inputFiles, output)
//
//		// 验证结果
//		if err == nil {
//			t.Error("期望返回错误，但得到 nil")
//		}
//		if fileExists(output) {
//			t.Error("期望输出文件不存在，但文件存在")
//		}
//	})
//
//	t.Run("输出文件路径无效", func(t *testing.T) {
//		// 准备测试文件
//		video := "test_error_video.mp4"
//		invalidOutput := "/invalid/path/that/does/not/exist/output.mp4"
//
//		// 创建测试视频文件
//		err := createTestVideoFile(video, true)
//
//		// 如果无法创建测试文件，跳过测试
//		if err != nil {
//			t.Skip("跳过测试：无法创建测试视频文件，可能缺少ffmpeg")
//		}
//
//		defer cleanupTestFiles(video)
//
//		// 执行测试
//		inputFiles := []string{video}
//		err = MergeVideosByConcat(inputFiles, invalidOutput)
//
//		// 验证结果
//		if err == nil {
//			t.Error("期望返回错误，但得到 nil")
//		}
//		if fileExists(invalidOutput) {
//			t.Error("期望输出文件不存在，但文件存在")
//		}
//	})
//}

// TestHasAudioStream 测试音频流检测函数
func TestHasAudioStream(t *testing.T) {
	t.Run("检测有音频的视频文件", func(t *testing.T) {
		video := "test_has_audio.mp4"

		// 创建带音频的测试视频文件
		err := createTestVideoFile(video, true)

		// 如果无法创建测试文件，跳过测试
		if err != nil {
			t.Skip("跳过测试：无法创建测试视频文件，可能缺少ffmpeg")
		}

		defer cleanupTestFiles(video)

		// 执行测试
		hasAudio, err := HasAudioStream(video)

		// 验证结果
		if err != nil {
			t.Errorf("期望 err 为 nil，但得到: %v", err)
		}
		if !hasAudio {
			t.Error("期望 hasAudio 为 true，但得到 false")
		}
	})

	t.Run("检测无音频的视频文件", func(t *testing.T) {
		video := "test_no_audio.mp4"

		// 创建无音频的测试视频文件
		err := createTestVideoFile(video, false)

		// 如果无法创建测试文件，跳过测试
		if err != nil {
			t.Skip("跳过测试：无法创建测试视频文件，可能缺少ffmpeg")
		}

		defer cleanupTestFiles(video)

		// 执行测试
		hasAudio, err := HasAudioStream(video)

		// 验证结果
		if err != nil {
			t.Errorf("期望 err 为 nil，但得到: %v", err)
		}
		if hasAudio {
			t.Error("期望 hasAudio 为 false，但得到 true")
		}
	})

	t.Run("检测不存在的文件", func(t *testing.T) {
		// 执行测试
		hasAudio, err := HasAudioStream("nonexistent_file.mp4")

		// 验证结果 - 函数设计为不存在文件时返回false, nil
		if err != nil {
			t.Errorf("期望 err 为 nil，但得到: %v", err)
		}
		if hasAudio {
			t.Error("期望 hasAudio 为 false，但得到 true")
		}
	})
}

// TestProbeVideoInfo 测试视频信息探测函数
func TestProbeVideoInfo(t *testing.T) {
	t.Run("探测有音频视频文件的信息", func(t *testing.T) {
		video := "test_probe_with_audio.mp4"

		// 创建带音频的测试视频文件
		err := createTestVideoFile(video, true)

		// 如果无法创建测试文件，跳过测试
		if err != nil {
			t.Skip("跳过测试：无法创建测试视频文件，可能缺少ffmpeg")
		}

		defer cleanupTestFiles(video)

		// 执行测试
		info, err := ProbeVideoInfo(video)

		// 验证结果
		if err != nil {
			t.Errorf("期望 err 为 nil，但得到: %v", err)
		}
		if info == nil {
			t.Fatal("期望 info 不为 nil，但得到 nil")
		}
		if info.Width != 320 {
			t.Errorf("期望 Width 为 320，但得到: %d", info.Width)
		}
		if info.Height != 240 {
			t.Errorf("期望 Height 为 240，但得到: %d", info.Height)
		}
		if !info.HasAudio {
			t.Error("期望 HasAudio 为 true，但得到 false")
		}
		if info.DurationSec <= 0 {
			t.Errorf("期望 DurationSec 大于 0，但得到: %f", info.DurationSec)
		}
		if info.InputFile != video {
			t.Errorf("期望 InputFile 为 %s，但得到: %s", video, info.InputFile)
		}
	})

	t.Run("探测无音频视频文件的信息", func(t *testing.T) {
		video := "test_probe_no_audio.mp4"

		// 创建无音频的测试视频文件
		err := createTestVideoFile(video, false)

		// 如果无法创建测试文件，跳过测试
		if err != nil {
			t.Skip("跳过测试：无法创建测试视频文件，可能缺少ffmpeg")
		}

		defer cleanupTestFiles(video)

		// 执行测试
		info, err := ProbeVideoInfo(video)

		// 验证结果
		if err != nil {
			t.Errorf("期望 err 为 nil，但得到: %v", err)
		}
		if info == nil {
			t.Fatal("期望 info 不为 nil，但得到 nil")
		}
		if info.Width != 320 {
			t.Errorf("期望 Width 为 320，但得到: %d", info.Width)
		}
		if info.Height != 240 {
			t.Errorf("期望 Height 为 240，但得到: %d", info.Height)
		}
		if info.HasAudio {
			t.Error("期望 HasAudio 为 false，但得到 true")
		}
		if info.DurationSec <= 0 {
			t.Errorf("期望 DurationSec 大于 0，但得到: %f", info.DurationSec)
		}
		if info.InputFile != video {
			t.Errorf("期望 InputFile 为 %s，但得到: %s", video, info.InputFile)
		}
	})

	t.Run("探测不存在文件的信息", func(t *testing.T) {
		// 执行测试
		info, err := ProbeVideoInfo("nonexistent_file.mp4")

		// 验证结果
		if err == nil {
			t.Error("期望返回错误，但得到 nil")
		}
		if info != nil {
			t.Error("期望 info 为 nil，但得到非 nil 值")
		}
	})
}

// TestProcessAudioDuration 测试音频时长处理函数
func TestProcessAudioDuration(t *testing.T) {
	t.Run("音频时长等于目标时长（直接复制）", func(t *testing.T) {
		inputAudio := "test_audio_equal.aac"
		outputAudio := "test_output_equal.aac"
		targetDuration := 3.0

		// 创建3秒的测试音频文件
		err := createTestAudioFile(inputAudio, targetDuration)
		if err != nil {
			t.Skip("跳过测试：无法创建测试音频文件，可能缺少ffmpeg")
		}

		defer cleanupTestFiles(inputAudio, outputAudio)

		// 执行测试
		err = ProcessAudioDuration(inputAudio, targetDuration, outputAudio)

		// 验证结果
		if err != nil {
			t.Errorf("期望 err 为 nil，但得到: %v", err)
		}
		if !fileExists(outputAudio) {
			t.Error("期望输出文件存在，但文件不存在")
		}

		// 验证输出文件时长
		duration, err := ProbeDuration(outputAudio)
		if err != nil {
			t.Errorf("期望 err 为 nil，但得到: %v", err)
		}
		if !almostEqual(duration, targetDuration, 0.2) { // 允许0.2秒误差
			t.Errorf("期望时长约为 %f，但得到: %f", targetDuration, duration)
		}
	})

	t.Run("音频时长大于目标时长（需要裁剪）", func(t *testing.T) {
		inputAudio := "test_audio_trim.aac"
		outputAudio := "test_output_trim.aac"
		originalDuration := 5.0
		targetDuration := 3.0

		// 创建5秒的测试音频文件
		err := createTestAudioFile(inputAudio, originalDuration)
		if err != nil {
			t.Skip("跳过测试：无法创建测试音频文件，可能缺少ffmpeg")
		}

		defer cleanupTestFiles(inputAudio, outputAudio)

		// 执行测试
		err = ProcessAudioDuration(inputAudio, targetDuration, outputAudio)

		// 验证结果
		if err != nil {
			t.Errorf("期望 err 为 nil，但得到: %v", err)
		}
		if !fileExists(outputAudio) {
			t.Error("期望输出文件存在，但文件不存在")
		}

		// 验证输出文件时长应该被裁剪到目标时长
		duration, err := ProbeDuration(outputAudio)
		if err != nil {
			t.Errorf("期望 err 为 nil，但得到: %v", err)
		}
		if !almostEqual(duration, targetDuration, 0.2) { // 允许0.2秒误差
			t.Errorf("期望时长约为 %f，但得到: %f", targetDuration, duration)
		}
		if duration >= originalDuration {
			t.Errorf("期望时长小于原始时长 %f，但得到: %f", originalDuration, duration)
		}
	})

}

// 基准测试
func BenchmarkMergeVideosByConcat(b *testing.B) {
	// 准备测试文件
	video1 := "bench_video1.mp4"
	video2 := "bench_video2.mp4"

	// 创建测试视频文件
	err1 := createTestVideoFile(video1, true)
	err2 := createTestVideoFile(video2, true)

	if err1 != nil || err2 != nil {
		b.Skip("跳过基准测试：无法创建测试视频文件，可能缺少ffmpeg")
	}

	defer cleanupTestFiles(video1, video2)

	inputFiles := []string{video1, video2}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		output := fmt.Sprintf("bench_output_%d.mp4", i)
		err := MergeVideosByConcat(inputFiles, output)
		if err != nil {
			b.Fatalf("基准测试失败: %v", err)
		}
		os.Remove(output) // 清理输出文件
	}
}

func BenchmarkHasAudioStream(b *testing.B) {
	// 准备测试文件
	video := "bench_audio_test.mp4"

	// 创建测试视频文件
	err := createTestVideoFile(video, true)

	if err != nil {
		b.Skip("跳过基准测试：无法创建测试视频文件，可能缺少ffmpeg")
	}

	defer cleanupTestFiles(video)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, err := HasAudioStream(video)
		if err != nil {
			b.Fatalf("基准测试失败: %v", err)
		}
	}
}

// 集成测试示例
func TestMergeVideosByConcatIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过集成测试")
	}

	Convey("MergeVideosByConcat 集成测试", t, func() {

		Convey("大文件合并测试", func() {
			// 这个测试需要更长的视频文件来测试实际场景
			video1 := "integration_video1.mp4"
			video2 := "integration_video2.mp4"
			output := "integration_output.mp4"

			// 创建较长的测试视频文件（5秒）
			cmd1 := exec.Command("ffmpeg",
				"-f", "lavfi",
				"-i", "color=c=red:s=640x480:d=5",
				"-f", "lavfi",
				"-i", "sine=frequency=440:duration=5",
				"-c:v", "libx264",
				"-c:a", "aac",
				"-shortest",
				"-y", video1)

			cmd2 := exec.Command("ffmpeg",
				"-f", "lavfi",
				"-i", "color=c=blue:s=640x480:d=5",
				"-f", "lavfi",
				"-i", "sine=frequency=880:duration=5",
				"-c:v", "libx264",
				"-c:a", "aac",
				"-shortest",
				"-y", video2)

			err1 := cmd1.Run()
			err2 := cmd2.Run()

			if err1 != nil || err2 != nil {
				SkipConvey("跳过集成测试：无法创建测试视频文件，可能缺少ffmpeg")
				return
			}

			defer cleanupTestFiles(video1, video2, output)

			// 执行合并
			inputFiles := []string{video1, video2}
			err := MergeVideosByConcat(inputFiles, output)

			// 验证结果
			So(err, ShouldBeNil)
			So(fileExists(output), ShouldBeTrue)

			// 验证输出文件的时长应该约为10秒（两个5秒视频的合并）
			duration, err := ProbeDuration(output)
			So(err, ShouldBeNil)
			So(duration, ShouldBeGreaterThan, 9.0) // 允许一些误差
			So(duration, ShouldBeLessThan, 11.0)   // 允许一些误差
		})
	})
}

// TestExtractNonBlackFrame 测试提取非黑帧封面函数
func TestExtractNonBlackFrame(t *testing.T) {
	t.Run("使用真实视频文件提取非黑帧", func(t *testing.T) {
		videoUrl := "https://cdn-allvoice-down-cn-testing.funnycp.com/allvoice/test/public/video_commentary/merge/20250829/4a3ac6a1-6772-488f-919a-49ff93e2b9e6.mp4"
		outputImage := "test_non_black_frame.jpg"

		defer cleanupTestFiles(outputImage)

		// 执行测试
		err := ExtractNonBlackFrame(videoUrl, outputImage)

		// 验证结果
		if err != nil {
			t.Errorf("期望 err 为 nil，但得到: %v", err)
		}
		if !fileExists(outputImage) {
			t.Error("期望输出文件存在，但文件不存在")
		}

		// 验证输出文件是有效的图片文件（检查文件大小）
		stat, err := os.Stat(outputImage)
		if err != nil {
			t.Errorf("期望 err 为 nil，但得到: %v", err)
		}
		if stat.Size() <= 0 {
			t.Errorf("期望文件大小大于 0，但得到: %d", stat.Size())
		}
	})

	t.Run("使用本地测试视频文件", func(t *testing.T) {
		video := "test_extract_frame.mp4"
		outputImage := "test_local_frame.jpg"

		// 创建带音频的测试视频文件
		err := createTestVideoFile(video, true)

		// 如果无法创建测试文件，跳过测试
		if err != nil {
			t.Skip("跳过测试：无法创建测试视频文件，可能缺少ffmpeg")
		}

		defer cleanupTestFiles(video, outputImage)

		// 执行测试
		err = ExtractNonBlackFrame(video, outputImage)

		// 验证结果
		if err != nil {
			t.Errorf("期望 err 为 nil，但得到: %v", err)
		}
		if !fileExists(outputImage) {
			t.Error("期望输出文件存在，但文件不存在")
		}

		// 验证输出文件是有效的图片文件
		stat, err := os.Stat(outputImage)
		if err != nil {
			t.Errorf("期望 err 为 nil，但得到: %v", err)
		}
		if stat.Size() <= 0 {
			t.Errorf("期望文件大小大于 0，但得到: %d", stat.Size())
		}
	})

	t.Run("输入视频文件不存在", func(t *testing.T) {
		nonexistentVideo := "nonexistent_video.mp4"
		outputImage := "test_error_frame.jpg"

		defer cleanupTestFiles(outputImage)

		// 执行测试
		err := ExtractNonBlackFrame(nonexistentVideo, outputImage)

		// 验证结果
		if err == nil {
			t.Error("期望返回错误，但得到 nil")
		}
		if fileExists(outputImage) {
			t.Error("期望输出文件不存在，但文件存在")
		}
	})

	t.Run("输出路径无效", func(t *testing.T) {
		video := "test_invalid_output.mp4"
		invalidOutput := "/invalid/path/that/does/not/exist/frame.jpg"

		// 创建测试视频文件
		err := createTestVideoFile(video, true)

		// 如果无法创建测试文件，跳过测试
		if err != nil {
			t.Skip("跳过测试：无法创建测试视频文件")
		}

		defer cleanupTestFiles(video)

		// 执行测试
		err = ExtractNonBlackFrame(video, invalidOutput)

		// 验证结果
		if err == nil {
			t.Error("期望返回错误，但得到 nil")
		}
		if fileExists(invalidOutput) {
			t.Error("期望输出文件不存在，但文件存在")
		}
	})

	t.Run("不同输出格式测试", func(t *testing.T) {
		video := "test_format_video.mp4"

		// 创建测试视频文件
		err := createTestVideoFile(video, true)

		// 如果无法创建测试文件，跳过测试
		if err != nil {
			t.Skip("跳过测试：无法创建测试视频文件")
		}

		defer cleanupTestFiles(video)

		testFormats := []string{".jpg", ".png", ".bmp"}

		for _, format := range testFormats {
			t.Run(fmt.Sprintf("输出格式 %s", format), func(t *testing.T) {
				outputImage := fmt.Sprintf("test_frame%s", format)
				defer cleanupTestFiles(outputImage)

				// 执行测试
				err := ExtractNonBlackFrame(video, outputImage)

				// 验证结果
				if err != nil {
					t.Errorf("期望 err 为 nil，但得到: %v", err)
				}
				if !fileExists(outputImage) {
					t.Error("期望输出文件存在，但文件不存在")
				}

				// 验证文件大小
				stat, err := os.Stat(outputImage)
				if err != nil {
					t.Errorf("期望 err 为 nil，但得到: %v", err)
				}
				if stat.Size() <= 0 {
					t.Errorf("期望文件大小大于 0，但得到: %d", stat.Size())
				}
			})
		}
	})
}

// BenchmarkExtractNonBlackFrame 基准测试
func BenchmarkExtractNonBlackFrame(b *testing.B) {
	// 准备测试文件
	video := "bench_extract_frame.mp4"

	// 创建测试视频文件
	err := createTestVideoFile(video, true)

	if err != nil {
		b.Skip("跳过基准测试：无法创建测试视频文件，可能缺少ffmpeg")
	}

	defer cleanupTestFiles(video)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		output := fmt.Sprintf("bench_frame_%d.jpg", i)
		err := ExtractNonBlackFrame(video, output)
		if err != nil {
			b.Fatalf("基准测试失败: %v", err)
		}
		os.Remove(output) // 清理输出文件
	}
}

func TestExtractNonBlackFrame1(t *testing.T) {
	type args struct {
		videoPath       string
		outputImagePath string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "test1",
			args: args{
				videoPath:       "https://cdn-allvoice-down-cn-testing.funnycp.com/allvoice/test/public/video_commentary/merge/20250829/4a3ac6a1-6772-488f-919a-49ff93e2b9e6.mp4",
				outputImagePath: "test1.jpg",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := ExtractNonBlackFrame(tt.args.videoPath, tt.args.outputImagePath); (err != nil) != tt.wantErr {
				t.Errorf("ExtractNonBlackFrame() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCutVideo(t *testing.T) {
	type args struct {
		inputPath  string
		startTime  string
		endTime    string
		outputPath string
		mute       bool
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "test1",
			args: args{
				inputPath:  "https://cdn-allvoice-down-cn-testing.funnycp.com/allvoice/test/public/video_commentary/merge/20250829/4a3ac6a1-6772-488f-919a-49ff93e2b9e6.mp4",
				startTime:  "00:00:00.000",
				endTime:    "00:00:05.000",
				outputPath: "test1.mp4",
				mute:       true,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := CutVideo(tt.args.inputPath, tt.args.startTime, tt.args.endTime, tt.args.outputPath, tt.args.mute); (err != nil) != tt.wantErr {
				t.Errorf("CutVideo() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
