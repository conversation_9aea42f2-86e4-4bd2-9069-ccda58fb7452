package util

import (
	"business-workflow/internal/common"
	"business-workflow/internal/common/config"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/google/uuid"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
)

func GenerateTargetUrl(tenantId, subTaskId int64, moduleName, vocalUrl string) string {
	fileName, fileExt := GetFileNameFromUrl(vocalUrl)
	ttsFileName := fmt.Sprintf("%s_%s_tts%s", fileName, uuid.New().String(), fileExt)

	cfg := config.GetConfig()
	// translate-saas-{环境}/public/{模块名}/{用户ID}/{年月日}/{任务ID}/{tts文件名}.{文件后缀}
	return fmt.Sprintf("%s-%s/public/%s/%d/%s/%d/%s", cfg.Obs.ObjectDir, cfg.Env, moduleName, tenantId, time.Now().Format("20060102"), subTaskId, ttsFileName)
}

func GetFileNameFromUrl(url string) (string, string) {
	parts := strings.Split(url, "?")
	filePath := path.Base(parts[0])
	// 提取文件名
	fileName := filePath
	if dotIndex := strings.LastIndex(fileName, "."); dotIndex != -1 {
		fileName = fileName[:dotIndex]
	}
	// 获取文件扩展名
	fileExt := path.Ext(filePath)
	return fileName, fileExt
}

func SrtStringTime2Ms(str string) (int64, error) {
	vecs := strings.Split(str, ",")
	if len(vecs) != 2 {
		return 0, fmt.Errorf("format error: %s", str)
	}
	heads := strings.Split(vecs[0], ":")
	if len(heads) != 3 {
		return 0, fmt.Errorf("format error: %s", str)
	}
	hour, err := timeString2Int(heads[0])
	if err != nil {
		return 0, err
	}
	minute, err := timeString2Int(heads[1])
	if err != nil {
		return 0, err
	}
	second, err := timeString2Int(heads[2])
	if err != nil {
		return 0, err
	}
	mill, err := timeString2Int(vecs[1])
	if err != nil {
		return 0, err
	}
	if hour < 0 || minute < 0 || second < 0 || mill < 0 {
		return 0, fmt.Errorf("format error: %s", str)
	}
	return hour*60*60*1000 + minute*60*1000 + second*1000 + mill, nil
}
func timeString2Int(str string) (int64, error) {
	num, err := strconv.ParseInt(str, 10, 64)
	return num, err
}
func SrtTimeMs2StringDot(t int64) string {
	hour := t / (60 * 60 * 1000)
	t = t - hour*(60*60*1000)

	minute := t / (60 * 1000)
	t = t - minute*(60*1000)

	second := t / 1000
	t = t - second*1000

	mill := t

	return fmt.Sprintf("%02d:%02d:%02d.%03d", hour, minute, second, mill)
}
func SrtTimeMs2String(t int64) string {
	hour := t / (60 * 60 * 1000)
	t = t - hour*(60*60*1000)

	minute := t / (60 * 1000)
	t = t - minute*(60*1000)

	second := t / 1000
	t = t - second*1000

	mill := t

	return fmt.Sprintf("%02d:%02d:%02d,%03d", hour, minute, second, mill)
}

//func SrtStringTime2MsDot(str string) (int64, error) {
//	nstr := strings.Replace(str, ".", ",", 1)
//	return SrtStringTime2Ms(nstr)
//}

// FfmpegTimeStr2Str ffmpeg 时间格式, 转 .
func FfmpegTimeStr2Str(str string) string {
	nstr := strings.Replace(str, ",", ".", 1)
	return nstr
}

func SrtStringTime2MsFloat(str string) (float64, error) {
	ms, err := SrtStringTime2Ms(str)
	if err != nil {
		return 0, err
	}
	return float64(ms) / 1e3, nil
}

// SrtTimeParseAndFormat 接受多种时间格式，返回 "HH:MM:SS,mmm"
func SrtTimeParseAndFormat(s string) (string, error) {
	s = strings.TrimSpace(s)
	if s == "" {
		return "", errors.New("empty input")
	}

	// 分离小数部分（毫秒）
	mainPart := s
	fracPart := ""
	if idx := strings.IndexAny(s, ".,"); idx != -1 { // 支持 . 或 , 作为小数点（容错）
		mainPart = s[:idx]
		fracPart = s[idx+1:]
	}

	// 处理主部分：可能是 "SS"、"MM:SS"、"HH:MM:SS"
	parts := strings.Split(mainPart, ":")
	// 逆序解析（从秒往前），以便处理不固定长度
	h, m, sec := 0, 0, 0
	switch len(parts) {
	case 1:
		// "SS"
		val, err := strconv.Atoi(parts[0])
		if err != nil {
			return "", err
		}
		sec = val
	case 2:
		// "MM:SS"
		mv, err := strconv.Atoi(parts[0])
		if err != nil {
			return "", err
		}
		sv, err := strconv.Atoi(parts[1])
		if err != nil {
			return "", err
		}
		m = mv
		sec = sv
	case 3:
		// "HH:MM:SS"
		hv, err := strconv.Atoi(parts[0])
		if err != nil {
			return "", err
		}
		mv, err := strconv.Atoi(parts[1])
		if err != nil {
			return "", err
		}
		sv, err := strconv.Atoi(parts[2])
		if err != nil {
			return "", err
		}
		h = hv
		m = mv
		sec = sv
	default:
		return "", errors.New("unsupported time format")
	}

	// 解析毫秒部分：将任意位数的小数（如 .2 .25 .282 .000）转换为毫秒（3 位）
	ms := 0
	if fracPart != "" {
		// 只取前 3 位，不足则右补零
		if len(fracPart) >= 3 {
			fracPart = fracPart[:3]
		} else {
			fracPart = fracPart + strings.Repeat("0", 3-len(fracPart))
		}
		mv, err := strconv.Atoi(fracPart)
		if err != nil {
			return "", err
		}
		ms = mv
	}

	// 把所有转成毫秒并归一化（处理秒或分超过 60 的情况）
	totalMs := (((h*60+m)*60)+sec)*1000 + ms
	if totalMs < 0 {
		return "", errors.New("negative time not supported")
	}

	// 重新分解为 HH:MM:SS,mmm
	outMs := totalMs % 1000
	totalSec := totalMs / 1000
	outSec := totalSec % 60
	totalMin := totalSec / 60
	outMin := totalMin % 60
	outHour := totalMin / 60

	// 格式化：小时至少两位
	return fmt.Sprintf("%02d:%02d:%02d,%03d", outHour, outMin, outSec, outMs), nil
}

func GenLocalPathByObjectName(objectName string) string {
	platName := common.StringFlag(objectName) + filepath.Ext(objectName)
	format := config.GetConfig().LocalPath + "/%v/%s"
	id := uuid.New().String()
	return fmt.Sprintf(format, id, platName)
}

func GenLocalPath(name, ext string) string {
	format := config.GetConfig().LocalPath + "/%v/%s"
	id := uuid.New().String()
	return fmt.Sprintf(format, id, name+ext)
}

func GenLocalPathWithPrefix(prefix, name, ext string) string {
	format := config.GetConfig().LocalPath + "/%s/%s/%s"
	id := uuid.New().String()
	return fmt.Sprintf(format, prefix, id, name+ext)
}

func SliceContains[T comparable](slice []T, key T) bool {
	for _, v := range slice {
		if v == key {
			return true
		}
	}
	return false
}

func EnsureOutputDirectory(outputPath ...string) error {
	for _, path := range outputPath {
		if _, err := os.Stat(path); os.IsNotExist(err) {
			if err := os.MkdirAll(path, os.ModePerm); err != nil {
				return fmt.Errorf("failed to create output directory, path: %v, err: %v", path, err)
			}
		}
	}
	return nil
}

func HttpPostReqWithResponseWithTimeout(ctx context.Context, url string, req any, res any, headers map[string]string, timeoutInSeconds int) error {
	respData, err := HttpPostReqWithTimeout(ctx, url, headers, req, timeoutInSeconds)
	if err != nil {
		return err
	}

	err = json.Unmarshal(respData, res)
	if err != nil {
		return err
	}

	return nil

}
func HttpPostReqWithTimeout(ctx context.Context, urls string, headers map[string]string, reqObject any, timeoutInSeconds int) ([]byte, error) {
	data, err := json.Marshal(reqObject)
	if err != nil {
		return nil, err
	}
	req, err := http.NewRequest("POST", urls, bytes.NewBuffer(data))
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}
	if rId, ok := ctx.Value(trace.ReqId).(string); ok {
		req.Header.Set(trace.ReqId, rId)
	}
	// 设置业务类型
	if bt, ok := ctx.Value(trace.BusinessType).(string); ok {
		req.Header.Set(trace.BusinessType, bt)
	}
	// 设置业务类型
	if mk, ok := ctx.Value(trace.TrafficMark).(string); ok {
		req.Header.Set(trace.TrafficMark, mk)
	}
	g.Log().Debugf(ctx, "request url: %s, header:%+v, request body: %+v", req.URL, req.Header, string(data))
	for key, value := range headers {
		req.Header.Set(key, value)
	}
	// 发送请求
	clientHTTP := &http.Client{
		Timeout: time.Second * time.Duration(timeoutInSeconds),
	}
	resp, err := clientHTTP.Do(req)
	if err != nil {
		return nil, fmt.Errorf("clientHTTP.Do err: %s", err.Error())
	}
	defer resp.Body.Close()
	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response: %s", err.Error())
	}
	g.Log().Debugf(ctx, "responseBody: %s", responseBody)
	return responseBody, err
}

type DefaultHttpResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}
