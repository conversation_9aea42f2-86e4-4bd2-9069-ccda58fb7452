//
// Copyright (c) 2024 TT, Ltd. All Rights Reserved.
//

package http

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strings"
	"time"
)

func SendHttpPostRequest(ctx context.Context, url string, request interface{}) ([]byte, error) {
	// 将请求体转换为 JSON 格式
	data, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal data: %s", err)
	}

	// 创建 HTTP POST 请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(data))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %s", err)
	}

	req.Header.Set("Content-Type", "application/json")

	headers := map[string]string{
		"x-qw-traffic-mark": "",
	}
	for key, value := range headers {
		req.Header.Set(key, value)
	}
	// 使用 http.Client 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %s", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("request failed with status code %d: %s", resp.StatusCode, body)
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %s", err)
	}

	return body, nil
}

// 发送GET请求
func SendHttpGetRequest(url string) ([]byte, error) {
	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to send get request: %s", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %s", err)
	}

	return body, nil
}

// 通过url下载文件
func DownloadToByte(url string) ([]byte, error) {
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	return data, nil
}

// DownloadToFile 从指定的 URL 下载文件并保存到指定的目录
func DownloadToFile(url string, downloadDir string) (string, string, string, int64, time.Duration, error) {
	start := time.Now() // 记录开始时间

	resp, err := http.Get(url)
	if err != nil {
		return "", "", "", 0, 0, fmt.Errorf("failed to download file: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return "", "", "", 0, 0, fmt.Errorf("failed to download file: server returned %s", resp.Status)
	}

	// 获取文件名
	fileName, fileExt := GetFileNameFromUrl(url)
	// 创建保存文件的完整路径
	filePath := filepath.Join(downloadDir, fileName+fileExt)

	// 检查并创建目录
	if err := os.MkdirAll(downloadDir, os.ModePerm); err != nil {
		return "", "", "", 0, 0, fmt.Errorf("failed to create directory: %w", err)
	}

	// 创建目标文件
	outFile, err := os.Create(filePath)
	if err != nil {
		return "", "", "", 0, 0, fmt.Errorf("failed to create file: %w", err)
	}
	defer outFile.Close()

	// 将响应体写入到文件
	written, err := io.Copy(outFile, resp.Body)
	if err != nil {
		return "", "", "", 0, 0, fmt.Errorf("failed to save file: %w", err)
	}

	duration := time.Since(start) // 计算耗时
	return filePath, fileName, fileExt, written, duration, nil
}

func GetFileNameFromUrl(url string) (string, string) {
	parts := strings.Split(url, "?")
	filePath := path.Base(parts[0])
	// 提取文件名
	fileName := filePath
	if dotIndex := strings.LastIndex(fileName, "."); dotIndex != -1 {
		fileName = fileName[:dotIndex]
	}
	// 获取文件扩展名
	fileExt := path.Ext(filePath)
	return fileName, fileExt
}
