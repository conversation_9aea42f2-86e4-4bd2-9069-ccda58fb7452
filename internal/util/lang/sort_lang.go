package audio_trans

import (
	"sort"
)

// 语言排序结构体
type LangOrder struct {
	LangId string
	Order  int
}

// 按指定顺序的语言排序映射表
var LangOrderMap = map[string]int{
	"auto":    0,  // 自动检测
	"zh":      1,  // 中文
	"zh-Hant": 2,  // 繁体中文
	"en":      3,  // 英语
	"ja":      4,  // 日语
	"fr":      5,  // 法语
	"de":      6,  // 德语
	"ko":      7,  // 韩语
	"yue":     8,  // 粤语
	"vi":      9,  // 越南语
	"th":      10, // 泰语
	"id":      11, // 印尼语
	"hi":      12, // 印地语
	"ar":      13, // 阿拉伯语
	"es":      14, // 西班牙语
	"ru":      15, // 俄语
	"it":      16, // 意大利语
	"pt":      17, // 葡萄牙语
	"pt-BR":   18, // 葡萄牙语-巴西
	"tr":      19, // 土耳其语
	"fil":     20, // 菲律宾语
	"ms":      21, // 马来西亚语
	"nl":      22, // 荷兰语
	"pl":      23, // 波兰语
	"fi":      24, // 芬兰语
	"uk":      25, // 乌克兰语
	"sv":      26, // 瑞典语
	"el":      27, // 希腊语
	"cs":      28, // 捷克语
	"da":      29, // 丹麦语
	"hr":      30, // 克罗地亚语
	"ro":      31, // 罗马尼亚语
	"bg":      32, // 保加利亚语
	"sk":      33, // 斯洛伐克语
	"ta":      34, // 泰米尔语
	"no":      35, // 挪威语
	"hu":      36, // 匈牙利语
	"sl":      37, // 斯洛文尼亚语
	"et":      38, // 爱沙尼亚语
	"my":      39, // 缅甸语
	"km":      40, // 高棉语
	"af":      41, // 南非语
	"be":      42, // 白俄罗斯语
	"gl":      43, // 加利西亚语
	"hy":      44, // 亚美尼亚语
	"bs":      45, // 波斯尼亚语
	"ca":      46, // 加泰罗尼亚语
	"lv":      47, // 拉脱维亚语
	"az":      48, // 阿塞拜疆语
	"he":      49, // 希伯来语
	"is":      50, // 冰岛语
	"kn":      51, // 卡纳达语
	"kk":      52, // 哈萨克语
	"sr":      53, // 塞尔维亚语
	"sw":      54, // 斯瓦希里语
	"lt":      55, // 立陶宛语
	"mk":      56, // 马其顿语
	"mr":      57, // 马拉地语
	"ne":      58, // 尼泊尔语
	"mi":      59, // 毛利语
	"fa":      60, // 波斯语
	"ur":      61, // 乌尔都语
	"cy":      62, // 威尔士语
	"tl":      63, // 他加禄语
}

// 获取语言排序值的辅助函数
func GetLangOrder(langId string) int {
	if order, exists := LangOrderMap[langId]; exists {
		return order
	}
	return 999 // 未知语言排在最后
}

// 按指定顺序排序语言列表的辅助函数
func SortLangsByOrder(langs []TargetLangDef) []TargetLangDef {
	sort.Slice(langs, func(i, j int) bool {
		return GetLangOrder(langs[i].LangId) < GetLangOrder(langs[j].LangId)
	})
	return langs
}