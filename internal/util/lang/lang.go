package audio_trans

var (
	// 用于代码中语言检测
	SourceLangVecForCheck = []TargetLangDef{
		{"auto", "Auto Detect"},
		{"en", "English"},
		{"af", "Afrikaans"},
		{"ar", "Arabic"},
		{"hy", "Armenian"},
		{"az", "Azerbaijani"},
		{"be", "Belarusian"},
		{"bs", "Bosnian"},
		{"bg", "Bulgarian"},
		{"ca", "Catalan"},
		{"zh", "Chinese(Mainland)"},
		{"zh-Hant", "Chinese(Traditional)"},
		{"yue", "Cantonese"},
		{"hr", "Croatian"},
		{"cs", "Czech"},
		{"da", "Danish"},
		{"nl", "Dutch"},
		{"et", "Estonian"},
		{"fi", "Finnish"},
		{"fr", "French"},
		{"gl", "Galician"},
		{"de", "German"},
		{"el", "Greek"},
		{"he", "Hebrew"},
		{"hi", "Hindi"},
		{"hu", "Hungarian"},
		{"is", "Icelandic"},
		{"id", "Indonesian"},
		{"it", "Italian"},
		{"ja", "Japanese"},
		{"kn", "Kannada"},
		{"kk", "Kazakh"},
		{"ko", "Korean"},
		{"lv", "Latvian"},
		{"lt", "Lithuanian"},
		{"mk", "Macedonian"},
		{"ms", "Malay"},
		{"mr", "Marathi"},
		{"mi", "Maori"},
		{"ne", "Nepali"},
		{"no", "Norwegian"},
		{"fa", "Persian"},
		{"pl", "Polish"},
		{"pt", "Portuguese"},
		{"ro", "Romanian"},
		{"ru", "Russian"},
		{"sr", "Serbian"},
		{"sk", "Slovak"},
		{"sl", "Slovenian"},
		{"es", "Spanish"},
		{"sw", "Swahili"},
		{"sv", "Swedish"},
		{"tl", "Tagalog"},
		{"ta", "Tamil"},
		{"th", "Thai"},
		{"tr", "Turkish"},
		{"uk", "Ukrainian"},
		{"ur", "Urdu"},
		{"vi", "Vietnamese"},
		{"cy", "Welsh"},
	}
	SourceVocalLangVecForCheck = []TargetLangDef{
		{"auto", "Auto Detect"},
		{"en", "English"},
		{"af", "Afrikaans"},
		{"ar", "Arabic"},
		{"hy", "Armenian"},
		{"az", "Azerbaijani"},
		{"be", "Belarusian"},
		{"bs", "Bosnian"},
		{"bg", "Bulgarian"},
		{"ca", "Catalan"},
		{"zh", "Chinese(Mainland)"},
		{"zh-Hant", "Chinese(Traditional)"},
		{"yue", "Cantonese"},
		{"hr", "Croatian"},
		{"cs", "Czech"},
		{"da", "Danish"},
		{"nl", "Dutch"},
		{"et", "Estonian"},
		{"fi", "Finnish"},
		{"fr", "French"},
		{"gl", "Galician"},
		{"de", "German"},
		{"el", "Greek"},
		{"he", "Hebrew"},
		{"hi", "Hindi"},
		{"hu", "Hungarian"},
		{"is", "Icelandic"},
		{"id", "Indonesian"},
		{"it", "Italian"},
		{"ja", "Japanese"},
		{"kn", "Kannada"},
		{"kk", "Kazakh"},
		{"ko", "Korean"},
		{"lv", "Latvian"},
		{"lt", "Lithuanian"},
		{"mk", "Macedonian"},
		{"ms", "Malay"},
		{"mr", "Marathi"},
		{"mi", "Maori"},
		{"ne", "Nepali"},
		{"no", "Norwegian"},
		{"fa", "Persian"},
		{"pl", "Polish"},
		{"pt", "Portuguese"},
		{"ro", "Romanian"},
		{"ru", "Russian"},
		{"sr", "Serbian"},
		{"sk", "Slovak"},
		{"sl", "Slovenian"},
		{"es", "Spanish"},
		{"sw", "Swahili"},
		{"sv", "Swedish"},
		{"tl", "Tagalog"},
		{"ta", "Tamil"},
		{"th", "Thai"},
		{"tr", "Turkish"},
		{"uk", "Ukrainian"},
		{"ur", "Urdu"},
		{"vi", "Vietnamese"},
		{"cy", "Welsh"},
	}
	TargetVocalLangVecForCheck = []TargetLangDef{
		{LangId: "zh", LangDesc: "Chinese(Mainland)"},
		{LangId: "zh-Hant", LangDesc: "Chinese(Traditional)"},
		{LangId: "en", LangDesc: "English"},
		{LangId: "ja", LangDesc: "Japanese"},
		{LangId: "fr", LangDesc: "French"},
		{LangId: "de", LangDesc: "German"},
		{LangId: "ko", LangDesc: "Korean"},
		{LangId: "yue", LangDesc: "Cantonese"},
		{LangId: "vi", LangDesc: "Vietnamese"},
		{LangId: "th", LangDesc: "Thai"},
	}
	ElevenLabsLangVecForCheck = []TargetLangDef{
		{LangId: "ru", LangDesc: "Russian"},
		{LangId: "hi", LangDesc: "Hindi"},
		{LangId: "id", LangDesc: "Indonesian"},
		{LangId: "pt", LangDesc: "Portuguese"},
		{LangId: "it", LangDesc: "Italian"},
		{LangId: "es", LangDesc: "Spanish"},
		{LangId: "nl", LangDesc: "Dutch"},
		{LangId: "tr", LangDesc: "Turkish"},
		{LangId: "ms", LangDesc: "Malay"},
		{LangId: "fil", LangDesc: "Filipino"},
		{LangId: "pl", LangDesc: "Polish"},
		{LangId: "sv", LangDesc: "Swedish"},
		{LangId: "ro", LangDesc: "Romanian"},
		{LangId: "bg", LangDesc: "Bulgarian"},
		{LangId: "ar", LangDesc: "Arabic"},
		{LangId: "cs", LangDesc: "Czech"},
		{LangId: "el", LangDesc: "Greek"},
		{LangId: "fi", LangDesc: "Finnish"},
		{LangId: "hr", LangDesc: "Croatian"},
		{LangId: "da", LangDesc: "Danish"},
		{LangId: "hu", LangDesc: "Hungarian"},
		{LangId: "no", LangDesc: "Norwegian"},
		{LangId: "ta", LangDesc: "Tamil"},
		{LangId: "sk", LangDesc: "Slovak"},
		{LangId: "uk", LangDesc: "Ukrainian"},
		{LangId: "th", LangDesc: "Thai"},
	}
	TargetLangVecForCheck = []TargetLangDef{
		{LangId: "auto", LangDesc: "Auto Detect"},
		{LangId: "zh", LangDesc: "Chinese(Mainland)"},
		{LangId: "zh-Hant", LangDesc: "Chinese(Traditional)"},
		{LangId: "en", LangDesc: "English"},
		{LangId: "ja", LangDesc: "Japanese"},
		{LangId: "fr", LangDesc: "French"},
		{LangId: "de", LangDesc: "German"},
		{LangId: "ko", LangDesc: "Korean"},
		{LangId: "yue", LangDesc: "Cantonese"},
		{LangId: "km", LangDesc: "Khmer"},
		{LangId: "tr", LangDesc: "Turkish"},
		{LangId: "hu", LangDesc: "Hungarian"},
		{LangId: "ro", LangDesc: "Romanian"},
		{LangId: "da", LangDesc: "Danish"},
		{LangId: "pl", LangDesc: "Polish"},
		{LangId: "it", LangDesc: "Italian"},
		{LangId: "ru", LangDesc: "Russian"},
		{LangId: "es", LangDesc: "Spanish"},
		{LangId: "ms", LangDesc: "Malay"},
		{LangId: "hi", LangDesc: "Hindi"},
		{LangId: "vi", LangDesc: "Vietnamese"},
		{LangId: "sv", LangDesc: "Swedish"},
		{LangId: "cs", LangDesc: "Czech"},
		{LangId: "et", LangDesc: "Estonian"},
		{LangId: "nl", LangDesc: "Dutch"},
		{LangId: "ar", LangDesc: "Arabic"},
		{LangId: "my", LangDesc: "Myanmar"},
		{LangId: "no", LangDesc: "Norwegian"},
		{LangId: "fil", LangDesc: "Filipino"},
		{LangId: "pt-BR", LangDesc: "Portuguese(Brazil)"},
		{LangId: "id", LangDesc: "Indonesian"},
		{LangId: "sl", LangDesc: "Slovene"},
		{LangId: "fi", LangDesc: "Finnish"},
		{LangId: "bg", LangDesc: "Bulgarian"},
		{LangId: "el", LangDesc: "Greek"},
		{LangId: "pt", LangDesc: "Portuguese"},
		{LangId: "th", LangDesc: "Thai"},
	}
	// 英文版
	SourceLangVec = []TargetLangDef{
		{"auto", "Auto Detect"},
		{"en", "English"},
		{"af", "Afrikaans"},
		{"ar", "Arabic"},
		{"hy", "Armenian"},
		{"az", "Azerbaijani"},
		{"be", "Belarusian"},
		{"bs", "Bosnian"},
		{"bg", "Bulgarian"},
		{"ca", "Catalan"},
		{"zh", "Chinese(Mainland)"},
		{"zh-Hant", "Chinese(Traditional)"},
		{"yue", "Cantonese"},
		{"hr", "Croatian"},
		{"cs", "Czech"},
		{"da", "Danish"},
		{"nl", "Dutch"},
		{"et", "Estonian"},
		{"fi", "Finnish"},
		{"fr", "French"},
		{"gl", "Galician"},
		{"de", "German"},
		{"el", "Greek"},
		{"he", "Hebrew"},
		{"hi", "Hindi"},
		{"hu", "Hungarian"},
		{"is", "Icelandic"},
		{"id", "Indonesian"},
		{"it", "Italian"},
		{"ja", "Japanese"},
		{"kn", "Kannada"},
		{"kk", "Kazakh"},
		{"ko", "Korean"},
		{"lv", "Latvian"},
		{"lt", "Lithuanian"},
		{"mk", "Macedonian"},
		{"ms", "Malay"},
		{"mr", "Marathi"},
		{"mi", "Maori"},
		{"ne", "Nepali"},
		{"no", "Norwegian"},
		{"fa", "Persian"},
		{"pl", "Polish"},
		{"pt", "Portuguese"},
		{"ro", "Romanian"},
		{"ru", "Russian"},
		{"sr", "Serbian"},
		{"sk", "Slovak"},
		{"sl", "Slovenian"},
		{"es", "Spanish"},
		{"sw", "Swahili"},
		{"sv", "Swedish"},
		{"tl", "Tagalog"},
		{"ta", "Tamil"},
		{"th", "Thai"},
		{"tr", "Turkish"},
		{"uk", "Ukrainian"},
		{"ur", "Urdu"},
		{"vi", "Vietnamese"},
		{"cy", "Welsh"},
	}
	TargetVocalLangVec = []TargetLangDef{
		{LangId: "zh", LangDesc: "Chinese(Mainland)"},
		{LangId: "zh-Hant", LangDesc: "Chinese(Traditional)"},
		{LangId: "en", LangDesc: "English"},
		{LangId: "ja", LangDesc: "Japanese"},
		{LangId: "fr", LangDesc: "French"},
		{LangId: "de", LangDesc: "German"},
		{LangId: "ko", LangDesc: "Korean"},
		{LangId: "yue", LangDesc: "Cantonese"},
		{LangId: "vi", LangDesc: "Vietnamese"},
		//{LangId: "th", LangDesc: "Thai"},
	}
	ElevenLabsLangVec = []TargetLangDef{
		{LangId: "ru", LangDesc: "Russian"},
		{LangId: "hi", LangDesc: "Hindi"},
		{LangId: "id", LangDesc: "Indonesian"},
		{LangId: "pt", LangDesc: "Portuguese"},
		{LangId: "it", LangDesc: "Italian"},
		{LangId: "es", LangDesc: "Spanish"},
		{LangId: "nl", LangDesc: "Dutch"},
		{LangId: "tr", LangDesc: "Turkish"},
		{LangId: "ms", LangDesc: "Malay"},
		{LangId: "fil", LangDesc: "Filipino"},
		{LangId: "pl", LangDesc: "Polish"},
		{LangId: "sv", LangDesc: "Swedish"},
		{LangId: "ro", LangDesc: "Romanian"},
		{LangId: "bg", LangDesc: "Bulgarian"},
		{LangId: "ar", LangDesc: "Arabic"},
		{LangId: "cs", LangDesc: "Czech"},
		{LangId: "el", LangDesc: "Greek"},
		{LangId: "fi", LangDesc: "Finnish"},
		{LangId: "hr", LangDesc: "Croatian"},
		{LangId: "da", LangDesc: "Danish"},
		{LangId: "hu", LangDesc: "Hungarian"},
		{LangId: "no", LangDesc: "Norwegian"},
		{LangId: "ta", LangDesc: "Tamil"},
		{LangId: "sk", LangDesc: "Slovak"},
		{LangId: "uk", LangDesc: "Ukrainian"},
		//{LangId: "th", LangDesc: "Thai"},
	}
	TargetLangVec = []TargetLangDef{
		{LangId: "zh", LangDesc: "Chinese(Mainland)"},
		{LangId: "zh-Hant", LangDesc: "Chinese(Traditional)"},
		{LangId: "en", LangDesc: "English"},
		{LangId: "ja", LangDesc: "Japanese"},
		{LangId: "fr", LangDesc: "French"},
		{LangId: "de", LangDesc: "German"},
		{LangId: "ko", LangDesc: "Korean"},
		{LangId: "yue", LangDesc: "Cantonese"},
		{LangId: "km", LangDesc: "Khmer"},
		{LangId: "tr", LangDesc: "Turkish"},
		{LangId: "hu", LangDesc: "Hungarian"},
		{LangId: "ro", LangDesc: "Romanian"},
		{LangId: "da", LangDesc: "Danish"},
		{LangId: "pl", LangDesc: "Polish"},
		{LangId: "it", LangDesc: "Italian"},
		{LangId: "ru", LangDesc: "Russian"},
		{LangId: "es", LangDesc: "Spanish"},
		{LangId: "ms", LangDesc: "Malay"},
		{LangId: "hi", LangDesc: "Hindi"},
		{LangId: "vi", LangDesc: "Vietnamese"},
		{LangId: "sv", LangDesc: "Swedish"},
		{LangId: "cs", LangDesc: "Czech"},
		{LangId: "et", LangDesc: "Estonian"},
		{LangId: "nl", LangDesc: "Dutch"},
		{LangId: "ar", LangDesc: "Arabic"},
		{LangId: "my", LangDesc: "Myanmar"},
		{LangId: "no", LangDesc: "Norwegian"},
		{LangId: "fil", LangDesc: "Filipino"},
		{LangId: "pt-BR", LangDesc: "Portuguese(Brazil)"},
		{LangId: "id", LangDesc: "Indonesian"},
		{LangId: "sl", LangDesc: "Slovene"},
		{LangId: "fi", LangDesc: "Finnish"},
		{LangId: "bg", LangDesc: "Bulgarian"},
		{LangId: "el", LangDesc: "Greek"},
		{LangId: "pt", LangDesc: "Portuguese"},
		{LangId: "th", LangDesc: "Thai"},
	}
	// 中文版
	SourceLangVecForCn = []TargetLangDef{
		{"auto", "自动检测"},
		{"zh", "中文"},
		{"zh-Hant", "繁体中文"},
		{"en", "英语"},
		{"ja", "日语"},
		{"fr", "法语"},
		{"de", "德语"},
		{"ko", "韩语"},
		{"yue", "粤语"},
		{"vi", "越南语"},
		{"af", "南非荷兰语"},
		{"ar", "阿拉伯语"},
		{"hy", "亚美尼亚语"},
		{"az", "阿塞拜疆语"},
		{"be", "白俄罗斯语"},
		{"bs", "波斯尼亚语"},
		{"bg", "保加利亚语"},
		{"ca", "加泰罗尼亚语"},
		{"hr", "克罗地亚语"},
		{"cs", "捷克语"},
		{"da", "丹麦语"},
		{"nl", "荷兰语"},
		{"et", "爱沙尼亚语"},
		{"fi", "芬兰语"},
		{"gl", "加利西亚语"},
		{"el", "希腊语"},
		{"he", "希伯来语"},
		{"hi", "印地语"},
		{"hu", "匈牙利语"},
		{"is", "冰岛语"},
		{"id", "印尼语"},
		{"it", "意大利语"},
		{"kn", "卡纳达语"},
		{"kk", "哈萨克语"},
		{"lv", "拉脱维亚语"},
		{"lt", "立陶宛语"},
		{"mk", "马其顿语"},
		{"ms", "马来语"},
		{"mr", "马拉地语"},
		{"mi", "毛利语"},
		{"ne", "尼泊尔语"},
		{"no", "挪威语"},
		{"fa", "波斯语"},
		{"pl", "波兰语"},
		{"pt", "葡萄牙语"},
		{"ro", "罗马尼亚语"},
		{"ru", "俄语"},
		{"sr", "塞尔维亚语"},
		{"sk", "斯洛伐克语"},
		{"sl", "斯洛文尼亚语"},
		{"es", "西班牙语"},
		{"sw", "斯瓦希里语"},
		{"sv", "瑞典语"},
		{"tl", "塔加路语"},
		{"ta", "泰米尔语"},
		{"th", "泰语"},
		{"tr", "土耳其语"},
		{"uk", "乌克兰语"},
		{"ur", "乌尔都语"},
		{"cy", "威尔士语"},
	}
	TargetVocalLangVecForCn = []TargetLangDef{
		{LangId: "zh", LangDesc: "中文"},
		//{LangId: "zh-Hant", LangDesc: "繁体中文"},
		{LangId: "en", LangDesc: "英语"},
		{LangId: "ja", LangDesc: "日语"},
		{LangId: "fr", LangDesc: "法语"},
		{LangId: "de", LangDesc: "德语"},
		{LangId: "ko", LangDesc: "韩语"},
		{LangId: "yue", LangDesc: "粤语"},
		{LangId: "vi", LangDesc: "越南语"},
		// {LangId: "th", LangDesc: "泰语"},
	}
	ElevenLabsLangVecForCn = []TargetLangDef{
		{LangId: "ru", LangDesc: "俄语"},
		{LangId: "hi", LangDesc: "印地语"},
		{LangId: "id", LangDesc: "印尼语"},
		{LangId: "pt", LangDesc: "葡萄牙语"},
		{LangId: "it", LangDesc: "意大利语"},
		{LangId: "es", LangDesc: "西班牙语"},
		{LangId: "nl", LangDesc: "荷兰语"},
		{LangId: "tr", LangDesc: "土耳其语"},
		{LangId: "ms", LangDesc: "马来语"},
		{LangId: "fil", LangDesc: "菲律宾语"},
		{LangId: "pl", LangDesc: "波兰语"},
		{LangId: "sv", LangDesc: "瑞典语"},
		{LangId: "ro", LangDesc: "罗马尼亚语"},
		{LangId: "bg", LangDesc: "保加利亚语"},
		{LangId: "ar", LangDesc: "阿拉伯语"},
		{LangId: "cs", LangDesc: "捷克语"},
		{LangId: "el", LangDesc: "希腊语"},
		{LangId: "fi", LangDesc: "芬兰语"},
		{LangId: "hr", LangDesc: "克罗地亚语"},
		{LangId: "da", LangDesc: "丹麦语"},
		{LangId: "hu", LangDesc: "匈牙利语"},
		{LangId: "no", LangDesc: "挪威语"},
		{LangId: "ta", LangDesc: "泰米尔语"},
		{LangId: "sk", LangDesc: "斯洛伐克语"},
		{LangId: "uk", LangDesc: "乌克兰语"},
		//{LangId: "th", LangDesc: "泰语"},
	}
	TargetLangVecForCn = []TargetLangDef{
		{LangId: "zh", LangDesc: "中文"},
		{LangId: "zh-Hant", LangDesc: "繁体中文"},
		{LangId: "en", LangDesc: "英语"},
		{LangId: "ja", LangDesc: "日语"},
		{LangId: "fr", LangDesc: "法语"},
		{LangId: "de", LangDesc: "德语"},
		{LangId: "ko", LangDesc: "韩语"},
		{LangId: "yue", LangDesc: "粤语"},
		{LangId: "km", LangDesc: "高棉语"},
		{LangId: "tr", LangDesc: "土耳其语"},
		{LangId: "hu", LangDesc: "匈牙利语"},
		{LangId: "ro", LangDesc: "罗马尼亚语"},
		{LangId: "da", LangDesc: "丹麦语"},
		{LangId: "pl", LangDesc: "波兰语"},
		{LangId: "it", LangDesc: "意大利语"},
		{LangId: "ru", LangDesc: "俄语"},
		{LangId: "es", LangDesc: "西班牙语"},
		{LangId: "ms", LangDesc: "马来语"},
		{LangId: "hi", LangDesc: "印地语"},
		{LangId: "vi", LangDesc: "越南语"},
		{LangId: "sv", LangDesc: "瑞典语"},
		{LangId: "cs", LangDesc: "捷克语"},
		{LangId: "et", LangDesc: "爱沙尼亚语"},
		{LangId: "nl", LangDesc: "荷兰语"},
		{LangId: "ar", LangDesc: "阿拉伯语"},
		{LangId: "my", LangDesc: "缅甸语"},
		{LangId: "no", LangDesc: "挪威语"},
		{LangId: "fil", LangDesc: "菲律宾语"},
		{LangId: "pt-BR", LangDesc: "葡萄牙语-巴西"},
		{LangId: "id", LangDesc: "印尼语"},
		{LangId: "sl", LangDesc: "斯洛文尼亚语"},
		{LangId: "fi", LangDesc: "芬兰语"},
		{LangId: "bg", LangDesc: "保加利亚语"},
		{LangId: "el", LangDesc: "希腊语"},
		{LangId: "pt", LangDesc: "葡萄牙语"},
		{LangId: "th", LangDesc: "泰语"},
	}
	SourceLangLists      = make(map[string]string)
	TargetLangLists      = make(map[string]string)
	SourceVocalLangLists = make(map[string]string)
	TargetVocalLangLists = make(map[string]string) // 合并了自研语言和11labs支持的语言
	AVLVocalLangLists    = make(map[string]string) // 自研语言
	ElevenLabsLangLists  = make(map[string]string)
)

type TargetLangDef struct {
	LangId   string
	LangDesc string
}

func init() {
	for _, val := range SourceLangVecForCheck {
		SourceLangLists[val.LangId] = val.LangDesc
	}
	for _, val := range TargetLangVecForCheck {
		TargetLangLists[val.LangId] = val.LangDesc
	}
	for _, val := range SourceVocalLangVecForCheck {
		SourceVocalLangLists[val.LangId] = val.LangDesc
	}
	for _, val := range TargetVocalLangVecForCheck {
		TargetVocalLangLists[val.LangId] = val.LangDesc
		AVLVocalLangLists[val.LangId] = val.LangDesc
	}
	for _, val := range ElevenLabsLangVecForCheck {
		ElevenLabsLangLists[val.LangId] = val.LangDesc
	}
	// 排序
	TargetLangVecForCn = SortLangsByOrder(TargetLangVecForCn)
	SourceLangVecForCn = SortLangsByOrder(SourceLangVecForCn)
	TargetVocalLangVecForCn = SortLangsByOrder(TargetVocalLangVecForCn)
	ElevenLabsLangVecForCn = SortLangsByOrder(ElevenLabsLangVecForCn)
	TargetLangVec = SortLangsByOrder(TargetLangVec)
	SourceLangVec = SortLangsByOrder(SourceLangVec)
	TargetVocalLangVec = SortLangsByOrder(TargetVocalLangVec)
	ElevenLabsLangVec = SortLangsByOrder(ElevenLabsLangVec)
}
