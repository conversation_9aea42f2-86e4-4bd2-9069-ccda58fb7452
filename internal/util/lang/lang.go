package lang

import (
	"business-workflow/internal/common/config"
	"business-workflow/internal/entity/vo"
	"business-workflow/internal/util"
	"context"
	"github.com/gogf/gf/v2/frame/g"
)

type LangListReq struct {
	Appid    int64  `json:"appid" p:"appid" v:"required"`         //appid
	TenantId int64  `json:"tenant_id" p:"tenant_id" v:"required"` //租户id
	ApiKey   string `json:"api_key" p:"api_key"`                  //api_key
}

type LangListRes struct {
	Code int                `json:"code"`
	Msg  string             `json:"message"`
	Data *vo.GetLangListRes `json:"data"`
}

func CallVideoTranslateLangList(ctx context.Context, req *LangListReq, res *LangListRes, timeoutInSeconds int) error {
	commonHeaders := map[string]string{
		"Content-Type": "application/json",
	}
	g.Log().Infof(ctx, "CallVideoTranslateLangList, req: %+v", req)
	return util.HttpPostReqWithResponseWithTimeout(ctx, config.GetConfig().VideoTranslate.Address+"/v1/translate_api/subtitle_translate/lang_list", req, res, commonHeaders, timeoutInSeconds)
}
