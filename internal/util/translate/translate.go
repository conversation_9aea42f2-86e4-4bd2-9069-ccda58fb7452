package translate

import (
	"business-workflow/internal/common/config"
	"business-workflow/internal/util/http"
	"context"
	"encoding/json"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
)

type TranslateReq struct {
	Text         string   `json:"text"`
	FromLang     string   `json:"from_lang"`
	ToLang       string   `json:"to_lang"`
	TextJson     TextJson `json:"text_json"`
	PhonemeAlign bool     `json:"phoneme_align"` // 是否使用音素校正，默认为False，开启则会在原本的翻译逻辑后，接入音素校正算法流程
}

// 文本翻译请求
type TextJson struct {
	TaskId    int64      `json:"task_id"`
	Subtitles []Subtitle `json:"subtitles"`
}

// Subtitle 结构体表示字幕信息
type Subtitle struct {
	Id   int    `json:"id"`
	Text string `json:"text"`
}

// 文本翻译响应
type TransJsonResult struct {
	TaskId    int64      `json:"task_id"`
	Subtitles []Subtitle `json:"subtitles"`
}

type Data struct {
	FromLang        string          `json:"from_lang"`
	ToLang          string          `json:"to_lang"`
	Query           string          `json:"query"`
	Translation     string          `json:"translation"`
	TransJsonResult TransJsonResult `json:"trans_json_result"`
	ErrorCode       int             `json:"error_code"`
}

type TranslateRes struct {
	Code int    `json:"code"`
	Msg  string `json:"message"`
	Data Data   `json:"data"`
}

// 全文翻译（同步接口）
func TranslateFullText(ctx context.Context, taskId int64, fromLang, toLang string, fromText []string) (*TranslateRes, error) {
	if fromLang == "" || toLang == "" || len(fromText) == 0 {
		return nil, fmt.Errorf("invalid params, fromLang:%s toLang:%s fromText:%v", fromLang, toLang, fromText)
	}

	// 构造请求数据
	req := TranslateReq{
		FromLang: fromLang,
		ToLang:   toLang,
		TextJson: TextJson{
			TaskId: taskId,
		},
		PhonemeAlign: true, // 默认使用音素校正
	}

	for idx, text := range fromText {
		if text != "" {
			subtile := Subtitle{
				Id:   idx,
				Text: text,
			}
			req.TextJson.Subtitles = append(req.TextJson.Subtitles, subtile)
		}
	}

	if req.FromLang == "auto" {
		req.PhonemeAlign = false
	}

	req.PhonemeAlign = config.GetBC().OpenPhonemeAlign

	cfg := config.GetConfig()
	// 添加debug日志，输出完整请求数据
	g.Log().Debugf(ctx, "TranslateFullText request data: %+v, url: %s", req, cfg.TextTranslateApi.TranslateUrl)
	body, err := http.SendHttpPostRequest(ctx, cfg.TextTranslateApi.TranslateUrl, req)
	if err != nil {
		return nil, fmt.Errorf("failed to SendHttpPostRequest, TranslateUrl:%s req:%+v error:%v", cfg.TextTranslateApi.TranslateUrl, req, err)
	}

	// 解析返回数据
	var res TranslateRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return nil, fmt.Errorf("failed to Unmarshal, req:%+v body:%s error:%v", req, body, err)
	}

	return &res, nil
}
