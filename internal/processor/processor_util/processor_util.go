package processor_util

import (
	"business-workflow/internal/common/config"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/util/media"
	hobs "business-workflow/internal/util/obs"
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/avast/retry-go"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/google/uuid"

	commentary_repo_impl "business-workflow/internal/repo/commentary_repo/impl"
)

const (
	DefaultFilePerm = 0644 // 推荐的文件权限：rw-r--r--
)

var (
	OcrSupportedLang = map[string]string{
		"zh":   "zh",
		"en":   "en",
		"ja":   "japan",
		"ko":   "korean",
		"auto": "auto",
	}
)

func GetOcrLanguage(langId string) string {
	if lang, ok := OcrSupportedLang[langId]; ok {
		return lang
	}
	return "auto"
}

func BytesCompress(data []byte) (string, error) {
	var b bytes.Buffer
	gz := gzip.NewWriter(&b)
	if _, err := gz.Write(data); err != nil {
		return "", err
	}
	if err := gz.Close(); err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(b.Bytes()), nil
	//return string(b.Bytes()), nil
}

func StringDecompressBytes(text string) ([]byte, error) {
	decodedBytes, err := base64.StdEncoding.DecodeString(text)
	if err != nil {
		return nil, err
	}
	b := bytes.NewReader(decodedBytes)
	var r *gzip.Reader
	r, err = gzip.NewReader(b)
	if err != nil {
		return nil, err
	}
	defer r.Close()
	desData, err := io.ReadAll(r)
	if err != nil {
		return nil, err
	}
	return desData, nil
}

func GetObjectNameByHttpsUrl(httpsUrl string) (string, error) {

	//兼容http协议以及带签名的链接
	if strings.HasPrefix(httpsUrl, "http://") {
		httpsUrl = strings.Replace(httpsUrl, "http://", "https://", 1)
	}
	prefix := "https://"
	pos := strings.Index(httpsUrl, prefix)
	if pos == -1 {
		return "", fmt.Errorf("url is not https url")
	}
	str := httpsUrl[pos+len(prefix)+1:]
	pos = strings.Index(str, "/")
	if pos == -1 {
		return "", fmt.Errorf("object name is not in url")
	}
	objectName := str[pos+1:]
	pos3 := strings.Index(objectName, "?")
	if pos3 == -1 {
		return objectName, nil
	}
	//去掉？号后面的内容
	return objectName[:pos3], nil
}

func GetObjectName(tenantId int64, taskId int64, localFileName string, subModelNames ...string) string {
	modelName := consts.ObsModeVideoCommentary
	subModelName := "unknown_submodel"
	if len(subModelNames) > 0 {
		subModelName = subModelNames[0]
	}
	//
	dateStr := time.Now().Format("20060102")
	objectName := fmt.Sprintf("%s-%s/public/%v/%s/%v/%s/%s%s", modelName,
		config.GetConfig().Env, tenantId, subModelName, taskId, dateStr, uuid.New().String(), filepath.Base(localFileName))
	return objectName
}

func HandleSubTaskFail(ctx context.Context, subTask *bo.CommentarySubTaskBO, code int, msg string) error {
	g.Log().Warningf(ctx, "HandleSubTaskFail, subtask: %+v, code: %v, msg: %v", subTask, code, msg)
	// task, err := commentary_repo_impl.NewCommentarySubTaskRepoImpl().GetSubTaskById(ctx, subTask.Id)
	// if err != nil {
	// 	return err
	// }
	return commentary_repo_impl.NewCommentarySubTaskRepoImpl().
		UpdateSubTaskStatus(ctx, subTask.Id, int(consts.SubTaskStatusFailed), msg, nil)
}

func DownloadRemoteObjectToLocalUseOs(remoteUrl string, localFile string) (string, error) {
	objName, err := GetObjectNameByHttpsUrl(remoteUrl)
	if err != nil {
		return "", err
	}
	err = hobs.GetOsClient().DownloadFile(objName, localFile)
	if err != nil {
		return "", err
	}
	err = retry.Do(func() error {
		_, err2 := os.Stat(localFile)
		return err2
	}, retry.Attempts(5), retry.Delay(3*time.Second), retry.DelayType(retry.BackOffDelay))
	return localFile, err
}

func EnsureOutputDirectory(outputPath string) error {
	if _, err := os.Stat(outputPath); os.IsNotExist(err) {
		if err := os.MkdirAll(outputPath, os.ModePerm); err != nil {
			return fmt.Errorf("failed to create output directory: %v", err)
		}
	}
	return nil
}

func timeString2Int(str string) (int64, error) {
	num, err := strconv.ParseInt(str, 10, 64)
	return num, err
}

func SrtStringTime2Ms(str string) (int64, error) {
	vecs := strings.Split(str, ",")
	if len(vecs) != 2 {
		return 0, fmt.Errorf("format error: %s", str)
	}
	heads := strings.Split(vecs[0], ":")
	if len(heads) != 3 {
		return 0, fmt.Errorf("format error: %s", str)
	}
	hour, err := timeString2Int(heads[0])
	if err != nil {
		return 0, err
	}
	minute, err := timeString2Int(heads[1])
	if err != nil {
		return 0, err
	}
	second, err := timeString2Int(heads[2])
	if err != nil {
		return 0, err
	}
	mill, err := timeString2Int(vecs[1])
	if err != nil {
		return 0, err
	}
	if hour < 0 || minute < 0 || second < 0 || mill < 0 {
		return 0, fmt.Errorf("format error: %s", str)
	}
	return hour*60*60*1000 + minute*60*1000 + second*1000 + mill, nil
}

func EnsureFile(filename string) error {
	err := retry.Do(func() error {
		fi, errF := os.Stat(filename)
		if errF != nil {
			return errF
		}
		if fi.IsDir() {
			return fmt.Errorf("file is a directory")
		}
		file, errF := os.OpenFile(filename, os.O_RDWR, DefaultFilePerm)
		if errF != nil {
			g.Log().Errorf(context.Background(), "open file failed, file: %v, err: %v", filename, errF)
			return errF
		}
		defer file.Close()
		file.Sync() // 直接刷盘
		return nil
	}, retry.Attempts(10), retry.Delay(2*time.Second), retry.DelayType(retry.FixedDelay))
	if err != nil {
		return err
	}
	return nil
}

func CopyFile(src, dst string) error {
	// 打开源文件
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	// 创建目标文件
	destinationFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destinationFile.Close()

	// 复制源文件内容到目标文件
	_, err = io.Copy(destinationFile, sourceFile)
	if err != nil {
		return err
	}

	return nil
}

func MergeAudioFromVideo(fromVideo, videoOnlyFile, newVideoFile string) error {
	avInfo, err := media.GetAudioVideoInfo(fromVideo)
	if err != nil {
		return fmt.Errorf("get audio video info failed, file: %v, err: %v", fromVideo, err)
	}
	if avInfo.AudioInfo.Duration < 0 {
		err = CopyFile(videoOnlyFile, newVideoFile)
		if err != nil {
			return fmt.Errorf("copy file failed, file: %v, err: %v", videoOnlyFile, err)
		}
	}
	// 有音频，要合成
	audioFile := fmt.Sprintf("%s.aac", fromVideo)
	_, audioFileTemp, err := media.ExtractAudio(fromVideo, audioFile)
	if err != nil {
		return fmt.Errorf("extract audio failed, file: %v, err: %v", fromVideo, err)
	}
	if audioFileTemp == "" {
		return fmt.Errorf("audio file is empty")
	}
	defer os.Remove(audioFileTemp)
	err = media.MergeAudioAndVideo(audioFile, videoOnlyFile, newVideoFile)
	if err != nil {
		return fmt.Errorf("merge audio to video failed, file: %v, err: %v", audioFileTemp, err)
	}

	return nil
}
