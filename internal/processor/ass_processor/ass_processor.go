package ass_processor

import (
	"bufio"
	"business-workflow/internal/common/config"
	"business-workflow/internal/entity/common"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/entity/vo"
	"business-workflow/internal/processor/processor_util"
	"business-workflow/internal/processor/sub_item_processor"
	"business-workflow/internal/util"
	hobs "business-workflow/internal/util/obs"
	"business-workflow/internal/util/srt_util"
	"context"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/tyr/x/utils"
	"go.uber.org/zap"
	"os"
	"runtime"
)

func GenerateAssFileV2(task *do.CommentarySubTask, items []*do.CommentarySubtitleItem) (string, error) {
	//生成oss文件到本地
	dir := GetLocalVideoPath(task)
	err := util.EnsureOutputDirectory(dir)
	if err != nil {
		g.Log().Error(context.Background(), "generateAssFile,EnsureOutputDirectory failed", zap.Error(err))
		return "", err
	}
	assFilePath := GetLocalVideoPath(task) + "/merge.ass"
	style := task.CustomSubtitleStyle

	err = SaveASS2V3(task.Id, assFilePath, style, task.TargetLangId, items)
	if err != nil {
		return "", err
	}

	//上传字幕文件，业务没用到，方便查问题
	tgtSrtObsObjectName := processor_util.GetObjectName(task.TenantId, task.Id, assFilePath, "merge_ass_tmp")
	cdnUrl, err := hobs.GetOsClient().UploadFile(assFilePath, tgtSrtObsObjectName, 1)
	if err != nil {
		g.Log().Errorf(context.Background(), "SaveASS2V3 ObsUploadAndPublic assfile failed, taskId: %v, assFilePath: %s, err: %v", task.Id, assFilePath, err)
	}
	g.Log().Infof(context.Background(), "SaveASS2V3 ObsUploadAndPublic assfile, taskId: %v, assFilePath: %s, cdnUrl: %s,lang: %s", task.Id, assFilePath, cdnUrl, task.TargetLangId)

	return assFilePath, nil
}

func SaveASS2V3(taskId int64, outputASSFile string, style *common.CustomSubtitleStyle, lang string, items []*do.CommentarySubtitleItem) error {
	subItemVos := []*vo.SubItemVO{}
	for _, entry := range items {
		if len(entry.SubItemList) == 0 {
			continue
		}
		for _, subItem := range entry.SubItemList {
			startStr := srt_util.SrtTimeMs2StringAss(subItem.BeginMs)
			endStr := srt_util.SrtTimeMs2StringAss(subItem.EndMs)
			subItemVo := &vo.SubItemVO{
				StartTime: startStr,
				EndTime:   endStr,
				Text:      subItem.SubPostText,
			}
			subItemVos = append(subItemVos, subItemVo)
		}
	}
	g.Log().Infof(context.Background(), "SaveASS2V3,taskId: %v, outputASSFile: %s, style: %v, lang: %s, before fix subItemVos:%v,subtems: %v", taskId, outputASSFile, style, lang, utils.ToJson(subItemVos), utils.ToJson(items))

	FixSubtitleItemOverlapTime(context.Background(), items)
	subItemVos = []*vo.SubItemVO{}
	for _, entry := range items {
		if len(entry.SubItemList) == 0 {
			continue
		}
		for _, subItem := range entry.SubItemList {
			startStr := srt_util.SrtTimeMs2StringAss(subItem.BeginMs)
			endStr := srt_util.SrtTimeMs2StringAss(subItem.EndMs)
			subItemVo := &vo.SubItemVO{
				StartTime: startStr,
				EndTime:   endStr,
				Text:      subItem.SubPostText,
			}
			subItemVos = append(subItemVos, subItemVo)
		}
	}
	g.Log().Infof(context.Background(), "SaveASS2V3,taskId: %v, outputASSFile: %s, style: %v, lang: %s, after fix subItemVos:%v,subtems: %v", taskId, outputASSFile, style, lang, utils.ToJson(subItemVos), utils.ToJson(items))
	outFile, err := os.Create(outputASSFile)
	if err != nil {
		g.Log().Errorf(context.Background(), "SaveASS2V3, Create outputASSFile failed, err: %v, outputASSFile: %s", err, outputASSFile)
		return err
	}
	defer outFile.Close()
	g.Log().Infof(context.Background(), "SaveASS2V3, Create outputASSFile success, outputASSFile: %s", outputASSFile)
	writer := bufio.NewWriter(outFile)

	writer.WriteString("[Script Info]\n")
	writer.WriteString("Title: Converted\n")
	writer.WriteString("Original Script: Go\n")
	writer.WriteString("ScriptType: v4.00+\n")
	writer.WriteString("Collisions: Normal\n")
	writer.WriteString("PlayDepth: 0\n")
	writer.WriteString("ScaledBorderAndShadow: Yes\n")
	writer.WriteString(fmt.Sprintf("PlayResX: %d\n", style.Width))
	writer.WriteString(fmt.Sprintf("PlayResY: %d\n", style.Height))
	// 关键行：只在 \N 处换行
	writer.WriteString("WrapStyle: 2\n")
	writer.WriteString("\n")

	style.FontName = sub_item_processor.GetLocalFontNameByName2(style.FontName, lang)

	// Styles
	writer.WriteString("[V4+ Styles]\n")
	writer.WriteString("Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding\n")
	writer.WriteString(fmt.Sprintf("Style: Default,%s,%d,%s,%s,%s,%s,%d,%d,%d,0,%d,%d,0,0,%d,%d,%d,%d,%d,%d,%d,1\n",
		style.FontName, style.FontSize, style.PrimaryColor, style.SecondaryColour, style.OutlineColour, style.BackColour,
		style.Bold, style.Italic, style.Underline,
		style.ScaleX, style.ScaleY,
		style.BorderStyle, style.Outline, style.Shadow, style.Alignment, style.MarginL, style.MarginR, style.MarginV))
	writer.WriteString("\n")
	// Events
	writer.WriteString("[Events]\n")
	writer.WriteString("Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n")

	gCustom := ""
	if style.PosX > 0 || style.PosY > 0 {
		gCustom = fmt.Sprintf("{\\an%d\\pos(%d,%d)}", style.Alignment, style.PosX, style.PosY)
	}
	for _, subtitleItem := range items {
		//字幕在SubItemList里
		if len(subtitleItem.SubItemList) == 0 { //该片段被完全重叠的情况
			g.Log().Infof(context.Background(), "SubItemList is empty, taskId: %v, subIdx: %v", subtitleItem.SubTaskId, subtitleItem.ItemIdx)
			continue
		}
		for _, subItem := range subtitleItem.SubItemList {
			startStr := srt_util.SrtTimeMs2StringAss(subItem.BeginMs)
			endStr := srt_util.SrtTimeMs2StringAss(subItem.EndMs)
			writer.WriteString(fmt.Sprintf("Dialogue: 0,%s,%s,Default, ,0000,0000,0000, ,%s\n",
				startStr, endStr, fmt.Sprintf("%s%s", gCustom, subItem.SubPostText)))
		}
	}
	g.Log().Infof(context.Background(), "SaveASS2V3 done,taskId: %v, outputASSFile: %s", taskId, outputASSFile)

	return writer.Flush()
}

// 修正片段重叠的时间戳
func FixSubtitleItemOverlapTime(ctx context.Context, items []*do.CommentarySubtitleItem) {
	//1. 判断下一个片段的开始时间是否小于当前片段的结束时间
	for i := 0; i < len(items)-1; i++ {
		next := items[i+1]
		cur := items[i]
		curEndMs, err2 := srt_util.SrtStringTime2Ms(cur.SubtitleEndStr)
		if err2 != nil {
			g.Log().Errorf(ctx, "FixSubtitleItemOverlapTime, SrtStringTime2Ms failed, err: %v, SubtitleEndStr: %s", err2, cur.SubtitleEndStr)
			continue
		}
		nextStartMs, err3 := srt_util.SrtStringTime2Ms(next.SubtitleStartStr)
		if err3 != nil {
			g.Log().Errorf(ctx, "FixSubtitleItemOverlapTime, SrtStringTime2Ms failed, err: %v, SubtitleStartStr: %s", err3, next.SubtitleStartStr)
			continue
		}
		if nextStartMs >= curEndMs {
			continue
		}
		g.Log().Infof(ctx, "FixSubtitleItemOverlapTime,taskId:%d, nextStartMs: %d, curEndMs: %d, nextStartStr: %s, curEndStr: %s", cur.SubTaskId, nextStartMs, curEndMs, next.SubtitleStartStr, cur.SubtitleEndStr)
		//找子片段被切断的位置,被切的子片段之后的子片段删除
		cutIdx := -1
		for k, subItem := range cur.SubItemList {
			if nextStartMs >= subItem.BeginMs && nextStartMs <= subItem.EndMs {
				g.Log().Infof(ctx, "FixSubtitleItemOverlapTime, cut subItem,taskId:%d, nextStartMs: %d, subItem.BeginMs: %d, subItem.EndMs: %d, subItem.SubPostText: %s", cur.SubTaskId, nextStartMs, subItem.BeginMs, subItem.EndMs, subItem.SubPostText)
				subItem.EndMs = nextStartMs
				cutIdx = k
				break
			}
		}
		if cutIdx != -1 {
			cur.SubItemList = cur.SubItemList[:cutIdx+1]
		} else {
			cur.SubItemList = nil
		}
	}

}

func GetLocalVideoPath(task *do.CommentarySubTask) string {
	if true {
		if runtime.GOOS == "windows" {
			return "./"
		}
	}
	format := config.GetConfig().LocalPath + "/%v"
	return fmt.Sprintf(format, task.Id)
}
