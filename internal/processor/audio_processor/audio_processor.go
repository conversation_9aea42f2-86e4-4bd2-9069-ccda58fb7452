package audio_processor

import (
	"business-workflow/internal/common"
	"business-workflow/internal/common/config"
	"business-workflow/internal/processor/processor_util"
	"business-workflow/internal/util/media"
	"bytes"
	"context"
	"errors"
	"fmt"
	"math"
	"os"
	"os/exec"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"time"

	"business-workflow/internal/consts"
	hobs "business-workflow/internal/util/obs"

	"github.com/avast/retry-go"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/google/uuid"
	"github.com/panjf2000/ants/v2"
	"gitlab.ttyuyin.com/x-project/server/basic-service-set/x-common-pkg/concurrent"
)

type AudioConfig struct {
	Speed        float32 `json:"speed"`          // 语速
	VolumeGainDB float32 `json:"volume_gain_db"` // 音量加减值
}

type AudioItem struct {
	TtsUrl           string
	SubtitleStartStr string
	ItemIdx          int
	Config           *AudioConfig
}

type AudioMergeTask struct {
	TaskId int64
	//SubTaskId    int64
	BgUrl        string
	TtsUrls      []*AudioItem
	DisableBgm   bool //关闭背景单
	OutLocalPath string
}

type BriefAudio struct {
	Start       int64  // 音频起始时间，毫秒
	Path        string // 路径
	SoundVolume int
}

const (
	bgmDownDb float32 = -18
)

func GetLocalVideoPathById(ctx context.Context, taskId int64) string {
	return fmt.Sprintf("%s/%v", config.GetConfig().LocalPath, taskId)
}

func MergeAudio(ctx context.Context, task *AudioMergeTask, threadNum int) (string, error) {
	audioFile, err := buildAudioFile(ctx, task, threadNum)
	task.OutLocalPath = audioFile
	return audioFile, err
}

func buildAudioFile(ctx context.Context, task *AudioMergeTask, threadNum int) (string, error) {
	g.Log().Infof(ctx, "[buildAudioFile] start build audio file, taskId=%v, hasBackground=%v, len(items)=%v",
		task.TaskId, task.DisableBgm, len(task.TtsUrls))
	outputFilePath := GetLocalVideoPathById(ctx, task.TaskId)

	bgAudioPathDir := fmt.Sprintf("%s/background_audio", outputFilePath)
	vocalOutputDir := fmt.Sprintf("%s/vocal", outputFilePath)
	err := common.EnsureOutputDirectory(bgAudioPathDir, vocalOutputDir)
	if err != nil {
		return "", fmt.Errorf("EnsureOutputDirectory error: %s", err.Error())
	}
	// 1. 下载背景音频
	bgAudioFile := fmt.Sprintf("%s/backgroup_%s.%s", bgAudioPathDir, uuid.New().String(), filepath.Base(task.BgUrl))
	begin := time.Now()
	if !task.DisableBgm {
		bgAudioFile, err = processor_util.DownloadRemoteObjectToLocalUseOs(task.BgUrl, bgAudioFile)
		if err != nil {
			g.Log().Errorf(ctx, "[buildAudioFile] download background audio err: %s", err.Error())
			return "", err
		}
		adjustFile := fmt.Sprintf("%s/backgroup_adjust_%s.aac", bgAudioPathDir, uuid.New().String())
		newBgFile, err := AdjustAudioSpeedAndVolume(ctx, bgAudioFile, adjustFile, 0, bgmDownDb)
		if err != nil {
			g.Log().Errorf(ctx, "[buildAudioFile] AdjustAudioSpeedAndVolume background audio err: %s", err.Error())
			return "", err
		}
		bgAudioFile = newBgFile
		g.Log().Infof(ctx, "[buildAudioFile] download background audio cost: %vms, taskId=%d", time.Since(begin).Milliseconds(), task.TaskId)
	}
	var vocalFileMap sync.Map

	pool, err := ants.NewPool(threadNum)
	if err != nil {
		panic(err)
	}
	defer pool.Release()
	g.Log().Infof(ctx, "[buildAudioFile] action 1 start download audio file, taskId=%v, hasBackground=%v", task.TaskId, task.DisableBgm)
	// 3. 下载 tts
	wg := new(sync.WaitGroup)
	for _, val := range task.TtsUrls {
		wg.Add(1)
		func(ctx context.Context, item *AudioItem) {
			pool.Submit(func() {
				defer wg.Done()
				vocalUrl := item.TtsUrl
				vocalFile := fmt.Sprintf("%s/%s_%v_tts", vocalOutputDir, uuid.New().String(), item.ItemIdx)
				begin := time.Now()
				err = DownloadMediaWithRetry(ctx, vocalUrl, vocalFile)
				if err != nil {
					g.Log().Errorf(ctx, "[buildAudioFile] download tts  taskId: %v, subItemIdx: %v, err: %s",
						task.TaskId, item.ItemIdx, err.Error())
					return
				}
				g.Log().Infof(ctx, "[buildAudioFile] download tts cost: %vms, taskId=%d, itemIdx=%d", time.Since(begin).Milliseconds(), task.TaskId, item.ItemIdx)
				startTime, _ := processor_util.SrtStringTime2Ms(item.SubtitleStartStr)
				//1. 根据配置是否要调整速度？
				//2. 调整后的音频时间是否超出视频时间？
				if item.Config != nil {
					adjustFile := fmt.Sprintf("%s/%v_%s_adjust.aac", vocalOutputDir, item.ItemIdx, uuid.New().String())
					newVocalFile, err2 := AdjustAudioSpeedAndVolume(ctx, vocalFile, adjustFile, item.Config.Speed, item.Config.VolumeGainDB)
					if err2 != nil {
						g.Log().Errorf(ctx, "[buildAudioFile] adjust audio speed err, taskId: %v, subItemIdx: %v, err: %s",
							task.TaskId, item.ItemIdx, err2.Error())
					} else {
						vocalFile = newVocalFile
					}
				}
				vocalFileMap.Store(item.ItemIdx, BriefAudio{Path: vocalFile, Start: startTime})
			})
		}(ctx, val)
	}
	wg.Wait()
	g.Log().Infof(ctx, "[buildAudioFile] action 2 download audio file cost: %vms, taskId: %v", time.Since(begin).Milliseconds(), task.TaskId)
	audioList := make([]*BriefAudio, 0, len(task.TtsUrls)+1)
	if !task.DisableBgm {
		audioList = append(audioList, &BriefAudio{Path: bgAudioFile, Start: 0, SoundVolume: 20})
	}
	// 所有 goroutine 执行完成后，从 map 中取出 vocalFile
	vocalFileMap.Range(func(key, value interface{}) bool {
		briefAudio := value.(BriefAudio)
		audioList = append(audioList, &briefAudio)
		return true
	})
	if len(audioList) == 0 {
		g.Log().Warningf(ctx, "[buildAudioFile] no audio task found, taskId: %v", task.TaskId)
		return "", nil
	}
	mergedAudioPath, err := mergeAudio(ctx, task, audioList, vocalOutputDir)
	if err != nil {
		g.Log().Errorf(ctx, "[buildAudioFile] merge audio failed, taskId: %v err: %s", task.TaskId, err.Error())
		return "", err
	}
	g.Log().Infof(ctx, "[buildAudioFile] action 3 merge aac cost: %vms, taskId: %v, mergeAudioFile: %v",
		time.Since(begin).Milliseconds(), task.TaskId, mergedAudioPath)
	return mergedAudioPath, nil
}

func mergeAudio(ctx context.Context, task *AudioMergeTask, audioList []*BriefAudio, vocalOutputDir string) (string, error) {
	// 判断文件的大小，文件太小（为0）的不参与合成
	g.Log().Infof(ctx, "[buildAudioFile] merge audio , taskId: %v, audioList: %v", task.TaskId, audioList)
	zeroIndexMap := make(map[int]struct{})
	for idx, audioFile := range audioList {
		fi, err := os.Stat(audioFile.Path)
		if err != nil {
			return "", fmt.Errorf("file not found, err: %s", err.Error())
		}
		if fi.Size() == 0 {
			zeroIndexMap[idx] = struct{}{}
			g.Log().Warningf(ctx, "audio tts file size is zero, idx: %v, start: %v, path: %v", idx, audioFile.Start, audioFile.Path)
		}
	}
	if len(zeroIndexMap) > 0 {
		newList := make([]*BriefAudio, 0, len(audioList))
		for idx, audioFile := range audioList {
			_, ok := zeroIndexMap[idx]
			if !ok {
				newList = append(newList, audioFile)
			}
		}
		audioList = newList
	}
	if len(audioList) == 0 {
		return "", nil
	}
	// 合并音频
	mergedAudioPrefix := "merged_audio_" + uuid.New().String()
	mergedAudioPath, err := MergeAudioWithStep(audioList, vocalOutputDir, mergedAudioPrefix, ".aac")
	if err != nil {
		g.Log().Errorf(ctx, "[buildAudioFile] merge audio err: %s", err.Error())
		return "", err
	}
	g.Log().Infof(ctx, "[buildAudioFile] merge audio success, taskId: %v, mergedAudioPath: %v", task.TaskId, mergedAudioPath)
	err = EnsureMediaInfo(ctx, mergedAudioPath, 15, 2)
	if err != nil {
		g.Log().Errorf(ctx, "[buildAudioFile] EnsureMediaInfo failed, audioFile: %v, err: %s, vocalOutputDir: %v", mergedAudioPath, err.Error(), vocalOutputDir)
		mergedAudioPath, err = MergeAudioWithStep(audioList, vocalOutputDir, mergedAudioPrefix, ".mp3")
		if err != nil {
			g.Log().Errorf(ctx, "[buildAudioFile] merge audio with aac err: %s", err.Error())
			return "", err
		}
		err = EnsureMediaInfo(ctx, mergedAudioPath, 10, 3)
		if err != nil {
			g.Log().Errorf(ctx, "[buildAudioFile] EnsureMediaInfo failed, vocalOutputDir:%v, mergedAudioPath: %v, err: %v", vocalOutputDir, mergedAudioPath, err)
			return "", err
		}
	}
	g.Log().Infof(ctx, "[buildAudioFile] merge audio success2, taskId: %v, mergedAudioPath: %v", task.TaskId, mergedAudioPath)
	return mergedAudioPath, nil
}

func AdjustAudioSpeedAndVolume(ctx context.Context, inputPath, outputPath string, speed, db float32) (string, error) {
	afStr := ""
	if speed > 0.01 {
		//afStr = fmt.Sprintf("rubberband=tempo=%0.2f:pitch=1.0", speed)
		afStr = fmt.Sprintf("atempo=%0.2f", speed)
	}
	if math.Abs(float64(db)) > 0.01 {
		if afStr != "" {
			afStr += fmt.Sprintf(",volume=%0.2fdB", db)
		} else {
			afStr = fmt.Sprintf("volume=%0.2fdB", db)
		}
	}
	if afStr == "" {
		return inputPath, nil
	}
	cmd := exec.Command("ffmpeg",
		"-i", inputPath,
		"-af", afStr,
		"-b:a", "192k",
		"-y", outputPath)
	cmdStr := strings.Join(cmd.Args, " ")
	fmt.Println("AdjustAudioSpeedAndVolume", cmdStr)
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return "", fmt.Errorf("failed to AdjustAudioSpeedAndVolume, err: %s, inputPath: %s, stderr: %s",
			err.Error(), inputPath, stdoutStderr)
	}
	return outputPath, nil
}

func EnsureMediaInfo(ctx context.Context, filePath string, retryCount, delay int) (err error) {
	err = retry.Do(func() error {
		_, errOs := media.GetAudioVideoInfo(filePath)
		if errOs != nil {
			return errOs
		}
		return nil
	}, retry.Attempts(uint(retryCount)), retry.Delay(time.Duration(delay)*time.Second), retry.DelayType(retry.FixedDelay))
	return err
}

func DownloadMediaWithRetry(ctx context.Context, targetUrl, localPath string) error {
	err := retry.Do(func() error {
		objName, err := processor_util.GetObjectNameByHttpsUrl(targetUrl)
		// 首先使用OSS下载
		if err != nil {
			return err
		}
		err = hobs.GetOsClient().DownloadFile(objName, localPath)
		if err != nil {
			return err
		}
		//检测文件是否存在，防止还没有落盘完成的情况
		return EnsureMediaInfo(ctx, localPath, 15, 2)
	}, retry.Attempts(3), retry.DelayType(retry.FixedDelay))
	return err
}

func MergeAudioWithStep(audioList []*BriefAudio, outputPath, outputPrefix, ext string) (string, error) {
	var audioFileName string
	g.Log().Infof(context.TODO(), "MergeAudioWithStep start, audioList len: %v, outputPath: %v", len(audioList), outputPath)
	// 确保输出目录存在
	if err := processor_util.EnsureOutputDirectory(outputPath); err != nil {
		return "", err
	}
	// 根据开始时间进行排序
	sort.Slice(audioList, func(i, j int) bool {
		return audioList[i].Start < audioList[j].Start
	})

	audioFileName = filepath.Join(outputPath, outputPrefix+ext)
	codecName := "libmp3lame"
	if ext == ".aac" {
		codecName = "aac"
	}
	/*
		ffmpeg -i bg.mp3 -i audio1.mp3 -i audio2.mp3 -i audio3.mp3 -filter_complex
		"[0]adelay=0|0[v0];[1]adelay=1486|1486[v1];[2]adelay=5679|5679[v2];
		[3]adelay=11520|11520[v3];[v0][v1][v2][v3]amix=inputs=4:duration=longest"
		-c:a libmp3lame -b:a 128k output.mp3
	*/
	// 分批合成小的音频文件
	batchSize := len(audioList) / consts.BatchVocalCount
	if batchSize*consts.BatchVocalCount < len(audioList) {
		batchSize = batchSize + 1
	}
	if batchSize == 1 {
		return MergeAudioWith(audioList, outputPath, outputPrefix, ext, true)
	}
	mergeAudios := make([]*BriefAudio, 0, batchSize)
	lock := sync.Mutex{}
	//wg := &sync.WaitGroup{}
	wg := concurrent.New(context.TODO(), 20)
	// 分批合成
	for i := 0; i < batchSize; i++ {
		if i*consts.BatchVocalCount >= len(audioList) {
			break
		}
		end := (i + 1) * consts.BatchVocalCount
		if end > len(audioList) {
			end = len(audioList)
		}
		subAudioList := audioList[i*consts.BatchVocalCount : end]
		subFileName := fmt.Sprintf("%s_subAudioFile_%d", outputPrefix, i+1)
		//wg.Add(1)
		func(subAudioList []*BriefAudio, subFileName string, firstStart int64) {
			wg.Go(func() error {
				subFileName, err := MergeAudioWith(subAudioList, outputPath, subFileName, ext, false)
				if err != nil {
					g.Log().Errorf(context.TODO(), "MergeAudioWith sub error: %v, outputpath: %v, subFileName: %v", err, outputPath, subFileName)
				} else {
					lock.Lock()
					defer lock.Unlock()
					mergeAudios = append(mergeAudios, &BriefAudio{
						Path:  subFileName,
						Start: firstStart,
					})
				}
				return nil
			})
			//defer wg.Done()

		}(subAudioList, subFileName, subAudioList[0].Start)
	}
	wg.Wait()
	if len(mergeAudios) != batchSize || batchSize == 1 {
		g.Log().Errorf(context.TODO(), "MergeAudioWithStep mergeAudios len error: %v, batchSize: %v", len(mergeAudios), batchSize)
		return "", errors.New("mergeAudios len error")
	}
	// 合成最终的音频文件
	var args []string
	inputList, filter := []string{}, bytes.Buffer{}
	for idx, audio := range mergeAudios {
		inputList = append(inputList, "-i", audio.Path)
		filter.WriteString(fmt.Sprintf("[%d]adelay=%d|%d[v%d];", idx, audio.Start, audio.Start, idx))
	}
	for i := 0; i < len(mergeAudios); i++ {
		filter.WriteString(fmt.Sprintf("[v%d]", i))
	}
	filter.WriteString(fmt.Sprintf("amix=inputs=%d:duration=longest:normalize=0", len(mergeAudios)))
	args = append(args, inputList...)
	args = append(args, "-filter_complex", filter.String())
	args = append(args, "-ar", "48000", "-ac", "2", "-c:a", codecName, "-b:a", "128k", "-y", audioFileName)

	cmd := exec.Command("ffmpeg", args...)

	g.Log().Infof(context.TODO(), "MergeAudioWithStep command:%s", strings.Join(cmd.Args, " "))

	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		s := fmt.Sprintf("failed to merge audio: %v\noutput: %s", err, stdoutStderr)
		return "", errors.New(s)
	}

	return audioFileName, nil
}

func MergeAudioWith(audioList []*BriefAudio, outputPath, outputPrefix, ext string, fromBegin bool) (string, error) {
	var audioFileName string
	g.Log().Infof(context.TODO(), "MergeAudioWith start, audioList len: %v, outputPath: %v, outputPrefix: %v", len(audioList), outputPath, outputPrefix)
	// 确保输出目录存在
	if err := processor_util.EnsureOutputDirectory(outputPath); err != nil {
		return "", err
	}
	//每个音频的开始时间为减去第一个的开始时间，这样可以保证音频的开始时间是0
	if !fromBegin {
		start := audioList[0].Start
		for _, audio := range audioList {
			audio.Start -= start
		}
	}
	if len(audioList) == 1 {
		return audioList[0].Path, nil
	}
	audioFileName = filepath.Join(outputPath, outputPrefix+ext)
	codecName := "libmp3lame"
	if ext == ".aac" {
		codecName = "aac"
	}
	begin := time.Now()
	g.Log().Infof(context.TODO(), "MergeAudioWith start, audioFileName: %v, audioList len: %v", audioFileName, len(audioList))
	/*
		ffmpeg -i bg.mp3 -i audio1.mp3 -i audio2.mp3 -i audio3.mp3 -filter_complex
		"[0]adelay=0|0[v0];[1]adelay=1486|1486[v1];[2]adelay=5679|5679[v2];
		[3]adelay=11520|11520[v3];[v0][v1][v2][v3]amix=inputs=4:duration=longest"
		-c:a libmp3lame -b:a 128k output.mp3
	*/
	var args []string
	inputList, filter := []string{}, bytes.Buffer{}
	for idx, audio := range audioList {
		inputList = append(inputList, "-i", audio.Path)
		filter.WriteString(fmt.Sprintf("[%d]adelay=%d|%d[v%d];", idx, audio.Start, audio.Start, idx))
	}
	for i := 0; i < len(audioList); i++ {
		filter.WriteString(fmt.Sprintf("[v%d]", i))
	}
	filter.WriteString(fmt.Sprintf("amix=inputs=%d:duration=longest:normalize=0", len(audioList)))
	args = append(args, inputList...)
	args = append(args, "-filter_complex", filter.String())
	args = append(args, "-ar", "48000", "-ac", "2", "-c:a", codecName, "-b:a", "128k", "-y", audioFileName)

	cmd := exec.Command("ffmpeg", args...)

	g.Log().Infof(context.TODO(), "MergeAudio command:%s", strings.Join(cmd.Args, " "))

	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		s := fmt.Sprintf("failed to merge audio: %v\noutput: %s", err, stdoutStderr)
		return "", errors.New(s)
	}
	g.Log().Infof(context.TODO(), "MergeAudioWith end, audioFileName: %v, cost: %v", audioFileName, time.Since(begin).Milliseconds())
	return audioFileName, nil
}
