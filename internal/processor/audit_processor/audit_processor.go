package audit_processor

import (
	"business-workflow/internal/common/config"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/utils/audit_util"
	"sync"
)

const (
	AuditSceneKeyCommentaryVideoUpload audit_util.BizScene = "commentary_video_upload" // 视频解说视频上传
	AuditSceneKeyCommentaryAudioUpload audit_util.BizScene = "commentary_audio_upload" // 视频解说音频上传            // 视频解说视频
	AuditSceneKeyCommentaryText        audit_util.BizScene = "commentary_text"         // 解说文本审核
)

var (
	auditHandler     *audit_util.AuditHandler
	auditHandlerOnce sync.Once
)

func GetAuditProcessor() *audit_util.AuditHandler {
	auditHandlerOnce.Do(func() {
		cfg := config.GetConfig()
		scene2strategy := map[audit_util.AuditScene]audit_util.AuditStrategy{
			cfg.Audit.Scene[AuditSceneKeyCommentaryVideoUpload].SceneCode: audit_util.GetAuditStrategy(audit_util.AuditStrategyUseMachineAuditResult),
			cfg.Audit.Scene[AuditSceneKeyCommentaryAudioUpload].SceneCode: audit_util.GetAuditStrategy(audit_util.AuditStrategyUseMachineAuditResult),
			cfg.Audit.Scene[AuditSceneKeyCommentaryText].SceneCode:        audit_util.GetAuditStrategy(audit_util.AuditStrategyUseMachineAuditResult),
		}
		scene2AuditType := map[audit_util.AuditScene]audit_util.AuditType{
			cfg.Audit.Scene[AuditSceneKeyCommentaryVideoUpload].SceneCode: audit_util.GetAuditType(cfg.Audit.Scene[AuditSceneKeyCommentaryVideoUpload].AuditType),
			cfg.Audit.Scene[AuditSceneKeyCommentaryAudioUpload].SceneCode: audit_util.GetAuditType(cfg.Audit.Scene[AuditSceneKeyCommentaryAudioUpload].AuditType),
			cfg.Audit.Scene[AuditSceneKeyCommentaryText].SceneCode:        audit_util.GetAuditType(cfg.Audit.Scene[AuditSceneKeyCommentaryText].AuditType),
		}

		scene2AuditScene := map[audit_util.BizScene]audit_util.AuditScene{
			AuditSceneKeyCommentaryVideoUpload: cfg.Audit.Scene[AuditSceneKeyCommentaryVideoUpload].SceneCode,
			AuditSceneKeyCommentaryAudioUpload: cfg.Audit.Scene[AuditSceneKeyCommentaryAudioUpload].SceneCode,
			AuditSceneKeyCommentaryText:        cfg.Audit.Scene[AuditSceneKeyCommentaryText].SceneCode,
		}
		auditHandler = audit_util.NewAuditHandler(&audit_util.AuditConfig{
			Host:                     cfg.Audit.Host,
			TenantId:                 cfg.Audit.TenantId,
			AppCode:                  cfg.Audit.AppCode,
			AuditScene2AuditStrategy: scene2strategy,
			AuditScene2AuditType:     scene2AuditType,
			BizScene2AuditScene:      scene2AuditScene,
			IntervalMs:               cfg.Audit.IntervalMs,
			TimeoutMs:                cfg.Audit.TimeoutMin * 60 * 1000,
		})
	})
	return auditHandler
}

func EnableAudit() bool {
	return config.GetConfig().Audit.Enable
}
