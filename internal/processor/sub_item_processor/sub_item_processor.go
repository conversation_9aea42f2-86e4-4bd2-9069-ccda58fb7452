package sub_item_processor

import (
	"business-workflow/internal/entity/common"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/util"
	"business-workflow/internal/util/font_util"
	"business-workflow/internal/util/srt_util"
	"context"
	"fmt"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	wc "github.com/veer66/mapkha"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/utils/slice_util"
	"gitlab.ttyuyin.com/tyr/x/utils"
	"go.uber.org/zap"
)

var (
	wordcutHandler map[string]IWordCut

	//字体名称映射
	fontNameMap = map[string]string{
		"HarmonyOS_Sans": "HarmonyOS Sans SC",
		"HarmonyOS Sans": "HarmonyOS Sans SC",
	}

	//符号列表
	Symbols = []rune{
		'!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '-', '_', '=', '+', '[', ']', '€',
		'{', '}', '\\', '|', ';', ':', '"', ',', '.', '<', '>', '/', '?', '`', '~', ' ',
		'。', '，', '；', '！', '？', '【', '】', '￥',
	}

	lineSymMap = map[rune]struct{}{
		'。': {},
		'，': {},
		'；': {},
		'！': {},
		'？': {},
		';': {},
		'.': {},
		'!': {},
		'?': {},
		',': {},
	}

	SymbolMap map[rune]struct{}

	commonFontNames = []string{
		"HarmonyOS_Sans",
		"Alibaba_PuHuiTi",
		"MiSans",
	}
	//支持这些和common里的
	langSupportFontNames = map[string][]string{
		"zh": {"HarmonyOS_Sans", "SourceHanSerifCN", "SourceHanSansCN", "Alibaba_PuHuiTi", "MiSans"},
		"en": {"HarmonyOS_Sans", "Georgia", "Verdana", "Calibri", "Alibaba_PuHuiTi", "MiSans"},
		"ru": {"HarmonyOS_Sans", "Georgia", "Verdana", "Calibri", "Roboto", "Alibaba_PuHuiTi", "MiSans"},
		"vi": {"Alibaba Sans Viet"},
		"ko": {"Alibaba Sans KR"},
		"ja": {"Alibaba Sans JP"},
		"th": {"AlibabaSansThai-Rg"},
		"hi": {"NotoSansDevanagari-Regular"},
		"km": {"MiSansKhmer-Normal"},
		"my": {"MiSansMyanmar-Normal"},
		"ta": {"Karla Tamil Upright"},
		"ar": {"NotoNaskhArabic-Rg"},
		"no": {"NotoSans-Regular"},
	}
)

func GetDefaultFontNameByLang(lang string) string {
	langs := langSupportFontNames[lang]
	if len(langs) > 0 {
		return langs[0]
	}
	return commonFontNames[0]
}

type IWordCut interface {
	Wordcut(text string) []string
	GetName() string
}

type ThWordCut struct {
	thWordcut *wc.Wordcut
}

func (w *ThWordCut) Wordcut(text string) (vecs []string) {
	//默认至少分两行
	vecs = []string{text}
	if len(text) <= 1 {
		return
	}
	if len(text) > 10 {
		vecs = []string{text[:5], text[5:]}
	}
	defer func() {
		if r := recover(); r != nil {
			g.Log().Error(context.Background(), "panic", zap.Any("panic", r))
		}
	}()
	vecs = w.thWordcut.Segment(text)
	return
}

func (w *ThWordCut) GetName() string {
	return "ThWordCut"
}

func NewThWordCut(wc *wc.Wordcut) *ThWordCut {
	return &ThWordCut{
		thWordcut: wc,
	}
}

type ViWordCut struct{}

func (w *ViWordCut) Wordcut(text string) (vecs []string) {
	//默认至少分两行
	//使用空格或者符号换行
	vecs = splitWestWords(text)
	return
}

func isSymbol(c rune) bool {
	_, ok := SymbolMap[c]
	return ok
}
func parseSymbols(text string) []string {
	if len(text) < 2 {
		return []string{text}
	}
	vecs := make([]string, 0)
	runes := []rune(text)
	word := make([]rune, 0)
	for i := 0; i < len(runes); i++ {
		symbol := isSymbol(runes[i])
		if symbol {
			if len(word) > 0 {
				vecs = append(vecs, string(word))
			}
			vecs = append(vecs, string(runes[i:i+1]))
			word = make([]rune, 0)
		} else {
			word = append(word, runes[i])
		}
	}
	if len(word) > 0 {
		vecs = append(vecs, string(word))
	}
	return vecs
}
func splitWestWords(text string) []string {
	//照空格和符号来拆分
	vecs := strings.Split(text, " ")
	texts := make([]string, 0, len(vecs))
	//上面获得的结果，单词前面或者后面可能有符号
	for idx, val := range vecs {
		words := parseSymbols(val)
		texts = append(texts, words...)
		//插入一个空格
		if idx != len(vecs)-1 {
			texts = append(texts, " ")
		}
	}
	return texts
}
func (w *ViWordCut) GetName() string {
	return "ViWordCut"
}

func NewViWordCut() *ViWordCut {
	return &ViWordCut{}
}

// RuneWordCut 基于rune字符的分词器
// 将文本按照rune字符逐个拆分，适用于中文、日文、韩文等字符
type RuneWordCut struct {
	// 是否保留空白字符
	KeepWhitespace bool
	// 是否合并连续的数字
	MergeNumbers bool
	// 是否合并连续的英文字母
	MergeLetters bool
	// 是否将标点符号合并到前一个词
	MergePunctuation bool
}

// Wordcut 实现IWordCut接口，将文本按rune字符拆分
func (r *RuneWordCut) Wordcut(text string) []string {
	if len(text) == 0 {
		return []string{}
	}

	// 将文本转换为rune数组
	runes := []rune(text)
	result := make([]string, 0, len(runes))

	i := 0
	for i < len(runes) {
		currentRune := runes[i]

		// 处理空白字符
		if isWhitespace(currentRune) {
			if r.KeepWhitespace {
				result = append(result, string(currentRune))
			}
			i++
			continue
		}

		// 处理标点符号合并
		if r.MergePunctuation && isPunctuation(currentRune) && len(result) > 0 {
			// 将标点符号合并到前一个词
			lastIndex := len(result) - 1
			result[lastIndex] = result[lastIndex] + string(currentRune)
			i++
			continue
		}

		// 处理连续数字
		if r.MergeNumbers && isDigit(currentRune) {
			numberStr := r.extractNumbers(runes, &i)
			result = append(result, numberStr)
			continue
		}

		// 处理连续英文字母
		if r.MergeLetters && isLetter(currentRune) {
			letterStr := r.extractLetters(runes, &i)
			result = append(result, letterStr)
			continue
		}

		// 默认情况：单个字符
		result = append(result, string(currentRune))
		i++
	}

	return result
}

func (w *RuneWordCut) GetName() string {
	return "RuneWordCut"
}

// extractNumbers 提取连续的数字
func (r *RuneWordCut) extractNumbers(runes []rune, index *int) string {
	start := *index
	for *index < len(runes) && isDigit(runes[*index]) {
		(*index)++
	}
	return string(runes[start:*index])
}

// extractLetters 提取连续的英文字母
func (r *RuneWordCut) extractLetters(runes []rune, index *int) string {
	start := *index
	for *index < len(runes) && isLetter(runes[*index]) {
		(*index)++
	}
	return string(runes[start:*index])
}

// isWhitespace 判断是否为空白字符
func isWhitespace(r rune) bool {
	return r == ' ' || r == '\t' || r == '\n' || r == '\r'
}

// isDigit 判断是否为数字
func isDigit(r rune) bool {
	return r >= '0' && r <= '9'
}

// isLetter 判断是否为英文字母
func isLetter(r rune) bool {
	return (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z')
}

// isPunctuation 判断是否为标点符号
func isPunctuation(r rune) bool {
	// 常见的标点符号
	commonPuncts := []rune{
		'.', ',', '!', '?', ';', ':', '"', '\'', '(', ')', '[', ']', '{', '}',
		'-', '_', '=', '+', '*', '&', '^', '%', '$', '#', '@', '~', '`',
		'/', '\\', '|', '<', '>',
		// 中文标点符号
		'。', '，', '！', '？', '；', '：', '"', '"', '\u2018', '\u2019', '（', '）',
		'【', '】', '《', '》', '、', '…', '—', '～',
	}

	for _, punct := range commonPuncts {
		if r == punct {
			return true
		}
	}
	return false
}

// NewRuneWordCut 创建新的RuneWordCut实例
func NewRuneWordCut() *RuneWordCut {
	return &RuneWordCut{
		KeepWhitespace:   true, // 默认保留空白字符
		MergeNumbers:     true, // 默认合并数字
		MergeLetters:     true, // 默认合并字母
		MergePunctuation: true, // 默认合并标点符号
	}
}

// 基本的换行单元，可能是单个字，可能是一个单词，也可能是一个短语（分词的情况下，如泰语）
type WrapUnit struct {
	StartMs int64
	Weight  int
	EndMs   int64
	Text    string
}

func init() {
	dict, e := wc.LoadDict("/app/configs/tdict-std.txt") //wc.LoadDefaultDict()
	if e != nil {
		fmt.Println("加载默认词典失败", e)
		//panic(e)
	}

	//初始化各种语文的分词器
	thWordcut := NewThWordCut(wc.NewWordcut(dict))
	viWordcut := NewViWordCut()
	runeWordcut := NewRuneWordCut()

	wordcutHandler = map[string]IWordCut{
		"th": thWordcut,
		//"vi": viWordcut,
		//"ru": viWordcut,
		//"sk": viWordcut,
		//"uk": viWordcut,
		//"bg": viWordcut,
		//"el": viWordcut,
		//"hy": viWordcut,
		//"fr": viWordcut,
		//"cs": viWordcut,
		//"hr": viWordcut,
		//"da": viWordcut,
		//"tr": viWordcut,
		//"pl": viWordcut,
		//"de": viWordcut,
		//"en": viWordcut,
		// 使用rune分词器的语言
		"zh":         runeWordcut, // 中文
		"ja":         runeWordcut, // 日文
		"ko":         runeWordcut, // 韩文
		"zh-cn":      runeWordcut, // 简体中文
		"zh-tw":      runeWordcut, // 繁体中文
		"yue":        runeWordcut, // 粤语
		"default":    runeWordcut, //默认
		"space_lang": viWordcut,   //空格分词
	}

	SymbolMap = make(map[rune]struct{})
	for _, v := range Symbols {
		SymbolMap[v] = struct{}{}
	}
}

// 更新单条字幕的换行信息
func UpdateSubtitleItemSubList(ctx context.Context, task *do.CommentarySubTask, items []*do.CommentarySubtitleItem) error {
	//如果ass信息是有效的，就使用当前的ass信息
	//如果ass信息无效，就生成新的ass信息
	style := task.CustomSubtitleStyle
	if style == nil {
		g.Log().Errorf(ctx, "UpdateSubtitleItemSubList, taskId:%d,style is nil", task.Id)
		return nil
	}
	g.Log().Infof(ctx, "UpdateSubtitleItemSubList, taskId:%d,old task.AssInfo: %s", task.Id, utils.ToJson(task.CustomSubtitleStyle))
	// useTtsWords := false
	//计算新的换行字幕
	g.Log().Infof(ctx, "UpdateSubtitleItemSubList start update items, taskId:%d,style: %s", task.Id, utils.ToJson(style))
	for _, item := range items {
		width := style.Width //旧逻辑
		if style.Rect != nil {
			width = style.Rect.GetWidth() //新逻辑
		}
		rowLimit := style.RowsLimit
		subItems := GetSubItemsForSubtitleItem(ctx, item, width, rowLimit, style.FontName, style.FontSize, style.Bold == -1, style.Italic == -1)
		item.SubItemList = subItems
	}
	return nil
}

// 2. 根据词拆行
func GetLinesForWords(ctx context.Context, itemId int64, frameWidth int32, words []*common.WordInfo, fontName string, fontSize int32, langId string, isBold bool, isItaly bool) []*common.LineInfo {
	computeWordWidth(words, fontName, fontSize, langId, isBold, isItaly)
	lines := make([]*common.LineInfo, 0)
	curLineTotalWidth := int32(0)
	curLineWords := make([]*common.WordInfo, 0)
	for _, word := range words {
		//不超过框宽就继续加
		if (curLineTotalWidth + word.WidthPx) <= frameWidth {
			curLineWords = append(curLineWords, word)
			curLineTotalWidth += word.WidthPx
		} else {
			//否则新开一行
			lines = append(lines, &common.LineInfo{
				Words: curLineWords,
			})
			curLineWords = nil
			curLineTotalWidth = 0
			curLineWords = append(curLineWords, word)
			curLineTotalWidth += word.WidthPx
		}

	}
	//最后一行
	if len(curLineWords) > 0 {
		lines = append(lines, &common.LineInfo{
			Words: curLineWords,
		})
	}
	g.Log().Infof(ctx, "GetLinesForWords, itemId: %v, lineCount: %v, lines: %v", itemId, len(lines), utils.ToJson(lines))
	return lines
}

// 计算词宽度
func computeWordWidth(words []*common.WordInfo, fontName string, fontSize int32, langId string, isBold bool, isItaly bool) {
	for _, word := range words {
		widthMeasure, fontPath, err := font_util.GetFontManager().MeasureTextWidth(langId, fontName, float64(fontSize), word.Word, isBold, isItaly)
		if err != nil {
			//出错走兜底估算
			g.Log().Errorf(context.Background(), "MeasureTextWidth failed, err: %v, langId: %s, fontName: %s, fontSize: %v, word: %s", err, langId, fontName, fontSize, word.Word)
			word.WidthPx = int32(getWordWidth(word.Word, int(fontSize), langId))
		} else {
			_ = fontPath
			//g.Log().Infof(context.Background(), "MeasureTextWidth succeed, langId: %s, fontName: %s, fontSize: %v, word: %s, width: %v, fontPath: %s", langId, fontName, fontSize, word.Word, widthMeasure, fontPath)
			word.WidthPx = int32(widthMeasure)
		}
	}
}

func getWordWidth(word string, fontSize int, language string) int {
	// 假设每个字符的宽度为 10 像素
	characterWidth := getCharacterWidthFactor(language)

	utf8Runes := []rune(word)
	return int(float64(fontSize)*characterWidth) * len(utf8Runes)
}

func getCharacterWidthFactor(language string) float64 {
	switch language {
	case "zh", "ja", "ko", "zh-Hant", "yue": // 中文、日文、韩文、繁体中文
		return 1.0
	case "en", "de", "fr", "ru": // 英语、德语、法语、俄语
		return 0.5
	case "ar", "th": // 阿拉伯语，泰语
		return 0.8
	default:
		return 0.5 // 默认假设英文字符的宽度
	}
}

// 3.根据行和每个字幕框最大容纳行数拆子字幕，
func GetSubItemsForLines(ctx context.Context, rowLimit int32, sentenceStartMs, sentenceEndMs int64, lines []*common.LineInfo) []*common.SubItem {
	subItems := make([]*common.SubItem, 0)
	//1.对lines进行分批，每批不超过maxLineCount
	batchLines := make([][]*common.LineInfo, 0)
	curBatchLines := make([]*common.LineInfo, 0)
	for idx, line := range lines {
		curBatchLines = append(curBatchLines, line)
		if len(curBatchLines) == int(rowLimit) || idx == len(lines)-1 {
			batchLines = append(batchLines, curBatchLines)
			curBatchLines = nil
		}
	}
	//2.对每组构造一个subItem
	//subItem的开始时间=sentenceStartMs+firstLine的开始时间
	//subItem的结束时间=sentenceStartMs+lastLine的结束时间
	//subItem的文本=该组lines的文本用\\N拼接
	for _, batchLine := range batchLines {
		subItem := &common.SubItem{}
		subItem.BeginMs = sentenceStartMs + batchLine[0].GetStartMs()
		subItem.EndMs = sentenceStartMs + batchLine[len(batchLine)-1].GetEndMs()
		for idx, line := range batchLine {
			subItem.SubPostText = subItem.SubPostText + line.GetText()
			if idx != len(batchLine)-1 {
				subItem.SubPostText = subItem.SubPostText + "\\N"
			}
			subItem.Lines = append(subItem.Lines, line)
		}
		subItems = append(subItems, subItem)
	}

	return subItems
}

func GetSubItemsForSubtitleItem(ctx context.Context, item *do.CommentarySubtitleItem, width int32, rowLimit int32, styleFontName string, fontSize int32, isBold bool, isItaly bool) []*common.SubItem {
	g.Log().Infof(ctx, "GetSubItemsForSubtitleItem, taskID: %v,itemId: %v,width:%d,rowLimit: %v, styleFontName: %v, fontSize: %v, isBold: %v, isItaly: %v", item.SubTaskId, item.Id, width, rowLimit, styleFontName, fontSize, isBold, isItaly)
	if len(item.TargetSubtitle) < 1 {
		g.Log().Warningf(ctx, "GetSubItemsForSubtitleItem, targetSubtitle is empty, taskID: %v,itemId: %v", item.SubTaskId, item.Id)
		return nil
	}
	words := GetSubtitleWordsForSubtitleItem(ctx, item)
	if len(words) == 0 {
		g.Log().Warningf(ctx, "GetSubItemsForSubtitleItem, words is empty, taskID: %v,itemId: %v", item.SubTaskId, item.Id)
		return nil
	}
	fontName := GetLocalFontNameByName2(styleFontName, item.TargetLangId)
	lines := GetLinesForWords(ctx, item.Id, width, words, fontName, fontSize, item.TargetLangId, isBold, isItaly)
	if len(lines) == 0 {
		g.Log().Warningf(ctx, "GetSubItemsForSubtitleItem, lines is empty, taskID: %v,itemId: %v", item.SubTaskId, item.Id)
		return nil
	}
	g.Log().Infof(ctx, "GetSubItemsForSubtitleItem, taskID: %v,itemId: %v,width:%d,lineCount: %v, lines: %v", item.SubTaskId, item.Id, width, len(lines), utils.ToJson(lines))
	startMs, err := srt_util.SrtStringTime2Ms(item.SubtitleStartStr)
	if err != nil {
		g.Log().Errorf(ctx, "SrtStringTime2Ms failed, err: %v, subtitleStartStr: %s", err, item.SubtitleStartStr)
		return nil
	}
	endMs, err := srt_util.SrtStringTime2Ms(item.SubtitleEndStr)
	if err != nil {
		g.Log().Errorf(ctx, "SrtStringTime2Ms failed, err: %v, SubtitleEndStr: %s", err, item.SubtitleEndStr)
		return nil
	}
	subItems := GetSubItemsForLines(ctx, rowLimit, startMs, endMs, lines)
	if len(subItems) == 0 {
		g.Log().Warningf(ctx, "GetSubItemsForSubtitleItem, subItems is empty, taskID: %v,itemId: %v", item.SubTaskId, item.Id)
		return nil
	}
	g.Log().Infof(ctx, "GetSubItemsForSubtitleItem,taskID:%d, itemId: %v,subItemCount: %v, subItems: %v", item.SubTaskId, item.Id, len(subItems), utils.ToJson(subItems))
	return subItems
}

func GetLocalFontNameByName2(fontName, lang string) string {
	// 检查是否存在映射
	// 如果没有映射，则返回原始字体名称
	if strings.Contains(fontName, "HarmonyOS") {
		return "HarmonyOS Sans SC"
	} else if strings.Contains(fontName, "Alibaba") {
		if strings.Contains(fontName, "KR") {
			return "Alibaba Sans KR"
		} else if strings.Contains(fontName, "JP") {
			return "Alibaba Sans JP"
		} else if strings.Contains(fontName, "Vi") {
			return "Alibaba Sans Viet"
		} else if strings.Contains(fontName, "Th") {
			return "Alibaba Sans Thai"
		}
		if strings.Contains(lang, "ko") {
			return "Alibaba Sans KR"
		} else if strings.Contains(lang, "ja") {
			return "Alibaba Sans JP"
		} else if strings.Contains(lang, "vi") {
			return "Alibaba Sans Viet"
		} else if strings.Contains(lang, "th") {
			return "Alibaba Sans Thai"
		} else {
			return "Alibaba PuHuiTi 3.0"
		}
	} else if strings.Contains(fontName, "SourceHan") {
		if strings.Contains(fontName, "Serif") {
			return "Source Han Serif CN VF"
		} else if strings.Contains(fontName, "Sans") {
			return "Source Han Sans CN VF"
		} else {
			return "Source Han Sans CN VF"
		}
	} else if strings.Contains(fontName, "Karla Tamil Upright") { //泰米尔语
		return "Karla Tamil Upright"
	} else if strings.Contains(fontName, "NotoNaskhArabic") { //阿拉伯语
		return "Noto Naskh Arabic"
	} else if strings.Contains(fontName, "NotoSans") { //
		return "Noto Sans"
	} else if strings.Contains(fontName, "NotoSansDevanagari") { //印地语
		return "Noto Sans Devanagari"
	}
	return fontName
}

// 1.获取片段分词列表
func GetSubtitleWordsForSubtitleItem(ctx context.Context, subtitleItem *do.CommentarySubtitleItem) (res []*common.WordInfo) {
	if len(subtitleItem.TargetSubtitle) < 1 {
		g.Log().Warningf(ctx, "GetSubtitleWordsForSubtitleItem, targetSubtitle is empty, taskID: %v,itemId: %v", subtitleItem.SubTaskId, subtitleItem.Id)
		return
	}
	//有ttsWords就用ttsWords
	startMs, err := srt_util.SrtStringTime2Ms(subtitleItem.SubtitleStartStr)
	if err != nil {
		g.Log().Errorf(ctx, "GetSubtitleWordsForSubtitleItem SrtStringTime2Ms failed, err: %v, subtitleStartStr: %s", err, subtitleItem.SubtitleStartStr)
		return
	}
	endMs, err := srt_util.SrtStringTime2Ms(subtitleItem.SubtitleEndStr)
	if err != nil {
		g.Log().Errorf(ctx, "GetSubtitleWordsForSubtitleItem SrtStringTime2Ms failed, err: %v, SubtitleEndStr: %s", err, subtitleItem.SubtitleEndStr)
		return
	}
	sentenceDurationMs := endMs - startMs
	sentenceDurationS := float64(sentenceDurationMs) / 1000
	ttsWordsText := ""
	for _, word := range subtitleItem.TTSWords {
		ttsWordsText += word.Word
	}
	//不匹配就用估算
	if ttsWordsText != subtitleItem.TargetSubtitle {
		g.Log().Warningf(ctx, "GetSubtitleWordsForSubtitleItem splitWordsByEstimate, ttsWordsText != TgtSubtitle can not match, taskId:%v,itemId: %v,targetText: %s, ttsWordsText: %s", subtitleItem.SubTaskId, subtitleItem.Id, subtitleItem.TargetSubtitle, ttsWordsText)
		return splitWordsByEstimate(ctx, subtitleItem, sentenceDurationMs)
	}
	if len(subtitleItem.TTSWords) > 0 {
		res = make([]*common.WordInfo, 0, len(subtitleItem.TTSWords))
		speed := subtitleItem.AudioConfig.GetSpeed()
		for _, word := range subtitleItem.TTSWords {
			newWord := &common.WordInfo{
				Start: word.Start,
				End:   word.End,
				Word:  word.Word,
			}
			if newWord.End > sentenceDurationS {
				g.Log().Errorf(ctx, "GetSubtitleWordsForSubtitleItem, tts word end time > sentence end time, taskId:%v,itemId: %v,targetText: %s, word: %v, wordEnd: %v, sentenceEnd: %v", subtitleItem.SubTaskId, subtitleItem.Id, subtitleItem.TargetSubtitle, word.Word, word.End, sentenceDurationS)
				newWord.End = sentenceDurationS
				//return splitWordsByEstimate(subtitleItem, sentenceDurationMs)
			}
			if newWord.Start > sentenceDurationS {
				g.Log().Errorf(ctx, "GetSubtitleWordsForSubtitleItem, tts word start time > sentence end time, taskId:%v,itemId: %v,targetText: %s, word: %v, wordStart: %v, sentenceEnd: %v", subtitleItem.SubTaskId, subtitleItem.Id, subtitleItem.TargetSubtitle, word.Word, word.Start, sentenceDurationS)
				newWord.Start = sentenceDurationS
				//return splitWordsByEstimate(subtitleItem, sentenceDurationMs)
			}
			if newWord.Start >= newWord.End {
				g.Log().Errorf(ctx, "GetSubtitleWordsForSubtitleItem,splitWordsByEstimate tts word start time >= end time, taskId:%v,itemId: %v,targetText: %s, word: %v, wordStart: %v, wordEnd: %v", subtitleItem.SubTaskId, subtitleItem.Id, subtitleItem.TargetSubtitle, word.Word, word.Start, word.End)
				return splitWordsByEstimate(ctx, subtitleItem, sentenceDurationMs)
			}
			//应用语速
			newWord.Start = newWord.Start / float64(speed)
			newWord.End = newWord.End / float64(speed)
			res = append(res, newWord)
		}
		g.Log().Infof(ctx, "GetSubtitleWordsForSubtitleItem, splitWordsByTTSWords, taskId:%v,itemId: %v,targetText: %s, wordCount: %v,res:%v,ttsWords: %v,", subtitleItem.SubTaskId, subtitleItem.Id, subtitleItem.TargetSubtitle, len(subtitleItem.TTSWords), utils.ToJson(res), utils.ToJson(subtitleItem.TTSWords))
		return res
	}

	//没有就对译文文本分词
	words := splitWordsByEstimate(ctx, subtitleItem, sentenceDurationMs)
	g.Log().Infof(ctx, "GetSubtitleWordsForSubtitleItem, splitWordsByEstimate, taskId:%d,itemId: %v,targetText: %s, wordCount: %v,words: %v", subtitleItem.SubTaskId, subtitleItem.Id, subtitleItem.TargetSubtitle, len(words), utils.ToJson(words))
	return words
}

var spaceDelimitedLangs = []string{
	// Indo-European
	"en", "fr", "es", "de", "it", "pt", "ru", "uk", "pl", "cs", "sk",
	"bg", "sr", "hr", "sl", "mk", "ga", "cy", "br", "hi", "ur", "fa", "ps",

	// Semitic
	"ar", "he", "am",

	// Dravidian
	"ta", "te", "kn", "ml",

	// Uralic
	"fi", "et", "hu",

	// Altaic
	"tr", "kk", "uz", "mn",

	// Others
	"sw", "ms", "id", "fil",
}

func doWordcutV2(ctx context.Context, text, language string) []string {
	handler, ok := wordcutHandler[language]
	if !ok {
		if util.SliceContains(spaceDelimitedLangs, language) {
			handler = wordcutHandler["space_lang"]
		} else {
			handler = wordcutHandler["default"]
		}
	}
	if handler == nil {
		g.Log().Errorf(ctx, "doWordcutV2, handler is nil, language: %s", language)
		return []string{text}
	}
	g.Log().Infof(ctx, "doWordcutV2, language: %s, handler: %s", language, handler.GetName())
	return handler.Wordcut(text)
}

func splitWordsByEstimate(ctx context.Context, subtitleItem *do.CommentarySubtitleItem, sentenceDurationMs int64) []*common.WordInfo {
	wordStrList := doWordcutV2(ctx, subtitleItem.TargetSubtitle, subtitleItem.TargetLangId)
	words := slice_util.Map(wordStrList, func(wordStr string, idx int) *common.WordInfo {
		return &common.WordInfo{
			Word: wordStr,
		}
	})

	allocateTimeForWords(ctx, subtitleItem, sentenceDurationMs, words)
	return words
}

// 给每个词分配时间，按照字符数占比
func allocateTimeForWords(ctx context.Context, subtitleItem *do.CommentarySubtitleItem, sentenceDurationMs int64, words []*common.WordInfo) {
	totalCharCount := 0
	for _, word := range words {
		totalCharCount += len([]rune(word.Word))
	}
	g.Log().Infof(ctx, "allocateTimeForWords subTaskId: %v, subtitleItemId: %v, sentenceDurationMs: %v, totalCharCount: %v", subtitleItem.SubTaskId, subtitleItem.Id, sentenceDurationMs, totalCharCount)

	var prevWord *common.WordInfo
	for idx, word := range words {
		charCount := len([]rune(word.Word))
		durationMs := int64(float64(charCount) / float64(totalCharCount) * float64(sentenceDurationMs))
		startMs := int64(0)
		endMs := int64(0)
		//第一个词
		if prevWord == nil {
			startMs = 0
			endMs = durationMs
		} else {
			startMs = prevWord.GetEndMs()
			endMs = startMs + durationMs
		}
		word.Start = float64(startMs) / 1000
		word.End = float64(endMs) / 1000

		if idx == len(words)-1 {
			word.End = float64(sentenceDurationMs) / 1000
			g.Log().Infof(ctx, "allocateTimeForWords subTaskId: %v, subtitleItemId: %v, sentenceDurationMs: %v, totalCharCount: %v, lastWord: %v", subtitleItem.SubTaskId, subtitleItem.Id, sentenceDurationMs, totalCharCount, word.Word)
		}
		prevWord = word
	}
}
