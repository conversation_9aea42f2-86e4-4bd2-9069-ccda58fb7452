package mw

import (
	"business-workflow/internal/common/uerrors"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/pkg/errors"
	"net/http"
	"runtime/debug"
)

// DefaultHandlerResponse is the default implementation of HandlerResponse.
type DefaultHandlerResponse struct {
	Code    int         `json:"code"    dc:"Error code"`
	Message string      `json:"msg" dc:"Error message"`
	Data    interface{} `json:"data"    dc:"Result data for certain request according API definition"`
}

// MiddlewareHandlerResponse is the default middleware handling handler response object and its error.
func MiddlewareHandlerResponse(r *ghttp.Request) {
	defer func() {
		if err := recover(); err != nil {
			g.Log().Errorf(r.Context(), "MiddlewareHandlerResponse panic error:%s: %v", err, string(debug.Stack()))
			writeResponse(r, 1002, "internal error", nil)
		}
	}()
	r.Middleware.Next()

	// There's custom buffer content, it then exits current handler.
	if r.Response.BufferLength() > 0 {
		return
	}

	var (
		msg  string
		err  = r.GetError()
		res  = r.GetHandlerResponse()
		code = gerror.Code(err)
	)
	if err != nil {
		msg = err.Error()
		var ce *uerrors.CommentaryErr
		if errors.As(err, &ce) {
			g.Log().Errorf(r.Context(), "MiddlewareHandlerResponse CommentaryErr:%s", err.Error())
			writeResponse(r, ce.RetCode, ce.Error(), nil)
			return
		}

		if code == gcode.CodeInternalError {
			g.Log().Errorf(r.Context(), "MiddlewareHandlerResponse internal error:%s", err.Error())
			code = gcode.New(http.StatusInternalServerError, msg, nil)
		}
		g.Log().Errorf(r.Context(), "MiddlewareHandlerResponse other error:%s", err.Error())
	} else {
		if r.Response.Status > 0 && r.Response.Status != http.StatusOK {
			msg = http.StatusText(r.Response.Status)
			code = gcode.New(r.Response.Status, msg, nil)

			// It creates error as it can be retrieved by other middlewares.
			err = gerror.NewCode(code, msg)
			g.Log().Errorf(r.Context(), "MiddlewareHandlerResponse Status other error:%s", msg)
			r.SetError(err)
		} else {
			code = gcode.CodeOK
		}
	}

	g.Log().Infof(r.Context(), "MiddlewareHandlerResponse code:%d,msg:%s,res:%v", code, msg, res)
	r.Response.WriteJson(DefaultHandlerResponse{
		Code:    code.Code(),
		Message: msg,
		Data:    res,
	})
}

// writeResponse 统一写响应
func writeResponse(r *ghttp.Request, code int, message string, data interface{}) {
	r.Response.ClearBuffer()
	r.Response.WriteJson(g.Map{
		"code":    code,
		"message": message,
		"data":    data,
	})
}
