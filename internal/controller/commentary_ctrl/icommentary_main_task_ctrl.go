package commentary_ctrl

import (
	"business-workflow/internal/entity/vo"
	"context"
)

type ICommentaryMainTask interface {
	// CreateTask 创建解说主任务
	CreateTask(ctx context.Context, req *vo.CommentaryMainTaskCreateReq) (res *vo.CommentaryMainTaskCreateRes, err error)
	// 查询任务列表
	GetCommentaryTaskHistory(ctx context.Context, req *vo.GetCommentaryTaskHistoryReq) (*vo.GetCommentaryTaskInfoRes, error)
	// BatchGetCommentaryTasksByOrderIds 根据订单ID批量获取解说任务
	BatchGetCommentaryTasksByOrderIds(ctx context.Context, req *vo.BatchGetCommentaryTasksByOrderIdsReq) (*vo.BatchGetCommentaryTasksByOrderIdsRes, error)

	// GetLangList 获取语言列表
	GetLangList(ctx context.Context, req *vo.GetLangListReq) (*vo.GetLangListRes, error)
}
