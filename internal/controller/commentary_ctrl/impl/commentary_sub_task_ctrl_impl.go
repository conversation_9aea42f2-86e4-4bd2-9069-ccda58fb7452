package impl

import (
	"business-workflow/internal/application/svc"
	"business-workflow/internal/common/config"
	"business-workflow/internal/common/uerrors"
	"business-workflow/internal/consts"
	"business-workflow/internal/controller/commentary_ctrl"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/vo"
	"business-workflow/internal/util/obs"
	"context"
	"fmt"
	"strconv"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"

	"github.com/gogf/gf/v2/frame/g"
)

type CommentarySubTaskCtrlImpl struct {
	svc *svc.Service
}

func NewCommentarySubTaskCtrlImpl(svc *svc.Service) commentary_ctrl.ICommentarySubTask {
	return &CommentarySubTaskCtrlImpl{
		svc: svc,
	}
}

func (c *CommentarySubTaskCtrlImpl) RenameSubtitle(ctx context.Context, req *vo.RenameSubtitleReq) (res *vo.RenameSubtitleRes, err error) {
	// 目前 service 层可能还没有对应的重命名方法
	_, err = c.svc.CommentarySubTaskService.RenameSubtitle(ctx, req.Tid, req.NewName)
	if err != nil {
		return nil, uerrors.ErrBusy
	}
	return &vo.RenameSubtitleRes{}, nil
}

func (c *CommentarySubTaskCtrlImpl) SaveSubtitleMode(ctx context.Context, req *vo.SaveSubtitleModeReq) (res *vo.SaveSubtitleModeRes, err error) {
	g.Log().Infof(ctx, "SaveSubtitleMode start, req: %+v", req)

	// 参数验证
	if req.Tid <= 0 {
		return nil, uerrors.ErrParam
	}

	if req.SubtitleMode != 0 && req.SubtitleMode != 1 {
		return nil, uerrors.ErrParam
	}

	// 转换VO到BO
	reqBO := conv.SaveSubtitleModeVOToBO(req)
	if reqBO == nil {
		return nil, uerrors.ErrParam
	}

	// 调用服务层保存字幕模式
	resBO, err := c.svc.CommentarySubTaskService.SaveSubtitleMode(ctx, reqBO)
	if err != nil {
		g.Log().Errorf(ctx, "SaveSubtitleMode failed, err: %v, req: %v", err, req)
		return nil, err
	}

	// 转换BO到VO
	res = conv.SaveSubtitleModeBOToVO(resBO)
	return res, nil
}

func (c *CommentarySubTaskCtrlImpl) SaveGlobalSubtitleStyle(ctx context.Context, req *vo.SaveGlobalSubtitleStyleReq) (res *vo.SaveGlobalSubtitleStyleRes, err error) {
	g.Log().Infof(ctx, "SaveGlobalSubtitleStyle start, req: %+v", req)
	// 转换VO到BO
	reqBO := conv.SaveGlobalSubtitleStyleVOToBO(req)
	// 调用服务层保存全局字幕样式
	resBO, err := c.svc.CommentarySubTaskService.SaveGlobalSubtitleStyle(ctx, reqBO)
	if err != nil {
		g.Log().Errorf(ctx, "SaveGlobalSubtitleStyle failed, err: %v, req: %v", err, req)
		return nil, err
	}

	// 转换BO到VO
	res = conv.SaveGlobalSubtitleStyleBOToVO(resBO)
	return res, nil
}

// UpdateSubTaskBgm 更换子任务背景音
func (c *CommentarySubTaskCtrlImpl) UpdateSubTaskBgm(ctx context.Context, req *vo.CommentarySubTaskUpdateBgmReq) (res *vo.CommentarySubTaskUpdateBgmRes, err error) {
	// 参数验证
	if req.SubTaskId <= 0 {
		return nil, uerrors.ErrParam
	}

	if req.BgmUrl == "" {
		return nil, uerrors.ErrParam
	}

	// 转换 VO 到 BO
	boReq := &bo.UpdateSubTaskBgmReqBO{
		SubTaskId: req.SubTaskId,
		BgmUrl:    req.BgmUrl,
	}

	// 调用服务层更新BGM
	boRes, err := c.svc.CommentarySubTaskService.UpdateSubTaskBgm(ctx, boReq)
	if err != nil {
		g.Log().Errorf(ctx, "UpdateSubTaskBgm err:%v", err)
		return nil, uerrors.ErrBusy
	}

	return &vo.CommentarySubTaskUpdateBgmRes{
		Success: boRes.Success,
	}, nil
}

// UpdateBgmConfig 更新BGM配置
func (c *CommentarySubTaskCtrlImpl) UpdateBgmConfig(ctx context.Context, req *vo.UpdateSubTaskBgmModeReq) (res *vo.UpdateSubTaskBgmModeRes, err error) {
	// 参数验证
	if req.Tid <= 0 {
		return nil, fmt.Errorf("任务ID无效")
	}

	// DisableBGM: 0表示启用BGM，1表示禁用BGM
	// 需要转换为BgmMode: 0表示关闭BGM，1表示开启BGM
	var bgmMode int
	if req.DisableBGM == 0 {
		bgmMode = int(consts.BgmModeOn) // 启用BGM
	} else {
		bgmMode = int(consts.BgmModeOff) // 禁用BGM
	}
	task, err := c.svc.CommentarySubTaskService.GetSubTaskById(ctx, req.Tid)
	if err != nil {
		g.Log().Errorf(ctx, "UpdateBgmConfig failed, err: %v, req: %v", err, req)
		return nil, uerrors.ErrBusy
	}
	// 没bgm 不能开
	if len(task.BgmUrl) == 0 && bgmMode == int(consts.BgmModeOn) {
		g.Log().Warningf(ctx, "UpdateBgmConfig failed, bgmUrl is empty, req: %v", req)
		return nil, uerrors.ErrParam
	}

	// 调用服务层根据主任务ID更新所有子任务的BGM模式
	updateReq := &bo.UpdateSubTaskBgmModeReqBO{
		SubTaskId: req.Tid,
		BgmMode:   bgmMode,
	}
	boRes, err := c.svc.CommentarySubTaskService.UpdateSubTaskBgmMode(ctx, updateReq)
	if err != nil {
		g.Log().Errorf(ctx, "UpdateBgmConfig failed, err: %v, req: %v", err, req)
		return nil, uerrors.ErrBusy
	}

	return &vo.UpdateSubTaskBgmModeRes{
		Success: boRes.Success,
	}, nil
}

// GetSubTaskInfo 获取解说子任务详情
func (c *CommentarySubTaskCtrlImpl) GetSubTaskInfo(ctx context.Context, req *vo.GetSubTaskInfoReq) (res *vo.GetSubTaskInfoRes, err error) {
	g.Log().Infof(ctx, "GetSubTaskInfo start req:%+v", req)
	// 调用服务层获取子任务详情和字幕项列表
	subTaskBO, err := c.svc.CommentarySubTaskService.GetSubTaskWithSubtitles(ctx, req.Tid)
	if err != nil {
		g.Log().Errorf(ctx, "GetSubTaskInfo failed, err: %v, req: %v", err, req)
		return nil, uerrors.ErrBusy
	}
	g.Log().Infof(ctx, "GetSubTaskInfo success, subTaskBO: %v", subTaskBO)
	if subTaskBO == nil {
		g.Log().Errorf(ctx, "子任务不存在, subTaskId: %d", req.Tid)
		return nil, uerrors.ErrTaskNotFound
	}
	g.Log().Infof(ctx, "GetSubTaskInfo success, subTaskBO: %v", subTaskBO)

	// 生产环境才校验
	if config.GetConfig().Env == string(consts.ProdEnv) {
		// 后面加管理员查看能力
		if subTaskBO.TenantId != req.TenantId {
			g.Log().Errorf(ctx, "子任务租户ID不匹配, subTaskId: %d, reqTenantId: %d, subTaskTenantId: %d", subTaskBO.Id, req.TenantId, subTaskBO.TenantId)
			return nil, uerrors.ErrForbiddenForTenant
		}
	}
	g.Log().Infof(ctx, "GetSubTaskInfo CommentarySubTaskBOToVO, start: %v", subTaskBO)
	// 转换 BO 到 VO
	subTaskVO := conv.CommentarySubTaskBOToVO(subTaskBO)
	// 处理下签名(bgm)
	bgmUrl, err := c.preSignBgmUrl(ctx, subTaskVO.BgmUrl)
	if err != nil {
		g.Log().Errorf(ctx, "GetSubTaskInfo preSignUrls failed, err: %v, req: %v", err, req)
		return nil, err
	}
	subTaskVO.BgmUrl = bgmUrl
	// 获取主任务信息
	mainTask, err := c.svc.CommentaryMainTaskService.GetTaskById(ctx, subTaskBO.MainTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "GetSubTaskInfo GetTaskById failed, err: %v, req: %v", err, req)
		return nil, uerrors.ErrBusy
	}

	g.Log().Infof(ctx, "GetSubTaskInfo CommentarySubTaskBOToVO, end: %v", subTaskVO)
	return &vo.GetSubTaskInfoRes{
		CommentarySubTaskVO: subTaskVO,
		BizMode:             mainTask.BizMode,
		TaskType:            mainTask.TaskType,
	}, nil
}

// BatchGetSubTaskInfo 获取解说子任务详情
func (c *CommentarySubTaskCtrlImpl) BatchGetSubTaskInfo(ctx context.Context, req *vo.BatchGetSubTaskStatusReq) (res *vo.BatchGetSubTaskStatusRes, err error) {
	// 参数验证
	if len(req.Tids) <= 0 {
		return nil, fmt.Errorf("子任务ID不能为空")
	}
	if req.TenantId <= 0 {
		return nil, fmt.Errorf("租户ID无效")
	}
	tids := make([]int64, 0, len(req.Tids))
	for _, tid := range req.Tids {
		tidInt64, err := strconv.ParseInt(tid, 10, 64)
		if err != nil || tidInt64 <= 0 {
			return nil, fmt.Errorf("子任务ID无效: %s", tid)
		}
		tids = append(tids, tidInt64)
	}

	// 调用服务层获取子任务详情和字幕项列表
	subTaskBOs, err := c.svc.CommentarySubTaskService.GetSubTaskListByIds(ctx, tids)
	if err != nil {
		return nil, fmt.Errorf("获取子任务详情失败: %w", err)
	}
	res = &vo.BatchGetSubTaskStatusRes{
		SubTaskList: make([]*vo.CommentarySubTaskVO, 0, len(subTaskBOs)),
	}
	for _, subTaskBO := range subTaskBOs {
		// 后面加管理员查看能力
		if config.GetConfig().Env == string(consts.ProdEnv) {
			if subTaskBO.TenantId != req.TenantId {
				g.Log().Errorf(ctx, "子任务租户ID不匹配, subTaskId: %d, reqTenantId: %d, subTaskTenantId: %d", subTaskBO.Id, req.TenantId, subTaskBO.TenantId)
				continue
			}
		}
		// 转换 BO 到 VO
		subTaskVO := conv.CommentarySubTaskBOToVO(subTaskBO)
		res.SubTaskList = append(res.SubTaskList, subTaskVO)
	}

	return res, nil
}

// 签名 bgm
func (c *CommentarySubTaskCtrlImpl) preSignBgmUrl(ctx context.Context, bgmUrl string) (string, error) {
	if bgmUrl == "" {
		return "", nil
	}
	signedUrl, err := obs.PreSignUrl(bgmUrl, 3600*24)
	if err != nil {
		g.Log().Errorf(ctx, "%s预签名失败: %v", bgmUrl, err)
		return "", uerrors.ErrBusy
	}
	// 替换成CDN
	signedUrl = obs.ReplaceUrlToCdn(signedUrl)
	return signedUrl, nil
}

// BatchGetSubTaskDetails 批量获取子任务详情信息
func (c *CommentarySubTaskCtrlImpl) BatchGetSubTaskDetails(ctx context.Context, req *vo.BatchGetSubTaskDetailsReq) (res *vo.BatchGetSubTaskDetailsRes, err error) {

	g.Log().Debugf(ctx, "[BatchGetSubTaskDetails] req: %v", req)
	// 参数验证
	if len(req.Tids) <= 0 {
		return nil, fmt.Errorf("子任务ID不能为空")
	}
	if req.TenantId <= 0 {
		return nil, fmt.Errorf("租户ID无效")
	}

	// 转换字符串ID为int64
	tids := make([]int64, 0, len(req.Tids))
	for _, tid := range req.Tids {
		tidInt64, err := strconv.ParseInt(tid, 10, 64)
		if err != nil || tidInt64 <= 0 {
			return nil, fmt.Errorf("子任务ID无效: %s", tid)
		}
		tids = append(tids, tidInt64)
	}

	// 调用服务层获取子任务详情
	subTaskBOs, err := c.svc.CommentarySubTaskService.GetSubTaskListByIds(ctx, tids)
	if err != nil {
		return nil, fmt.Errorf("获取子任务详情失败: %w", err)
	}

	// 构建响应数据
	res = &vo.BatchGetSubTaskDetailsRes{
		DetailsList: make([]*vo.SubTaskDetailsInfo, 0, len(subTaskBOs)),
	}

	for _, subTaskBO := range subTaskBOs {
		if config.GetConfig().Env == string(consts.ProdEnv) {
			// 权限检查：确保子任务属于当前租户
			if subTaskBO.TenantId != req.TenantId {
				g.Log().Errorf(ctx, "子任务租户ID不匹配, subTaskId: %d, reqTenantId: %d, subTaskTenantId: %d", subTaskBO.Id, req.TenantId, subTaskBO.TenantId)
				continue
			}
		}

		// 构建时长信息
		detailsInfo := &vo.SubTaskDetailsInfo{
			TaskId:                    strconv.FormatInt(subTaskBO.Id, 10),
			VideoDuration:             subTaskBO.VideoDuration,
			MaterialHighlightDuration: subTaskBO.MaterialHighlightDuration,
		}
		res.DetailsList = append(res.DetailsList, detailsInfo)
	}

	return res, nil
}

func (c *CommentarySubTaskCtrlImpl) MergeBatch(ctx context.Context, req *vo.MergeBatchReq) (res *vo.MergeBatchRes, err error) {
	g.Log().Infof(ctx, "MergeBatch req:%+v", req)
	requestId := ctx.Value(trace.ReqId).(string)
	if requestId == "" {
		requestId = uuid.NewString()
	}
	err = c.svc.CommentarySubTaskService.MergeBatch(ctx, requestId, lo.Map(req.Tasks, func(item vo.MergeBatchTaskItem, _ int) int64 {
		return item.Tid
	}))
	if err != nil {
		g.Log().Errorf(ctx, "MergeBatch failed, req: %v, err: %v", req, err)
		return nil, err
	}
	return &vo.MergeBatchRes{}, nil
}

func (c *CommentarySubTaskCtrlImpl) MockInpaint(ctx context.Context, req *vo.MockInpaintReq) (res *vo.MockInpaintRes, err error) {
	g.Log().Infof(ctx, "MockInpaint req:%+v", req)
	VideoUrls := lo.Map(req.Tasks, func(task vo.MockInpaintItem, _ int) string {
		return task.VideoUrl
	})
	taskIds, err := c.svc.CommentarySubTaskService.MockInpaint(ctx, VideoUrls)
	if err != nil {
		g.Log().Errorf(ctx, "MockInpaint failed, req: %v, err: %v", req, err)
		return nil, err
	}
	return &vo.MockInpaintRes{
		TaskIds: taskIds,
	}, nil
}

func (c *CommentarySubTaskCtrlImpl) MockSubtitleMerge(ctx context.Context, req *vo.MockSubtitleMergeReq) (res *vo.MockSubtitleMergeRes, err error) {
	g.Log().Infof(ctx, "MockSubtitleMerge req:%+v", req)
	err = c.svc.CommentarySubTaskService.MockSubtitleMerge(ctx, req.TaskIds)
	if err != nil {
		g.Log().Errorf(ctx, "MockSubtitleMerge failed, req: %v, err: %v", req, err)
		return nil, err
	}
	return &vo.MockSubtitleMergeRes{}, nil
}

func (c *CommentarySubTaskCtrlImpl) GetVideoPostUrl(ctx context.Context, req *vo.GetVideoPostUrlReq) (*vo.GetVideoPostUrlRes, error) {
	g.Log().Infof(ctx, "GetVideoPostUrl req:%+v", req)
	// 参数验证
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}
	if len(req.Tids) <= 0 {
		return nil, fmt.Errorf("子任务ID不能为空")
	}

	tids := make([]int64, 0, len(req.Tids))
	for _, tid := range req.Tids {
		tidInt64, err := strconv.ParseInt(tid, 10, 64)
		if err != nil || tidInt64 <= 0 {
			return nil, fmt.Errorf("子任务ID无效: %s", tid)
		}
		tids = append(tids, tidInt64)
	}
	returnedPostUrls := make([]*vo.VideoPostUrl, 0, len(tids))

	for _, tid := range tids {
		// 调用服务层查询任务信息
		postUrl, err := c.svc.CommentarySubTaskService.GetVideoPostUrl(ctx, tid)
		if err != nil {
			g.Log().Errorf(ctx, "GetVideoPostUrl err:%v", err)
			continue
		}
		returnedPostUrls = append(returnedPostUrls, &vo.VideoPostUrl{
			Tid:     strconv.FormatInt(tid, 10),
			PostUrl: postUrl,
		})
	}

	// 返回结果
	return &vo.GetVideoPostUrlRes{
		VideoPostUrlList: returnedPostUrls,
	}, nil
}

// GetEditOperationLogsByDeductOrderIds 通过扣费订单ID列表批量获取编辑操作流水
func (c *CommentarySubTaskCtrlImpl) GetEditOperationLogsByDeductOrderIds(ctx context.Context, req *vo.GetEditOperationLogsByDeductOrderIdsReq) (res *vo.GetEditOperationLogsByDeductOrderIdsRes, err error) {
	g.Log().Infof(ctx, "GetEditOperationLogsByDeductOrderIds start, req: %+v", req)

	// 参数验证
	if req == nil || len(req.DeductOrderIds) == 0 {
		return &vo.GetEditOperationLogsByDeductOrderIdsRes{
			EditOperationLogs: []*vo.CommentaryEditOperationLogVO{},
		}, nil
	}

	// 调用service层获取编辑操作流水
	editOperationLogs, err := c.svc.CommentarySubTaskService.GetEditOperationLogsByDeductOrderIds(ctx, req.DeductOrderIds)
	if err != nil {
		g.Log().Errorf(ctx, "GetEditOperationLogsByDeductOrderIds failed, req: %+v, err: %v", req, err)
		return nil, uerrors.ErrBusy
	}

	// 转换为VO列表
	results := make([]*vo.CommentaryEditOperationLogVO, 0, len(editOperationLogs))
	for _, editOperationLog := range editOperationLogs {
		editOperationLogVO := &vo.CommentaryEditOperationLogVO{
			Id:            editOperationLog.Id,
			MainTaskId:    editOperationLog.MainTaskId,
			SubTaskId:     editOperationLog.SubTaskId,
			SubTaskName:   editOperationLog.SubTaskName,
			BizMode:       editOperationLog.BizMode,
			DeductOrderId: editOperationLog.DeductOrderId,
			OperationType: editOperationLog.OperationType,
			OperationDesc: editOperationLog.OperationDesc,
		}
		results = append(results, editOperationLogVO)
	}

	g.Log().Infof(ctx, "GetEditOperationLogsByDeductOrderIds success, req: %+v, count: %d", req, len(results))
	return &vo.GetEditOperationLogsByDeductOrderIdsRes{
		EditOperationLogs: results,
	}, nil
}
