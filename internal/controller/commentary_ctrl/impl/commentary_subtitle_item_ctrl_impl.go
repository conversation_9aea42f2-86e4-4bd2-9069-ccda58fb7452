package impl

import (
	"business-workflow/internal/application/svc"
	"business-workflow/internal/consts"
	"business-workflow/internal/controller/commentary_ctrl"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/vo"
	"fmt"
	"strconv"

	"context"

	"github.com/gogf/gf/v2/frame/g"
)

type CommentarySubtitleItemCtrlImpl struct {
	svc *svc.Service
}

func NewCommentarySubtitleItemCtrlImpl(svc *svc.Service) commentary_ctrl.ICommentarySubtitleItem {
	return &CommentarySubtitleItemCtrlImpl{
		svc: svc,
	}
}

// DeleteSubtitleItem 删除片段
func (c *CommentarySubtitleItemCtrlImpl) DeleteSubtitleItem(ctx context.Context, req *vo.DeleteSubtitleItemReq) (res *vo.DeleteSubtitleItemRes, err error) {

	// 调用服务层
	err = c.svc.CommentarySubtitleItemService.DeleteSubtitleItem(ctx, req.Tid, req.SubId)
	if err != nil {
		g.Log().<PERSON><PERSON><PERSON>(ctx, "DeleteSubtitleItem failed, err: %v, req: %v", err, req)
		return nil, err
	}

	return &vo.DeleteSubtitleItemRes{}, nil
}

func (c *CommentarySubtitleItemCtrlImpl) TextTranslate(ctx context.Context, req *vo.TextTranslateReq) (res *vo.TextTranslateRes, err error) {
	// 转换 VO 到 BO

	// 调用服务层
	err = c.svc.CommentarySubtitleItemService.TextTranslate(ctx, req.Tid, req.SubId, req.CustomStyle, req.TranslateBack)
	if err != nil {
		g.Log().Errorf(ctx, "TextTranslate failed, err: %v, req: %v", err, req)
		return nil, err
	}

	return &vo.TextTranslateRes{
		TextTranslateStatus: consts.SubtitleItemReTranslateStatusProcessing,
	}, nil
}

func (c *CommentarySubtitleItemCtrlImpl) GetBatchTextTranslateStatus(ctx context.Context, req *vo.GetBatchTextTranslateStatusReq) (res *vo.GetBatchTextTranslateStatusRes, err error) {

	// 调用服务层
	subIds := make([]int64, 0, len(req.SubIds))
	for _, subId := range req.SubIds {
		subIdInt64, err := strconv.ParseInt(subId, 10, 64)
		if err != nil || subIdInt64 <= 0 {
			return nil, fmt.Errorf("子任务ID无效: %s", subId)
		}
		subIds = append(subIds, subIdInt64)
	}
	boRes, err := c.svc.CommentarySubtitleItemService.GetBatchSubtitleItems(ctx, req.Tid, subIds)
	if err != nil {
		g.Log().Errorf(ctx, "GetBatchTextTranslateStatus failed, err: %v, req: %v", err, req)
		return nil, err
	}
	// 转换 BO 到 VO 返回
	statusList := make([]*vo.ReTranslateStatusInfo, 0)
	for _, item := range boRes {
		g.Log().Infof(ctx, "GetBatchGenerateVoiceStatus boRes: %v", item)
		statusList = append(statusList, &vo.ReTranslateStatusInfo{
			SubId:             item.Id,
			ReTranslateStatus: item.TextTranslateStatus,
			PostText:          item.TargetSubtitle,
			SubtitleItem:      conv.CommentarySubtitleItemBOToVO(item),
		})
	}

	return &vo.GetBatchTextTranslateStatusRes{
		ReTranslateStatusList: statusList,
	}, nil
}

func (c *CommentarySubtitleItemCtrlImpl) GenerateVoice(ctx context.Context, req *vo.GenerateVoiceReq) (res *vo.GenerateVoiceRes, err error) {

	err = c.svc.CommentarySubtitleItemService.GenerateVoice(ctx, req.Tid, req.SubId)
	if err != nil {
		g.Log().Errorf(ctx, "GenerateVoice failed, err: %v, req: %v", err, req)
		return nil, err
	}
	return &vo.GenerateVoiceRes{
		RegenerateStatus: consts.SubtitleItemRegenerateStatusProcessing,
	}, nil
}

func (c *CommentarySubtitleItemCtrlImpl) GetBatchGenerateVoiceStatus(ctx context.Context, req *vo.GetBatchGenerateVoiceStatusReq) (res *vo.GetBatchGenerateVoiceStatusRes, err error) {
	// 转换 VO 到 BO
	subIds := make([]int64, 0, len(req.SubIds))
	for _, subId := range req.SubIds {
		subIdInt64, err := strconv.ParseInt(subId, 10, 64)
		if err != nil || subIdInt64 <= 0 {
			return nil, fmt.Errorf("子任务ID无效: %s", subId)
		}
		subIds = append(subIds, subIdInt64)
	}
	// 调用服务层
	boRes, err := c.svc.CommentarySubtitleItemService.GetBatchSubtitleItems(ctx, req.Tid, subIds)
	if err != nil {
		g.Log().Errorf(ctx, "GetBatchGenerateVoiceStatus failed, err: %v, req: %v", err, req)
		return nil, err
	}

	g.Log().Infof(ctx, "GetBatchGenerateVoiceStatus boRes: %v", boRes)
	statusList := make([]*vo.RegenerateStatusInfo, 0)
	for _, bo := range boRes {
		statusList = append(statusList, &vo.RegenerateStatusInfo{
			SubId:            bo.Id,
			TtsUrl:           bo.TTSUrl,
			RegenerateStatus: bo.GenerateVoiceStatus,
			SubtitleItem:     conv.CommentarySubtitleItemBOToVO(bo),
		})
	}

	return &vo.GetBatchGenerateVoiceStatusRes{
		RegenerateStatusList: statusList,
	}, nil
}

// ModifySubtitle 修改片段
func (c *CommentarySubtitleItemCtrlImpl) ModifySubtitle(ctx context.Context, req *vo.ModifySubtitleReq) (res *vo.ModifySubtitleRes, err error) {
	// 转换 VO 到 BO
	boReq := conv.ModifySubtitleVOToBO(req)

	// 调用服务层
	boRes, err := c.svc.CommentarySubtitleItemService.ModifySubtitle(ctx, boReq)
	if err != nil {
		g.Log().Errorf(ctx, "ModifySubtitle failed, err: %v, req: %v", err, req)
		return nil, err
	}

	// 转换 BO 到 VO 返回
	if boRes != nil && boRes.SubtitleItem != nil {
		subtitleItem := conv.CommentarySubtitleItemBOToVO(boRes.SubtitleItem)
		return &vo.ModifySubtitleRes{
			SubtitleItem: subtitleItem,
		}, nil
	}

	return &vo.ModifySubtitleRes{}, nil
}

// AddSubtitle 添加片段
func (c *CommentarySubtitleItemCtrlImpl) AddSubtitle(ctx context.Context, req *vo.AddSubtitleReq) (res *vo.AddSubtitleRes, err error) {
	// 转换 VO 到 BO
	boReq := conv.AddSubtitleVOToBO(req)

	// 调用服务层
	boRes, err := c.svc.CommentarySubtitleItemService.AddSubtitle(ctx, boReq)
	if err != nil {
		g.Log().Errorf(ctx, "AddSubtitle failed, err: %v, req: %v", err, req)
		return nil, err
	}

	// 转换 BO 到 VO 返回
	if boRes != nil && boRes.SubtitleItem != nil {
		subtitleItem := conv.CommentarySubtitleItemBOToVO(boRes.SubtitleItem)
		return &vo.AddSubtitleRes{
			SubtitleItem: subtitleItem,
		}, nil
	}

	return &vo.AddSubtitleRes{}, nil
}

func (c *CommentarySubtitleItemCtrlImpl) ModifySubtitleSpeed(ctx context.Context, req *vo.ModifySubtitleSpeedReq) (res *vo.ModifySubtitleSpeedRes, err error) {
	// 转换 VO 到 BO
	boReq := conv.ModifySubtitleSpeedVOToBO(req)

	// 调用服务层
	boRes, err := c.svc.CommentarySubtitleItemService.ModifySubtitleSpeed(ctx, boReq)
	if err != nil {
		g.Log().Errorf(ctx, "ModifySubtitleSpeed failed, err: %v, req: %v", err, req)
		return nil, err
	}

	// 转换 BO 到 VO
	items := make([]*vo.SubtitleItem, len(boRes.SubtitleItems))
	for i, item := range boRes.SubtitleItems {
		items[i] = conv.CommentarySubtitleItemBOToVO(item)
	}

	return &vo.ModifySubtitleSpeedRes{
		Items: items,
	}, nil
}

func (c *CommentarySubtitleItemCtrlImpl) ModifySubtitleVolume(ctx context.Context, req *vo.ModifySubtitleVolumeReq) (res *vo.ModifySubtitleVolumeRes, err error) {
	// 转换 VO 到 BO
	boReq := conv.ModifySubtitleVolumeVOToBO(req)

	// 调用服务层
	_, err = c.svc.CommentarySubtitleItemService.ModifySubtitleVolume(ctx, boReq)
	if err != nil {
		g.Log().Errorf(ctx, "ModifySubtitleVolume failed, err: %v, req: %v", err, req)
		return nil, err
	}

	return &vo.ModifySubtitleVolumeRes{}, nil
}

// SplitSubtitle 拆分片段
func (c *CommentarySubtitleItemCtrlImpl) SplitSubtitle(ctx context.Context, req *vo.SplitSubtitleReq) (res *vo.SplitSubtitleRes, err error) {
	// 转换 VO 到 BO
	boReq := conv.SplitSubtitleVOToBO(req)

	// 调用服务层
	boRes, err := c.svc.CommentarySubtitleItemService.SplitSubtitle(ctx, boReq)
	if err != nil {
		g.Log().Errorf(ctx, "SplitSubtitle failed, err: %v, req: %v", err, req)
		return nil, err
	}

	// 转换 BO 到 VO
	if boRes != nil && len(boRes.SubtitleItems) > 0 {
		items := make([]*vo.SubtitleItem, len(boRes.SubtitleItems))
		for i, item := range boRes.SubtitleItems {
			items[i] = conv.CommentarySubtitleItemBOToVO(item)
		}
		return &vo.SplitSubtitleRes{
			SubtitleItemList: items,
		}, nil
	}

	return &vo.SplitSubtitleRes{}, nil
}

func (c *CommentarySubtitleItemCtrlImpl) MergeSubtitle(ctx context.Context, req *vo.MergeSubtitleReq) (res *vo.MergeSubtitleRes, err error) {
	// 转换 VO 到 BO
	boReq := conv.MergeSubtitleVOToBO(req)

	// 调用服务层
	boRes, err := c.svc.CommentarySubtitleItemService.MergeSubtitle(ctx, boReq)
	if err != nil {
		g.Log().Errorf(ctx, "MergeSubtitle failed, err: %v, req: %v", err, req)
		return nil, err
	}

	// 转换 BO 到 VO
	if boRes != nil && boRes.SubtitleItem != nil {
		item := conv.CommentarySubtitleItemBOToVO(boRes.SubtitleItem)
		return &vo.MergeSubtitleRes{
			SubtitleItem: item,
		}, nil
	}

	return &vo.MergeSubtitleRes{}, nil
}
