package commentary_ctrl

import (
	"business-workflow/internal/entity/vo"
	"context"
)

type ICommentarySubTask interface {
	// RenameSubtitle 重命名任务
	RenameSubtitle(ctx context.Context, req *vo.RenameSubtitleReq) (res *vo.RenameSubtitleRes, err error)

	// SaveSubtitleMode 保存字幕开关状态
	SaveSubtitleMode(ctx context.Context, req *vo.SaveSubtitleModeReq) (res *vo.SaveSubtitleModeRes, err error)

	// SaveGlobalSubtitleStyle 保存全局字幕样式
	SaveGlobalSubtitleStyle(ctx context.Context, req *vo.SaveGlobalSubtitleStyleReq) (res *vo.SaveGlobalSubtitleStyleRes, err error)

	// UpdateSubTaskBgm 更新子任务背景音
	UpdateSubTaskBgm(ctx context.Context, req *vo.CommentarySubTaskUpdateBgmReq) (res *vo.CommentarySubTaskUpdateBgmRes, err error)

	// UpdateBgmConfig 更新BGM配置
	UpdateBgmConfig(ctx context.Context, req *vo.UpdateSubTaskBgmModeReq) (res *vo.UpdateSubTaskBgmModeRes, err error)

	// GetSubTaskInfo 获取解说子任务详情
	GetSubTaskInfo(ctx context.Context, req *vo.GetSubTaskInfoReq) (res *vo.GetSubTaskInfoRes, err error)
	// BatchGetSubTaskInfo 批量获取解说子任务详情
	BatchGetSubTaskInfo(ctx context.Context, req *vo.BatchGetSubTaskStatusReq) (res *vo.BatchGetSubTaskStatusRes, err error)

	// BatchGetSubTaskDetails 批量获取子任务详情信息
	BatchGetSubTaskDetails(ctx context.Context, req *vo.BatchGetSubTaskDetailsReq) (res *vo.BatchGetSubTaskDetailsRes, err error)

	// MergeBatch 批量合成视频
	MergeBatch(ctx context.Context, req *vo.MergeBatchReq) (res *vo.MergeBatchRes, err error)

	// 测试接口
	MockInpaint(ctx context.Context, req *vo.MockInpaintReq) (res *vo.MockInpaintRes, err error)
	// GetVideoPostUrl 获取视频上传地址
	GetVideoPostUrl(ctx context.Context, req *vo.GetVideoPostUrlReq) (*vo.GetVideoPostUrlRes, error)

	// MockSubtitleMerge 模拟字幕合并
	MockSubtitleMerge(ctx context.Context, req *vo.MockSubtitleMergeReq) (res *vo.MockSubtitleMergeRes, err error)

	// GetEditOperationLogsByDeductOrderIds 通过扣费订单ID列表批量获取编辑操作流水
	GetEditOperationLogsByDeductOrderIds(ctx context.Context, req *vo.GetEditOperationLogsByDeductOrderIdsReq) (res *vo.GetEditOperationLogsByDeductOrderIdsRes, err error)
}
