package workflows

import (
	"business-workflow/internal/activities/commentary/audit"
	"business-workflow/internal/activities/commentary/commentary"
	"business-workflow/internal/activities/commentary/concat"
	"business-workflow/internal/activities/commentary/deduction"
	"business-workflow/internal/activities/commentary/erase"
	"business-workflow/internal/activities/commentary/finalize"
	"business-workflow/internal/activities/commentary/merge"
	"business-workflow/internal/activities/commentary/ocr"
	posterase "business-workflow/internal/activities/commentary/post_erase"
	preerase "business-workflow/internal/activities/commentary/pre_erase"
	"business-workflow/internal/activities/commentary/refund"
	"business-workflow/internal/activities/commentary/subtitle_audit"
	text_translate "business-workflow/internal/activities/commentary/text_translate"
	"business-workflow/internal/activities/commentary/transcoding"
	"business-workflow/internal/activities/commentary/trimclip"
	"business-workflow/internal/activities/commentary/tts"
	"business-workflow/internal/common/config"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"context"

	"time"

	"github.com/gogf/gf/v2/frame/g"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

// buildActivityOptions 构建Activity选项的辅助函数
func buildActivityOptions(startToCloseMinutes, scheduleToCloseMinutes int, maxAttempts int32) workflow.ActivityOptions {
	temporalCfg := config.GetConfig().Temporal
	return workflow.ActivityOptions{
		StartToCloseTimeout:    time.Duration(startToCloseMinutes) * time.Minute,
		ScheduleToCloseTimeout: time.Duration(scheduleToCloseMinutes) * time.Minute,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Duration(temporalCfg.InitialInterval) * time.Minute,
			BackoffCoefficient: temporalCfg.BackoffCoefficient,
			MaximumInterval:    time.Duration(temporalCfg.MaximumInterval) * time.Minute,
			MaximumAttempts:    maxAttempts,
		},
	}
}

// getDefaultActivityOptions 获取默认Activity选项
func getDefaultActivityOptions() workflow.ActivityOptions {
	temporalCfg := config.GetConfig().Temporal
	return buildActivityOptions(
		temporalCfg.StartToCloseTimeout,
		temporalCfg.ScheduleToCloseTimeout,
		int32(temporalCfg.MaximumAttempts),
	)
}

func CommentaryWorkflow(ctx workflow.Context, task *bo.CommentaryMainTaskBO, subTasks []*bo.CommentarySubTaskBO) (*bo.CommentaryMainTaskBO, error) {
	temporalCfg := config.GetConfig().Temporal
	g.Log().Debugf(context.TODO(), "[CommentaryWorkflow] temporalCfg: %+v", temporalCfg)

	// 设置默认的工作流选项
	ctx = workflow.WithActivityOptions(ctx, getDefaultActivityOptions())

	defer func() {
		// 首次完成后处理
		err := workflow.ExecuteActivity(ctx, finalize.FinalizeProcess, task).Get(ctx, task)
		if err != nil {
			return
		}
	}()
	defer func() {
		// 首次完成后处理
		err := workflow.ExecuteActivity(ctx, refund.RefundProcess, task).Get(ctx, task)
		if err != nil {
			return
		}
	}()

	// 扣款
	err := workflow.ExecuteActivity(ctx, deduction.DeductionProcess, task).Get(ctx, task)
	if err != nil {
		return nil, err
	}

	// 审核活动需要更长的超时时间和更少的重试次数
	// 送审
	auditOptions := getDefaultActivityOptions()
	//设置审核超时时间
	auditOptions.ScheduleToCloseTimeout = 20 * time.Minute // 20分钟超时
	auditOptions.RetryPolicy.MaximumAttempts = 2           // 最多重试2次
	ctxAudit := workflow.WithActivityOptions(ctx, auditOptions)
	err = workflow.ExecuteActivity(ctxAudit, audit.AuditProcess, task).Get(ctx, task)
	if err != nil {
		return nil, err
	}

	// 转码解说视频
	err = workflow.ExecuteActivity(ctx, transcoding.TransCodingProcess, task).Get(ctx, task)
	if err != nil {
		return nil, err
	}
	//
	wg := workflow.NewWaitGroup(ctx)
	for _, commentarySubTask := range subTasks {
		commentarySubTask := commentarySubTask
		wg.Add(1)
		workflow.Go(ctx, func(ctx workflow.Context) {
			defer wg.Done()
			// 提交起飞解说，返回多份台词 -> 写字幕表
			errSub := workflow.ExecuteActivity(ctx, commentary.CommentaryProcess, task, commentarySubTask).Get(ctx, task)
			if errSub != nil {
				return
			}
			// 文本翻译
			errSub = workflow.ExecuteActivity(ctx, text_translate.TextTranslateProcess, task, commentarySubTask).Get(ctx, task)
			if errSub != nil {
				return
			}
			// 配音 不用调速
			errSub = workflow.ExecuteActivity(ctx, tts.TTSProcess, task, commentarySubTask).Get(ctx, task)
			if errSub != nil {
				return
			}

			childWg := workflow.NewWaitGroup(ctx)
			childWg.Add(2) // 子任务有2个并发节点
			cancelCtx, cancel := workflow.WithCancel(ctx)
			var cerr error
			workflow.Go(cancelCtx, func(ctx workflow.Context) {
				defer childWg.Done()
				// 子任务并行流程2.1 裁剪片段/合成
				cerr = workflow.ExecuteActivity(ctx, trimclip.TrimClipProcess, task, commentarySubTask).Get(ctx, task)
				if cerr != nil {
					cancel()
					return
				}
				// 子任务并行流程2.2 ocr
				cerr = workflow.ExecuteActivity(ctx, ocr.OCRProcess, task, commentarySubTask).Get(ctx, task)
				if cerr != nil {
					cancel()
					return
				}
				// 子任务并行流程2.3 擦除前处理
				cerr = workflow.ExecuteActivity(ctx, preerase.PreEraseProcess, task, commentarySubTask).Get(ctx, task)
				if cerr != nil {
					cancel()
					return
				}
				// 子任务并行流程2.4 擦除片段
				cerr = workflow.ExecuteActivity(ctx, erase.EraseProcess, task, commentarySubTask).Get(ctx, task)
				if cerr != nil {
					cancel()
					return
				}
				// 子任务并行流程2.5 擦除后处理
				cerr = workflow.ExecuteActivity(ctx, posterase.PostEraseProcess, task, commentarySubTask).Get(ctx, task)
				if cerr != nil {
					cancel()
					return
				}

			})
			workflow.Go(cancelCtx, func(ctx workflow.Context) {
				defer childWg.Done()
				// 子任务并行流程3 字幕审核
				cerr = workflow.ExecuteActivity(ctx, subtitle_audit.SubtitleAuditProcess, task, commentarySubTask).Get(ctx, task)
				if cerr != nil {
					cancel()
					return
				}
			})

			childWg.Wait(ctx)
			if cerr != nil {
				return
			}
			g.Log().Debugf(context.TODO(), "[CommentaryWorkflow] 2222 temporalCfg: %+v", temporalCfg)
			errSub = workflow.ExecuteActivity(ctx, merge.MergeBeforeProcess, task, commentarySubTask).Get(ctx, task)
			if errSub != nil {
				return
			}
			g.Log().Debugf(context.TODO(), "[CommentaryWorkflow] 3333 temporalCfg: %+v", temporalCfg)
			// 子任务：合成 先用mock  修改合成状态 -> 合成 -> 成功/失败
			errSub = workflow.ExecuteActivity(ctx, merge.MergeProcess, task, commentarySubTask, consts.WorkFlowFromCommentaryFirst).Get(ctx, task)
			if errSub != nil {
				return
			}

			// 最后 拼接视频 , 生产封面 , 写入时长 等
			errSub = workflow.ExecuteActivity(ctx, concat.ConcatProcess, task, commentarySubTask, consts.WorkFlowFromCommentaryFirst).Get(ctx, task)
			if errSub != nil {
				return
			}
			// 子任务：赠送免费积分和免费合成次数
			errSub = workflow.ExecuteActivity(ctx, merge.MergeAfterProcess, task, commentarySubTask).Get(ctx, task)
			if errSub != nil {
				return
			}

		})
	}
	wg.Wait(ctx)

	return task, nil
}
