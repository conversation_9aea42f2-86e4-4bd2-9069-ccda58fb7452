package workflows

import (
	"business-workflow/internal/activities/commentary/erase"
	"business-workflow/internal/activities/commentary/merge"
	"business-workflow/internal/activities/commentary/ocr"
	posterase "business-workflow/internal/activities/commentary/post_erase"
	preerase "business-workflow/internal/activities/commentary/pre_erase"
	"business-workflow/internal/common/config"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"go.temporal.io/sdk/workflow"
)

func getInpaintActivityOptions() workflow.ActivityOptions {
	temporalCfg := config.GetConfig().Temporal
	return buildActivityOptions(
		150,
		150,
		int32(temporalCfg.MaximumAttempts),
	)
}

func MockInpaint(ctx workflow.Context, subTasks []*bo.CommentarySubTaskBO) error {
	temporalCfg := config.GetConfig().Temporal
	g.Log().Infof(context.TODO(), "[MockInpaint] temporalCfg: %+v", temporalCfg)

	// 设置默认的工作流选项
	//ctx = workflow.WithActivityOptions(ctx, getDefaultActivityOptions())

	cancelCtx, cancel := workflow.WithCancel(ctx)

	task := &bo.CommentaryMainTaskBO{}
	task.Id = 12345
	task.EraseMode = consts.EraseModeSubtitle
	task.TenantId = 273297330659131394
	task.ApiKey = "ak_b6811f9f2c87a74dc23924a2c1b23b4b5e99"
	task.AppId = 2
	task.BizMode = consts.BizModeOnlyCommentary

	// curl -X POST http://localhost:28000/v1/commentary/mock_inpaint \
	//  -H "Content-Type: application/json" \
	//  -d '{"appid":"2","tenant_id":"273297330659131394","api_key":"mock","tasks":[{"video_url":"https://cdn-allvoice-down-cn-testing.funnycp.com/openapi-test/public/openapi_upload_file/273115235068411923/20250827/1bde298c-f453-4295-bb4a-4bf37e688cf5_dccvg97kqtf0haef6r.mp4"}]}'

	//用于擦除测试的接口
	wg := workflow.NewWaitGroup(cancelCtx)
	for _, commentarySubTask := range subTasks {
		//commentarySubTask := commentarySubTask
		wg.Add(1)
		g.Log().Infof(context.TODO(), "[MockInpaint] 子任务并行流程, mainTaskId: %v, subTaskId: %v",
			commentarySubTask.MainTaskId, commentarySubTask.Id)
		workflow.Go(cancelCtx, func(ctx workflow.Context) {
			defer wg.Done()
			ctx = workflow.WithActivityOptions(ctx, getInpaintActivityOptions())
			// 子任务并行流程2.2 ocr
			cerr := workflow.ExecuteActivity(ctx, ocr.OCRProcess, task, commentarySubTask).Get(ctx, task)
			if cerr != nil {
				g.Log().Errorf(context.TODO(), "[MockInpaint] 子任务并行流程, ocr 失败, mainTaskId: %v, subTaskId: %v, err: %v",
					commentarySubTask.MainTaskId, commentarySubTask.Id, cerr)
				cancel()
				return
			}
			// 子任务并行流程2.3 擦除前处理
			cerr = workflow.ExecuteActivity(ctx, preerase.PreEraseProcess, task, commentarySubTask).Get(ctx, task)
			if cerr != nil {
				g.Log().Errorf(context.TODO(), "[MockInpaint] 子任务并行流程, 擦除前处理 失败, mainTaskId: %v, subTaskId: %v, err: %v",
					commentarySubTask.MainTaskId, commentarySubTask.Id, cerr)
				cancel()
				return
			}
			// 子任务并行流程2.4 擦除片段
			cerr = workflow.ExecuteActivity(ctx, erase.EraseProcess, task, commentarySubTask).Get(ctx, task)
			if cerr != nil {
				g.Log().Errorf(context.TODO(), "[MockInpaint] 子任务并行流程, 擦除片段 失败, mainTaskId: %v, subTaskId: %v, err: %v",
					commentarySubTask.MainTaskId, commentarySubTask.Id, cerr)
				cancel()
				return
			}
			// 子任务并行流程2.5 擦除后处理
			cerr = workflow.ExecuteActivity(ctx, posterase.PostEraseProcess, task, commentarySubTask).Get(ctx, task)
			if cerr != nil {
				g.Log().Errorf(context.TODO(), "[MockInpaint] 子任务并行流程, 擦除后处理 失败, mainTaskId: %v, subTaskId: %v, err: %v",
					commentarySubTask.MainTaskId, commentarySubTask.Id, cerr)
				cancel()
				return
			}
		})
	}
	wg.Wait(cancelCtx)
	g.Log().Infof(context.TODO(), "[MockInpaint] 所有子任务完成")
	return nil
}

func MockSubtitleMerge(ctx workflow.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) error {

	ctx = workflow.WithActivityOptions(ctx, getInpaintActivityOptions())
	err := workflow.ExecuteActivity(ctx, merge.MergeProcess, task, subTask).Get(ctx, task)
	if err != nil {
		g.Log().Errorf(context.TODO(), "[MockSubtitleMerge] 字幕合成失败, mainTaskId: %v, subTaskId: %v, err: %v",
			subTask.MainTaskId, subTask.Id, err)
		return err
	}
	return nil
}
