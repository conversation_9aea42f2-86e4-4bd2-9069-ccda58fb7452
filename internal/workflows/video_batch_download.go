package workflows

import (
	"business-workflow/internal/activities/commentary/audit"
	"business-workflow/internal/activities/commentary/batch_download"
	"business-workflow/internal/activities/commentary/concat"
	"business-workflow/internal/activities/commentary/deduction"
	"business-workflow/internal/activities/commentary/merge"
	"business-workflow/internal/activities/commentary/refund"
	"business-workflow/internal/activities/commentary/subtitle_audit"
	subtitlegenerate "business-workflow/internal/activities/commentary/subtitle_generate"
	"business-workflow/internal/common/config"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"go.temporal.io/sdk/workflow"
)

func VideoBatchMergeWorkflow(ctx workflow.Context, task *bo.CommentaryMainTaskBO, subTasks []*bo.CommentarySubTaskBO) ([]*bo.CommentarySubTaskBO, error) {
	temporalCfg := config.GetConfig().Temporal
	g.Log().Debugf(context.TODO(), "[VideoBatchMergeWorkflow] temporalCfg: %+v", temporalCfg)

	// 设置默认的工作流选项
	ctx = workflow.WithActivityOptions(ctx, getDefaultActivityOptions())
	// 1. 扣费 defer 退款
	// 2. bgm 审核 文本翻译->文本审核
	// 3. 字幕生成
	// 4. 合成前处理
	// 5. 拼接 修改状态和版本

	defer func() {
		// 视频合成退款处理
		err := workflow.ExecuteActivity(ctx, refund.VideoBatchSynthesisRefund, task, subTasks).Get(ctx, nil)
		if err != nil {
			g.Log().Errorf(context.TODO(), "[VideoBatchMergeWorkflow] 视频合成退款失败: %v", err)
		}
	}()

	g.Log().Debugf(context.TODO(), "[VideoBatchMergeWorkflow] 子任务数量: %d", len(subTasks))
	// 视频合成扣费处理
	err := workflow.ExecuteActivity(ctx, deduction.VideoBatchSynthesisDeduction, task, subTasks).Get(ctx, nil)
	if err != nil {
		g.Log().Errorf(context.TODO(), "[VideoBatchMergeWorkflow] 视频合成扣费失败: %v", err)
		return nil, err
	}
	g.Log().Debugf(context.TODO(), "[VideoBatchMergeWorkflow] 子任务数量: %d", len(subTasks))

	wg := workflow.NewWaitGroup(ctx)
	for _, commentarySubTask := range subTasks {
		g.Log().Debugf(context.TODO(), "[VideoBatchMergeWorkflow] 子任务: %+v", commentarySubTask)
		commentarySubTask := commentarySubTask
		wg.Add(1)
		workflow.Go(ctx, func(ctx workflow.Context) {
			defer wg.Done()
			var err error

			defer func() {
				if err != nil {
					err2 := workflow.ExecuteActivity(ctx, batch_download.BatchDownloadErrorProcess, commentarySubTask).Get(ctx, task)
					if err2 != nil {
						return
					}
				}
			}()

			err = workflow.ExecuteActivity(ctx, batch_download.BatchDownloadStartProcess, commentarySubTask).Get(ctx, task)
			if err != nil {
				return
			}
			// BGM 审核
			err = workflow.ExecuteActivity(ctx, audit.BgmAuditProcess, commentarySubTask).Get(ctx, task)
			if err != nil {
				return
			}

			// 翻译, 判断目标语言 是否中文, 不是中文要翻译成中文
			err = workflow.ExecuteActivity(ctx, subtitle_audit.SubtitleAuditForMerge, commentarySubTask).Get(ctx, task)
			if err != nil {
				return
			}

			//  直接用传入的对象 当快照
			// 生成后 上传 ass 文件
			err = workflow.ExecuteActivity(ctx, subtitlegenerate.SubtitleGenerateProcess, task, commentarySubTask).Get(ctx, task)
			if err != nil {
				return
			}

			// 合成前置 处理 bgm处理
			err = workflow.ExecuteActivity(ctx, merge.MergeBeforeProcess, task, commentarySubTask).Get(ctx, task)
			if err != nil {
				return
			}
			//子任务：合成 先用mock  修改合成状态 -> 合成 -> 成功/失败
			err = workflow.ExecuteActivity(ctx, merge.MergeProcess, task, commentarySubTask, consts.WorkFlowFromCommentaryMergeDownload).Get(ctx, task)
			if err != nil {
				return
			}

			// 最后 拼接视频 , 生成封面   + 改合成状态   1表示下载
			err = workflow.ExecuteActivity(ctx, concat.ConcatProcess, task, commentarySubTask, consts.WorkFlowFromCommentaryMergeDownload).Get(ctx, task)
			if err != nil {
				return
			}

			//  设置版本号
			err = workflow.ExecuteActivity(ctx, batch_download.BatchDownloadEndProcess, commentarySubTask).Get(ctx, task)
			if err != nil {
				return
			}

		})
	}
	wg.Wait(ctx)

	return subTasks, nil
}
