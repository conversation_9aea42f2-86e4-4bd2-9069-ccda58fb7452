package activities

import (
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/do"
	impl2 "business-workflow/internal/repo/commentary_repo/impl"
	"business-workflow/internal/repo/engine_task_repo/impl"
	"context"
	"errors"
	"fmt"
	"runtime/debug"
	"time"

	"go.temporal.io/sdk/temporal"

	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	"go.temporal.io/sdk/activity"
)

type IActivity interface {
	IsSkip(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) bool                                          // 判断是否直接跳转到下个activity
	WaitCondition(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) error                                  //在正式进入activity逻辑前的条件校验
	HandleSubmit(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) ([]int64, error)                        // 提交任务到engine
	HandleResult(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, results []*do.BusinessEngineTask) error // 处理任务结果
	HandleFailed(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, aerr *temporal.ApplicationError) error  // 处理任务失败，主要是更新状态
}

// LangDetectActivity 提供活动基类，封装通用逻辑
type Processor struct {
}

func (a *Processor) HandleQuery(ctx context.Context, taskIds []int64) ([]*do.BusinessEngineTask, error) {
	engineTaskRepo := impl.NewEngineTaskRepoImpl()
	if len(taskIds) == 0 {
		g.Log().Errorf(ctx, "HandleQuery no taskIds")
		return nil, nil
	}
	// 在循环里面每隔5s轮询WorkflowEngineTask表，直到状态为成功或失败
	for {
		engineTasks, err := engineTaskRepo.GetWorkflowEngineTasks(ctx, taskIds)
		if err != nil {
			g.Log().Errorf(ctx, "GetWorkflowEngineTask error:%v", err)
			// 数据库报错不需要重试
			return nil, temporal.NewNonRetryableApplicationError(
				"GetWorkflowEngineTasks error",
				"db error",
				err)
		}
		if len(engineTasks) == 0 {
			g.Log().Errorf(ctx, "GetWorkflowEngineTask no task")
			return nil, temporal.NewNonRetryableApplicationError(
				"GetWorkflowEngineTasks no task",
				"db error",
				nil)
		}
		allComplete := true
		for _, engineTask := range engineTasks {
			// 如果超过最大超时时间，也不用继续重试
			if engineTask.HandleTimeout != nil && engineTask.HandleTimeout.Before(time.Now()) {
				return nil, temporal.NewNonRetryableApplicationError(
					"GetWorkflowEngineTask timeout",
					"timeout",
					nil)
			}

			// 继续等待
			if engineTask.EngineStatus <= consts.EngineWorkflowStatusProcessing {
				allComplete = false
				break
			}
		}
		if allComplete {
			return engineTasks, nil
		}
		time.Sleep(5 * time.Second)

	}
}
func (a *Processor) Process(ctx context.Context, act IActivity, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) (retTask *bo.CommentaryMainTaskBO, err error) {
	ctx = context.WithValue(ctx, trace.ReqId, GetRequestId(ctx))
	// 在Process依次调用WaitCondition，HandleSubmit，HandleResult方法
	defer func() {
		retTask = task
		if r := recover(); r != nil {
			g.Log().Errorf(ctx, "activity process panic, err:%v, stack:%s", r, string(debug.Stack()))
			err = temporal.NewNonRetryableApplicationError(
				"activity process panic",
				"panic",
				errors.New(string(debug.Stack())),
			)
		}
		if err != nil {
			g.Log().Errorf(ctx, "activity process failed, err:%v", err)
			var appErr *temporal.ApplicationError
			if errors.As(err, &appErr) {
				if appErr.NonRetryable() {
					// 如果是不可重试错误，处理失败逻辑
					g.Log().Errorf(ctx, "TextTranslateProcess non-retryable error: %v", appErr)
					act.HandleFailed(ctx, task, subTask, appErr)

				}
			}
		}
	}()
	if act.IsSkip(ctx, task, subTask) {
		// 直接跳到下个节点
		return
	}
	err = act.WaitCondition(ctx, task, subTask)
	if err != nil {
		return
	}

	var engineTaskIDs []int64
	if activity.HasHeartbeatDetails(ctx) { // 显式检查
		activity.GetHeartbeatDetails(ctx, &engineTaskIDs)
	}
	if len(engineTaskIDs) == 0 {
		engineTaskIDs, err = act.HandleSubmit(ctx, task, subTask)
		if err != nil {
			return
		}
		activity.RecordHeartbeat(ctx, engineTaskIDs)
	}
	if len(engineTaskIDs) > 0 {
		var results []*do.BusinessEngineTask
		results, err = a.HandleQuery(ctx, engineTaskIDs)
		if err != nil {
			return
		}

		err = act.HandleResult(ctx, task, subTask, results)
		if err != nil {
			return
		}
	}

	return
}
func GetRequestId(ctx context.Context) string {
	info := activity.GetInfo(ctx)
	activityID := info.ActivityID
	runID := info.WorkflowExecution.RunID
	return fmt.Sprintf("%s:%s", runID, activityID)
}

func UpdateSubTaskProcessing(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, fmt.Sprintf("UpdateSubTaskProcessing %s", task.RequestID))
	g.Log().Infof(ctx, "UpdateSubTaskProcessing task:%+v, sub_task:%+v", task, subTask)
	// 修改任务状态 执行中
	repoImpl := impl2.NewCommentarySubTaskRepoImpl()
	err := repoImpl.UpdateSubTaskStatus(ctx, subTask.Id, int(consts.SubTaskStatusProcessing), "")
	if err != nil {
		err := UpdateSubTaskStatusFailed(ctx, "UpdateSubTaskProcessing", subTask)
		if err != nil {
			return nil, temporal.NewNonRetryableApplicationError(
				"UpdateSubTaskStatus err",
				"db error",
				err)
		}
		g.Log().Errorf(ctx, "UpdateSubTaskProcessing err:%+v", err)
		return nil, temporal.NewNonRetryableApplicationError(
			"UpdateSubTaskStatus err",
			"db error",
			err)
	}
	return task, nil
}

func UpdateSubTaskStatusFailed(ctx context.Context, from string, subTask *bo.CommentarySubTaskBO) error {
	g.Log().Errorf(ctx, "UpdateSubTaskStatusFailed from:%v, subTask:%+v", from, subTask)
	err := impl2.NewCommentarySubTaskRepoImpl().UpdateSubTaskStatus(ctx, subTask.Id, int(consts.SubTaskStatusFailed), "sub task failed")
	if err != nil {
		g.Log().Errorf(ctx, "UpdateSubTaskStatus err:%v", err)
		return temporal.NewNonRetryableApplicationError(
			"UpdateSubTaskStatus err",
			"db error",
			err)
	}
	return nil
}

func UpdateSubTaskMergeStatusFailed(ctx context.Context, from string, subTask *bo.CommentarySubTaskBO) error {
	g.Log().Errorf(ctx, "UpdateSubTaskMergeStatusFailed from:%v, subTask:%+v", from, subTask)
	err := impl2.NewCommentarySubTaskRepoImpl().UpdateSubTaskFields(ctx, subTask.Id, map[string]interface{}{
		"merge_status": int(consts.MergeStatusFailed),
	})
	if err != nil {
		g.Log().Errorf(ctx, "UpdateSubTaskMergeStatusFailed err:%v", err)
		return temporal.NewNonRetryableApplicationError(
			"UpdateSubTaskMergeStatusFailed err",
			"db error",
			err)
	}
	return nil
}

func IsNonRetryableError(err error) bool {
	var appErr *temporal.ApplicationError
	if errors.As(err, &appErr) {
		if appErr.NonRetryable() {
			return true
		}
	}
	return false
}
