package audit

import (
	"business-workflow/internal/activities"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/processor/audit_processor"
	"business-workflow/internal/repo/commentary_repo"
	commentary_repo_impl "business-workflow/internal/repo/commentary_repo/impl"
	"business-workflow/internal/util/ffmpeg"
	"business-workflow/internal/util/obs"
	"context"
	"time"

	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/utils/audit_util"
	"go.temporal.io/sdk/temporal"
	"golang.org/x/sync/errgroup"

	"github.com/gogf/gf/v2/frame/g"
)

func AuditProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, activities.GetRequestId(ctx))
	g.Log().Info(ctx, "AuditProcess task:%v, sub_task:%+v", task, subTask)

	if !audit_processor.EnableAudit() {
		return task, nil
	}

	// 获取待审核素材
	sources, err := commentary_repo_impl.NewCommentarySourceDetailRepoImpl().
		GetSourceDetailsByMainTaskIdAndAuditStatus(ctx, task.Id, int(consts.AuditStatusPending))
	if err != nil {
		g.Log().Error(ctx, "GetSourceDetailsByMainTaskIdAndAuditStatus err:%v", err)
		return nil, err
	}
	if len(sources) == 0 {
		return task, nil
	}

	// 并行审核
	if err := parallelAudit(ctx, task, sources); err != nil {
		return handleAuditFailure(ctx, task, err)
	}

	// 审核通过，继续往下走就行
	return task, nil
}

func parallelAudit(ctx context.Context, task *bo.CommentaryMainTaskBO, sources []*do.CommentarySourceDetail) error {
	// 限制并发度为3
	semaphore := make(chan struct{}, 3)
	group, ctx := errgroup.WithContext(ctx)
	//return temporal.NewNonRetryableApplicationError("UpdateTaskStatus err", "db error", nil)

	for _, detail := range sources {
		detail := detail
		group.Go(func() error {
			// 在获取信号量时检查上下文
			select {
			case semaphore <- struct{}{}:
				defer func() { <-semaphore }()
				return auditSingleSource(ctx, task, detail)
			case <-ctx.Done():
				return ctx.Err()
			}
		})
	}

	return group.Wait()
}

func auditSingleSource(ctx context.Context, task *bo.CommentaryMainTaskBO, detail *do.CommentarySourceDetail) error {
	dataFormat := audit_util.AuditDataFormatVideo
	bizScene := audit_processor.AuditSceneKeyCommentaryVideoUpload
	if detail.SourceFileType == consts.SourceFileTypeAudio {
		dataFormat = audit_util.AuditDataFormatAudio
		bizScene = audit_processor.AuditSceneKeyCommentaryAudioUpload
	}
	url, err := obs.PreSignUrl(detail.SourceUrl, 3600*24)
	if err != nil {
		g.Log().Error(ctx, "PreSignUrl err:%v", err)
		return err
	}
	info := &audit_util.AuditInfo{
		AppId:      task.AppId,
		UserId:     task.TenantId,
		BizScene:   bizScene,
		DataFormat: dataFormat,
		Quantity:   detail.Duration,
	}
	if dataFormat == audit_util.AuditDataFormatVideo {
		info.VideoUrl = url
	} else {
		info.AudioUrl = url
	}
	return audit_processor.GetAuditProcessor().ProcessAudit(ctx, info)
}

func handleAuditFailure(ctx context.Context, task *bo.CommentaryMainTaskBO, err error) (*bo.CommentaryMainTaskBO, error) {
	errMsg := "素材审核异常"
	auditStatus := consts.AuditStatusError

	if audit_util.IsAuditRefused(err) {
		errMsg = "素材审核不通过"
		auditStatus = consts.AuditStatusRejected
	}

	g.Log().Error(ctx, "audit err:%v", err)

	// 更新任务状态 失败
	updateMainTaskStatus(ctx, task.Id, consts.MainTaskStatusFailed, errMsg)
	if err := updateSubTaskStatusFailed(ctx, task.Id, consts.SubTaskStatusFailed, auditStatus, errMsg); err != nil {
		return task, temporal.NewNonRetryableApplicationError("UpdateTaskStatus err", "db error", nil)
	}
	if auditStatus == consts.AuditStatusRejected {
		return task, temporal.NewNonRetryableApplicationError("AuditRefused", "audit refused", nil)
	}
	// 审核异常重试
	return task, temporal.NewApplicationError("AuditError", "audit error", nil)
}

func updateMainTaskStatus(ctx context.Context, taskId int64, status consts.CommentaryMainTaskStatus, errMsg string) {
	repo := commentary_repo_impl.NewCommentaryMainTaskRepoImpl()

	extraParams := &commentary_repo.UpdateTaskStatusExtraParams{}
	mainTaskDO, err := repo.GetTaskById(ctx, taskId)
	if err != nil {
		g.Log().Error(ctx, "GetTaskById(%d) status:%d err:%v", taskId, status, err)
	} else if mainTaskDO != nil && mainTaskDO.FstDoneTime.Time.IsZero() {
		now := time.Now()
		extraParams.FstDoneStatus = &status
		extraParams.FstDoneTime = &now
	}
	if err := repo.UpdateTaskStatus(ctx, taskId, status, errMsg, extraParams); err != nil {
		g.Log().Error(ctx, "UpdateTaskStatus err:%v", err)
	}
}

func updateSubTaskStatusFailed(ctx context.Context, taskId int64, status consts.CommentarySubTaskStatus, auditStatus consts.AuditStatus, errMsg string) error {
	_, err := commentary_repo_impl.NewCommentarySubTaskRepoImpl().UpdateSubTaskStatusAndAuditStatusByMainTaskId(ctx, taskId, status, auditStatus, errMsg)
	if err != nil {
		g.Log().Error(ctx, "UpdateSubTaskStatusAndAuditStatusByMainTaskId err:%v", err)
	}
	return err
}

func BgmAuditProcess(ctx context.Context, subTask *bo.CommentarySubTaskBO) (*bo.CommentarySubTaskBO, error) {
	if !audit_processor.EnableAudit() {
		return subTask, nil
	}
	if subTask.BgmUrl == "" {
		return subTask, nil
	}
	dataFormat := audit_util.AuditDataFormatAudio
	bizScene := audit_processor.AuditSceneKeyCommentaryAudioUpload
	url, err := obs.PreSignUrl(subTask.BgmUrl, 3600*24)
	defer func() {
		if err != nil {
			err2 := activities.UpdateSubTaskMergeStatusFailed(ctx, "BgmAuditProcess", subTask)
			if err2 != nil {
				g.Log().Error(ctx, "UpdateSubTaskMergeStatusFailed err:%v", err2)
			}
		}

	}()
	if err != nil {
		g.Log().Error(ctx, "PreSignUrl err:%v", err)
		return subTask, temporal.NewNonRetryableApplicationError("PreSignUrl err", "oss error", nil)
	}
	duration, err := ffmpeg.ProbeDuration(url)
	if err != nil {
		g.Log().Error(ctx, "ProbeDuration err:%v", err)
		return nil, temporal.NewNonRetryableApplicationError("ProbeDuration err", "ffmpeg error", nil)
	}

	info := &audit_util.AuditInfo{
		AppId:      subTask.AppId,
		UserId:     subTask.TenantId,
		BizScene:   bizScene,
		DataFormat: dataFormat,
		AudioUrl:   url,
		Quantity:   duration,
	}
	err = audit_processor.GetAuditProcessor().ProcessAudit(ctx, info)
	if err != nil {
		if audit_util.IsAuditRefused(err) {
			// 设置子任务审核失败
			err := commentary_repo_impl.NewCommentarySubTaskRepoImpl().UpdateSubTaskFields(ctx, subTask.Id, map[string]interface{}{
				"audit_status": int(consts.AuditStatusRejected),
				"audit_reason": err.Error(),
				"merge_status": int(consts.MergeStatusFailed),
			})
			if err != nil {
				g.Log().Error(ctx, "UpdateSubTaskFields err:%v", err)
				return nil, temporal.NewNonRetryableApplicationError("UpdateSubTaskFields err", "db error", err)
			}
			return nil, temporal.NewNonRetryableApplicationError("BgmAuditRefused", "audit refused", nil)
		}
		// 设置审核异常 可重试
		g.Log().Error(ctx, "BgmAuditProcess err:%v", err)
		return nil, temporal.NewApplicationError("BgmAuditError", "audit error", err)
	}
	return subTask, nil

}
