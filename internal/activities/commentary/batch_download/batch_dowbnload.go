package batch_download

import (
	"business-workflow/internal/activities"
	"business-workflow/internal/common/db"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/repo/commentary_repo/impl"
	"business-workflow/internal/util/id_generator"
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"go.temporal.io/sdk/temporal"
	"gorm.io/gorm"
)

func BatchDownloadStartProcess(ctx context.Context, subTask *bo.CommentarySubTaskBO) (*bo.CommentarySubTaskBO, error) {
	g.Log().Infof(ctx, "BatchDownloadStartProcess task:%v", subTask)
	shotRepoImpl := impl.NewCommentarySubtitleItemSnapShotRepoImpl()
	subtitleItemRepoImpl := impl.NewCommentarySubtitleItemRepoImpl()
	err := db.GetDB().Transaction(func(tx *gorm.DB) error {

		// 写字幕表快照
		subtitleItems, err := subtitleItemRepoImpl.GetSubtitleItemsBySubTaskId(ctx, subTask.Id)
		if err != nil {
			return err
		}
		snapshotItems := conv.CommentarySubItemListToSnapshotDO(subtitleItems)
		//  id 重新生成
		for _, item := range snapshotItems {
			newId, err := id_generator.GenerateId()
			if err != nil {
				return err
			}
			item.Id = newId
		}
		err = shotRepoImpl.DeleteSubtitleSnapshotItemsBySubTaskId(ctx, subTask.Id, tx)
		if err != nil {
			return err
		}
		err = shotRepoImpl.CreateSubtitleSnapshotItems(ctx, snapshotItems, tx)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		g.Log().Errorf(ctx, "BatchDownloadStartProcess err:%v", err)
		err := activities.UpdateSubTaskMergeStatusFailed(ctx, "BatchDownloadStartProcess", subTask)
		if err != nil {
			return nil, temporal.NewNonRetryableApplicationError(
				"UpdateSubTaskMergeStatusFailed err",
				"db error",
				err)
		}
		return nil, temporal.NewNonRetryableApplicationError(
			"BatchDownloadStartProcess err",
			"db error",
			err)
	}

	return subTask, nil
}

func BatchDownloadEndProcess(ctx context.Context, subTask *bo.CommentarySubTaskBO) (*bo.CommentarySubTaskBO, error) {
	//  设置版本号
	g.Log().Infof(ctx, "BatchDownloadEndProcess task:%v", subTask)

	err := impl.NewCommentarySubTaskRepoImpl().MarkMergeSuccess(ctx, subTask.Id, subTask.LatestVersion)
	if err != nil {
		return nil, temporal.NewNonRetryableApplicationError(
			"MarkMergeSuccess err",
			"db error",
			err)
	}
	return subTask, nil
}

func BatchDownloadErrorProcess(ctx context.Context, subTask *bo.CommentarySubTaskBO) (*bo.CommentarySubTaskBO, error) {
	//  设置版本号
	g.Log().Infof(ctx, "BatchDownloadEndProcess task:%v", subTask)

	err := impl.NewCommentarySubTaskRepoImpl().UpdateSubTaskFields(ctx, subTask.Id, map[string]interface{}{
		"merge_status": int(consts.MergeStatusFailed),
	})
	subTask.MergeStatus = consts.MergeStatusFailed
	if err != nil {
		return nil, temporal.NewNonRetryableApplicationError(
			"MarkMergeSuccess err",
			"db error",
			err)
	}
	return subTask, nil
}
