package posterase

import (
	"business-workflow/internal/activities"
	"business-workflow/internal/common"
	"business-workflow/internal/common/config"
	"business-workflow/internal/common/erase"
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/processor/audio_processor"
	"business-workflow/internal/processor/processor_util"
	"business-workflow/internal/repo/commentary_repo"
	commentaryRepoImpl "business-workflow/internal/repo/commentary_repo/impl"
	enginetaskrepo "business-workflow/internal/repo/engine_task_repo"
	"business-workflow/internal/repo/engine_task_repo/impl"
	"business-workflow/internal/repo/inpainting"
	impaintImpl "business-workflow/internal/repo/inpainting/impl"
	hobs "business-workflow/internal/util/obs"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/utils/acl"
	omniProto "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"

	"github.com/gogf/gf/v2/frame/g"
	"go.temporal.io/sdk/temporal"
)

// 确保在init时自动注册

type PostEraseActivity struct {
	ocrDb       inpainting.IInpaintingOcrRepo
	subTaskRepo commentary_repo.ICommentarySubTaskRepo
	engineRepo  enginetaskrepo.IEngineTaskRepo
}

var (
	noRetryErr = temporal.NewNonRetryableApplicationError(
		"defaultPostEraseNoRetryError",
		"InvalidOrderError",
		nil)

	retryErr = temporal.NewApplicationError( // 需要重试的错误
		"defaultPostEraseRetryError",
		"InvalidOrderError",
		nil)
)

func (a *PostEraseActivity) IsSkip(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) bool {
	ocrTask, err := a.ocrDb.GetOcrTaskBySubId(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "PreEraseActivity skip, subTaskId: %v", subTask.Id)
		return true
	}
	if ocrTask.Status == consts.InpaintingStatusPostprocrssComplete {
		return true
	}
	if task.EraseMode == consts.EraseModeOff {
		//
		subTaskDb, err := a.subTaskRepo.GetSubTaskById(ctx, subTask.Id)
		if err != nil {
			g.Log().Errorf(ctx, "PreEraseActivity WaitCondition, GetSubTaskById failed, subTaskId: %v, err: %s",
				subTask.Id, err.Error())
			return false
		}
		subTaskDb.NoSubtitleMaterialHighlightUrl = subTaskDb.MaterialHighlightUrl
		err = a.subTaskRepo.UpdateSubTask(ctx, subTaskDb, nil)
		if err != nil {
			g.Log().Errorf(ctx, "PreEraseActivity WaitCondition, UpdateSubTask failed, subTaskId: %v, err: %s",
				subTask.Id, err.Error())
			return false
		}
	}
	return false
}

func (a *PostEraseActivity) WaitCondition(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) error {
	// 实现等待逻辑...
	return nil
}

func (a *PostEraseActivity) HandleSubmit(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) ([]int64, error) {
	// 实现提交逻辑...
	ocrTask, err := impaintImpl.NewInpaintingOcrTaskImpl().GetOcrTaskBySubId(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "PostEraseActivity HandleSubmit, GetOcrTaskBySubId failed, err: %v", err)
		return nil, err
	}
	if ocrTask.Status == consts.InpaintingStatusDelete {
		return nil, noRetryErr
	}
	engineTask, err := a.engineRepo.GetWorkflowEngineTaskByBidAndType(ctx, ocrTask.Bid, int(omniProto.TaskType_VIDEO_TRANSLATION_POST_MERGE))
	if err != nil {
		g.Log().Warningf(ctx, "PostEraseActivity HandleSubmit, GetWorkflowEngineTaskByBid failed, err: %v", err)
		return nil, noRetryErr
	}
	if engineTask.Id > 0 {
		g.Log().Warningf(ctx, "PostEraseActivity HandleSubmit, engineTask is not nil: %v", engineTask)
	}
	if ocrTask.Status != consts.InpaintingStatusEraseComplete {
		if engineTask.Id > 0 && engineTask.Bid == ocrTask.Bid {
			g.Log().Warningf(ctx, "PostEraseActivity HandleSubmit, engineTask is not nil: %v", engineTask)
			return []int64{engineTask.EngineTaskId}, nil
		} else {
			g.Log().Errorf(ctx, "PostEraseActivity HandleSubmit, ocrTask.Stutus is not InpaintingStatusPreprocessComplete: %v", ocrTask.Status)
			return nil, noRetryErr
		}
	}
	enginetask, err := a.submitToEngine(ctx, task, subTask, ocrTask)
	if err != nil {
		g.Log().Errorf(ctx, "PostEraseActivity HandleSubmit, submitToEngine failed, err: %v", err)
		return nil, err
	}
	return []int64{enginetask.EngineTaskId}, nil
}

func (a *PostEraseActivity) submitToEngine(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, ocrTask *do.WorkflowOcrTask) (*do.BusinessEngineTask, error) {
	mergeReq := &erase.MergeV2SubmitReq{}
	mergeReq.Id = ocrTask.Bid
	mergeReq.TargetObject = processor_util.GetObjectName(subTask.TenantId, subTask.Id, "post_output.mp4", "post_merge")
	// reqBase := &omniProto.TaskBase{
	// 	ExtraMap: map[string]string{
	// 		// "task_cost": fmt.Sprintf("%d", int(subTask.VideoDuration)),
	// 		consts.BaseTaskCost:                 fmt.Sprintf("%v", subTask.VideoDuration),
	// 		consts.BaseTaskCommentrayMainTaskID: strconv.FormatInt(task.Id, 10),
	// 		consts.BaseTaskCommentraySubTaskID:  strconv.FormatInt(subTask.Id, 10),
	// 		consts.BaseTaskCommentrayBizMode:    strconv.FormatInt(int64(task.BizMode), 10),
	// 		consts.BaseTaskCommentrayTaskType:   strconv.FormatInt(int64(task.TaskType), 10),
	// 	},
	// }
	reqBase := conv.CommentaryMainTaskBOToBaseTask(task, subTask, int64(omniProto.TaskSource_INTERNAL_SOURCE),
		map[string]string{
			consts.BaseTaskCost: fmt.Sprintf("%v", subTask.VideoDuration),
		})

	taskInfo := &omniProto.TaskInfo{}
	taskInfo.Name = fmt.Sprintf("commentary_postprocess_%v", ocrTask.Bid)
	taskInfo.TaskType = omniProto.TaskType_VIDEO_TRANSLATION_POST_MERGE
	taskInfo.Quantity = 1
	taskInfo.RefererId = task.Id

	data, err := json.Marshal(mergeReq)
	if err != nil {
		g.Log().Error(ctx, "json.Marshal failed, err: %v", err)
		return nil, err
	}
	taskInfo.CommonInputContent = string(data)

	createReq := &omniProto.CreateTaskReq{
		TaskBase: reqBase,
		TaskList: []*omniProto.TaskInfo{taskInfo},
	}

	createRes, err := omni_engine.GetOmniEngineClient().CreateTask(ctx, createReq)
	if err != nil {
		g.Log().Error(ctx, "CreateTask PreEraseActivity failed, err: %v", err)
		return nil, retryErr
	}
	if len(createRes.TaskIds) != 1 {
		g.Log().Error(ctx, "CreateTask PreEraseActivity failed, len is not 1: %v", len(createRes.TaskIds))
		return nil, retryErr
	}
	ocrTask.PostprocessSubmitAt = time.Now()
	ocrTask.Status = consts.InpaintingStatusPostprocrssSubmitted
	_, cdnUrl, err := hobs.GetOsClient().GetObjectUrl(mergeReq.TargetObject)
	if err != nil {
		g.Log().Error(ctx, "GetObjectUrl failed, err: %v", err)
		return nil, err
	}
	ocrTask.NotitleVideoUrl = cdnUrl
	handoutTime := time.Now().Add(time.Duration(config.GetConfig().Temporal.ScheduleToCloseTimeout) / 2 * time.Minute)

	enginetask := &do.BusinessEngineTask{
		EngineTaskId:  createRes.TaskIds[0],
		MainTaskId:    subTask.MainTaskId,
		Bid:           ocrTask.Id,
		TenantID:      ocrTask.TenantID,
		TaskType:      int(taskInfo.TaskType),
		EngineStatus:  consts.EngineWorkflowStatusInit,
		HandleTimeout: &handoutTime,
	}

	err = impaintImpl.NewInpaintingOcrTaskImpl().CreateEngineTasksAndUpdateOcrTask(ctx, []*do.BusinessEngineTask{enginetask}, ocrTask)
	if err != nil {
		return nil, err
	}
	return enginetask, nil
}

func (a *PostEraseActivity) HandleResult(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, result []*do.BusinessEngineTask) error {
	// 处理成功...
	g.Log().Infof(ctx, "PostEraseActivity HandleResult:%+v", subTask)
	if len(result) == 0 {
		g.Log().Errorf(ctx, "PostEraseActivity HandleResult, result is empty")
		return noRetryErr
	}
	engineTask := result[0]
	omniTask := &omniProto.Task{}
	err := json.Unmarshal([]byte(engineTask.TaskInfo), omniTask)
	if err != nil {
		g.Log().Errorf(ctx, "PostEraseActivity HandleResult, unmarshal post_erase engine task info failed, err: %v, subTaskId: %v, taskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return noRetryErr
	}
	if engineTask.EngineStatus == consts.EngineWorkflowStatusSuccess {
		err := a.handleSuccess(ctx, task, subTask, engineTask, omniTask)
		if err != nil {
			g.Log().Errorf(ctx, "PostEraseActivity handleSuccess failed, err: %v", err)
			return noRetryErr
		}
		return nil
	} else if engineTask.EngineStatus == consts.EngineWorkflowStatusFailed ||
		engineTask.EngineStatus == consts.EngineWorkflowStatusTimeout {
		err := a.handleFail(ctx, task, subTask, engineTask, omniTask)
		if err != nil {
			g.Log().Errorf(ctx, "PostEraseActivity handleFail failed, err: %v", err)
		}
		return err
	} else {
		g.Log().Warningf(ctx, "PostEraseActivity HandleResult, status maybe wrong, engineStatus: %v", engineTask.EngineStatus)
		return retryErr
	}
}

func (a *PostEraseActivity) handleSuccess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO,
	engineTask *do.BusinessEngineTask, omniTask *omniProto.Task) error {
	g.Log().Infof(ctx, "PostEraseActivity handleSuccess, mainTaskId: %v, subTaskId: %v, engineTaskId: %v",
		task.Id, subTask.Id, engineTask.EngineTaskId)
	// 解析结果
	extractRes := &erase.MergeQueryV2Res{}
	err := json.Unmarshal([]byte(omniTask.CommonOutputContent), extractRes)
	if err != nil {
		g.Log().Errorf(ctx, "PostEraseActivity handleSuccess, unmarshal engine task info failed, err: %v, subTaskId: %v, taskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	ocrTask, err := a.ocrDb.GetOcrTaskBySubId(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "PostEraseActivity handleSuccess, get ocr task by sub id failed, err: %v, subTaskId: %v, engineTaskId: %v, subId: %v",
			err, subTask.Id, engineTask.EngineTaskId, subTask.Id)
		return err
	}
	// 合成完成
	ocrTask.Status = consts.InpaintingStatusPostprocrssComplete
	ocrTask.PostprocessEndAt = time.Now()
	// 同时更新链接到子任务里
	subTaskD, err := a.subTaskRepo.GetSubTaskById(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "PostEraseActivity handleSuccess, get sub task by id failed, err: %v, subTaskId: %v, engineTaskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	targetObject, err := processor_util.GetObjectNameByHttpsUrl(ocrTask.NotitleVideoUrl)
	if err != nil {
		g.Log().Errorf(ctx, "PostEraseActivity handleSuccess, GetObjectNameByHttpsUrl failed, subTaskId: %v, engineTaskId: %v, NotitleVideoUrl: %v",
			subTask.Id, engineTask.EngineTaskId, ocrTask.NotitleVideoUrl)
		return err
	}
	_, err = hobs.GetOsClient().SetPublicRead(targetObject)
	if err != nil {
		g.Log().Errorf(ctx, "PostEraseActivity handleSuccess, SetPublicRead failed, subTaskId: %v, engineTaskId: %v, targetObject: %v",
			subTask.Id, engineTask.EngineTaskId, targetObject)
		return err
	}
	if task.TaskType == consts.TaskTypeHighlight {
		// 高光剪辑判断
		err = a.handleMergeAudio(ctx, task, subTaskD, ocrTask)
		if err != nil {
			g.Log().Errorf(ctx, "PostEraseActivity handleMergeAudio failed, err: %v", err)
			return err
		}
	} else {
		subTaskD.NoSubtitleMaterialHighlightUrl = ocrTask.NotitleVideoUrl
	}
	return a.ocrDb.UpdateOcrTaskAndSaveNotitleVideoUrl(ctx, ocrTask, ocrTask.NotitleVideoUrl)
}

func (a *PostEraseActivity) handleMergeAudio(ctx context.Context, task *bo.CommentaryMainTaskBO,
	subTask *do.CommentarySubTask, ocrTask *do.WorkflowOcrTask) error {
	g.Log().Infof(ctx, "PostEraseActivity handleMergeAudio, mainTaskId: %v, subTaskId: %v, notitleUrl: %v",
		subTask.MainTaskId, subTask.Id, ocrTask.NotitleVideoUrl)
	// 合成原视频中的音频到擦除后的视频里
	localPath := audio_processor.GetLocalVideoPathById(ctx, subTask.Id)
	err := common.EnsureOutputDirectory(localPath)
	if err != nil {
		g.Log().Errorf(ctx, "PostEraseActivity handleMergeAudio, EnsureOutputDirectory failed, path: %v err: %v",
			localPath, err)
		return err
	}
	// 1. 下载原视频
	originLocalFile := fmt.Sprintf("%s/post_erase_origin_%s", localPath, filepath.Base(subTask.MaterialHighlightUrl))
	_, err = processor_util.DownloadRemoteObjectToLocalUseOs(subTask.MaterialHighlightUrl, originLocalFile)
	if err != nil {
		g.Log().Errorf(ctx, "PostEraseActivity handleMergeAudio, download MaterialHighlightUrl failed, err: %v", err)
		return err
	}
	defer func() {
		errD := os.Remove(originLocalFile)
		if errD != nil {
			g.Log().Errorf(ctx, "PostEraseActivity handleMergeAudio, os.Remove(originLocalFile) failed, err: %v",
				errD)
		}
	}()
	notitleLocalFile := fmt.Sprintf("%s/post_erase_notitle_%s", localPath, filepath.Base(subTask.MaterialHighlightUrl))
	_, err = processor_util.DownloadRemoteObjectToLocalUseOs(ocrTask.NotitleVideoUrl, notitleLocalFile)
	if err != nil {
		g.Log().Errorf(ctx, "PostEraseActivity handleMergeAudio, download NotitleVideoUrl failed, err: %v", err)
		return err
	}
	defer func() {
		errD := os.Remove(notitleLocalFile)
		if errD != nil {
			g.Log().Errorf(ctx, "PostEraseActivity handleMergeAudio, os.Remove(notitleLocalFile) failed, err: %v",
				errD)
		}
	}()
	mergeFile := fmt.Sprintf("%s/post_erase_notitle_mergeaudio_%s", localPath, filepath.Base(subTask.MaterialHighlightUrl))
	err = processor_util.MergeAudioFromVideo(originLocalFile, notitleLocalFile, mergeFile)
	if err != nil {
		g.Log().Errorf(ctx, "PostEraseActivity handleMergeAudio, MergeAudioFromVideo failed, err: %v", err)
		return err
	}
	defer func() {
		errD := os.Remove(mergeFile)
		if errD != nil {
			g.Log().Errorf(ctx, "PostEraseActivity handleMergeAudio, os.Remove(mergeFile) failed, err: %v",
				errD)
		}
	}()
	objectName := processor_util.GetObjectName(subTask.TenantId, subTask.Id, mergeFile)
	remoteUrl, err := hobs.GetOsClient().UploadFile(mergeFile, objectName, acl.AclTypePublicRead)
	if err != nil {
		g.Log().Errorf(ctx, "PostEraseActivity handleMergeAudio, UploadFile failed, err: %v", err)
		return err
	}
	subTask.NoSubtitleMaterialHighlightUrl = remoteUrl
	return nil
}

func (a *PostEraseActivity) handleFail(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO,
	engineTask *do.BusinessEngineTask, omniTask *omniProto.Task) error {
	g.Log().Errorf(ctx, "PostEraseActivity handleFail, subTaskId: %v, engineTaskId: %v",
		subTask.Id, engineTask.EngineTaskId)
	extractRes := &erase.MergeQueryV2Res{}
	err := json.Unmarshal([]byte(omniTask.CommonOutputContent), extractRes)
	if err != nil {
		g.Log().Errorf(ctx, "PostEraseActivity handleFail, unmarshal engine task info failed, err: %v, subTaskId: %v, taskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return noRetryErr
	}
	if extractRes.Code == erase.AiCodeTaskTimeout {
		err = a.retry(ctx, subTask)
		if err != nil {
			g.Log().Errorf(ctx, "PostEraseActivity handleFail retry failed, subTaskId: %v, err: %s", subTask.Id, err.Error())
			return noRetryErr
		}
		/// 重试合成
		g.Log().Errorf(ctx, "PostEraseActivity handleFail, task timeout, subTaskId: %v, engineTaskId: %v",
			subTask.Id, engineTask.EngineTaskId)
		return retryErr
	}
	return processor_util.HandleSubTaskFail(ctx, subTask, int(extractRes.Code), extractRes.Msg)
}

func (a *PostEraseActivity) retry(ctx context.Context, task *bo.CommentarySubTaskBO) error {
	subTaskDo, err := a.ocrDb.GetOcrTaskBySubId(ctx, task.Id)
	if err != nil {
		return err
	}
	subTaskDo.Status = consts.InpaintingStatusEraseComplete
	return a.ocrDb.UpdateOcrTask(ctx, subTaskDo)
}

func (a *PostEraseActivity) HandleFailed(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, aerr *temporal.ApplicationError) error {
	g.Log().Errorf(ctx, "PostEraseActivity HandleFailed, err: %v, subTask: %+v", aerr, subTask)
	_ = processor_util.HandleSubTaskFail(ctx, subTask, -1, "post erase failed")
	return nil
}

func PostEraseProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, activities.GetRequestId(ctx))
	g.Log().Infof(ctx, "PostEraseProcess: task %+v, sub_task:%+v", task, subTask)
	a := &activities.Processor{}
	return a.Process(ctx, &PostEraseActivity{
		ocrDb:       impaintImpl.NewInpaintingOcrTaskImpl(),
		subTaskRepo: commentaryRepoImpl.NewCommentarySubTaskRepoImpl(),
		engineRepo:  impl.NewEngineTaskRepoImpl(),
	}, task, subTask)
}
