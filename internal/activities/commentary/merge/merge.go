package merge

import (
	"business-workflow/internal/activities"
	"business-workflow/internal/common"
	"business-workflow/internal/common/config"
	"business-workflow/internal/common/merge"
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/processor/ass_processor"
	"business-workflow/internal/processor/audio_processor"
	"business-workflow/internal/processor/processor_util"
	"business-workflow/internal/repo/commentary_repo"
	commentaryRepoImpl "business-workflow/internal/repo/commentary_repo/impl"
	enginetaskrepo "business-workflow/internal/repo/engine_task_repo"
	"business-workflow/internal/repo/engine_task_repo/impl"
	"business-workflow/internal/repo/subtitle_merge"
	mergeRepoImpl "business-workflow/internal/repo/subtitle_merge/impl"
	"business-workflow/internal/util/media"
	hobs "business-workflow/internal/util/obs"
	"context"
	"encoding/json"
	"fmt"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"github.com/google/uuid"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/utils/acl"
	omniProto "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"

	"github.com/gogf/gf/v2/frame/g"
	"go.temporal.io/sdk/temporal"
)

// 确保在init时自动注册

type MergeActivity struct {
	subTaskRepo  commentary_repo.ICommentarySubTaskRepo
	itemRepo     commentary_repo.ICommentarySubtitleItemRepo
	mergeRepo    subtitle_merge.ISubtitleMergeTask
	engineRepo   enginetaskrepo.IEngineTaskRepo
	snapshotRepo commentary_repo.CommentarySubtitleItemSnapshotRepoImpl
	from         consts.WorkFlowFrom
}

var (
	noRetryErr = temporal.NewNonRetryableApplicationError(
		"defaultMergeNoRetryError",
		"InvalidOrderError",
		nil)

	retryErr = temporal.NewApplicationError( // 需要重试的错误
		"defaultMergeRetryError",
		"mergeRetryError",
		nil)
)

func (a *MergeActivity) IsSkip(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) bool {
	return false
}

func (a *MergeActivity) toMergeAudioTask(ctx context.Context, subTask *do.CommentarySubTask, items []*do.CommentarySubtitleItem) (*audio_processor.AudioMergeTask, error) {
	g.Log().Infof(ctx, "MergeActivity toMergeAudioTask, subTaskId: %v, items: %+v", subTask.Id, items)
	mergeTask := &audio_processor.AudioMergeTask{}
	mergeTask.TaskId = subTask.Id
	mergeTask.BgUrl = subTask.BgmUrl
	mergeTask.DisableBgm = subTask.BgmMode == consts.BgmModeOff
	if subTask.BgmMode == consts.BgmModeOn {
		if len(subTask.BgmUrl) < 5 {
			mergeTask.DisableBgm = true
			g.Log().Warningf(ctx, "MergeActivity toMergeAudioTask, bgmurl is invalied, subtaskId: %v, bgmUrl: %v",
				subTask.Id, subTask.BgmUrl)
			//return nil, fmt.Errorf("invalid bgmUrl: %v", subTask.BgmUrl)
		}
	}
	for _, item := range items {
		audioItem := &audio_processor.AudioItem{}
		audioItem.ItemIdx = int(item.ItemIdx)
		audioItem.TtsUrl = item.TTSUrl
		audioItem.SubtitleStartStr = item.SubtitleStartStr
		if item.AudioConfig != nil {
			audioItem.Config = &audio_processor.AudioConfig{}
			audioItem.Config.VolumeGainDB = item.AudioConfig.VolumeGainDB
			audioItem.Config.Speed = item.AudioConfig.Speed
		}
		mergeTask.TtsUrls = append(mergeTask.TtsUrls, audioItem)
	}
	return mergeTask, nil
}

func (a *MergeActivity) toSubItems(items []*do.CommentarySubtitleItemSnapShot) []*do.CommentarySubtitleItem {
	subItems := make([]*do.CommentarySubtitleItem, 0, len(items))
	for _, val := range items {
		if val.SubtitleItem != nil {
			//item := &do.CommentarySubtitleItem{}
			//item.ItemIdx = val.SubtitleItem.ItemIdx
			//item.SubItemList = val.SubtitleItem.SubItemList
			//item.SubtitleEndStr = val.SubtitleItem.SubtitleEndStr
			//item.SubtitleStartStr = val.SubtitleItem.SubtitleStartStr
			//item.TTSUrl = val.SubtitleItem.TTSUrl
			//if val.SubtitleItem.AudioConfig != nil {
			//	item.AudioConfig = val.SubtitleItem.AudioConfig
			//}

			subItems = append(subItems, val.SubtitleItem)
		}
	}
	// 排序
	sort.Slice(subItems, func(i, j int) bool {
		return subItems[i].ItemIdx < subItems[j].ItemIdx
	})
	return subItems
}

func (a *MergeActivity) WaitCondition(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) error {
	g.Log().Infof(ctx, "MergeActivity WaitCondition, subTaskId: %v", subTask.Id)
	// 合成音频并上传？？？或者放在本地？？？
	subTaskDO, err := a.subTaskRepo.GetSubTaskById(ctx, subTask.Id)
	if err != nil {
		g.Log().Error(ctx, "MergeActivity WaitCondition, GetSubTaskById error: %v", err)
		return retryErr
	}
	//subItems, err := a.itemRepo.GetSubtitleItemsBySubTaskId(ctx, subTaskDO.Id)
	items, err := a.snapshotRepo.GetSubtitleSnapshotItemsBySubTaskId(ctx, subTaskDO.Id)
	if err != nil {
		g.Log().Error(ctx, "MergeActivity WaitCondition, GetItemsBySubTaskId error: %v", err)
		return retryErr
	}
	subItems := a.toSubItems(items)
	mergeTask, err := a.toMergeAudioTask(ctx, subTaskDO, subItems)
	if err != nil {
		g.Log().Error(ctx, "MergeActivity WaitCondition, ToMergeAudioTask, subTaskId: %v, error: %v", subTask.Id, err)
		return noRetryErr
	}
	_, err = audio_processor.MergeAudio(ctx, mergeTask, 5)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity WaitCondition, MergeAudioTask failed, subTaskId: %v, err: %v", subTask.Id, err)
		return noRetryErr
	}
	if len(mergeTask.OutLocalPath) > 5 {
		err = processor_util.EnsureFile(mergeTask.OutLocalPath)
		if err != nil {
			g.Log().Errorf(ctx, "MergeActivity WaitCondition, subTaskId: %v, EnsureFile audio failed: %v, err: %v", subTask.Id, mergeTask.OutLocalPath, err)
			return retryErr
		}
	}
	// 生成字幕ASS文件
	assFile, err := ass_processor.GenerateAssFileV2(subTaskDO, subItems)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity WaitCondition, GenerateAssFileV2 failed, subTaskId: %v, err: %v", subTask.Id, err)
		return retryErr
	}
	err = processor_util.EnsureFile(assFile)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity WaitCondition, subTaskId: %v, EnsureFile assFile failed: %v, err: %v", subTask.Id, assFile, err)
		return retryErr
	}
	g.Log().Infof(ctx, "MergeActivity WaitCondition, CreateSubtitleMergeTask, subTaskId: %v", subTask.Id)
	// 生成一条合成记录
	taskDO := &do.WorkflowMergeTask{}
	taskDO.MainTaskId = task.Id
	taskDO.SubTaskId = subTask.Id
	taskDO.AudioUrl = mergeTask.OutLocalPath
	if subTaskDO.EraseMode == consts.EraseModeOff {
		taskDO.VideoUrl = subTaskDO.MaterialHighlightUrl
		g.Log().Infof(ctx, "MergeActivity WaitCondition, EraseModeOff subTaskId: %v, taskType: %v", subTask.Id, task.TaskType)
		if task.TaskType != consts.TaskTypeHighlight {
			taskDO.VideoUrl, err = a.getVideoFromOrigin(ctx, subTaskDO, subTaskDO.MaterialHighlightUrl)
			if err != nil {
				g.Log().Errorf(ctx, "MergeActivity WaitCondition, getVideoFromOrigin failed, subTaskId: %v, MaterialHighlightUrl: %v, err: %v",
					subTask.Id, subTask.MaterialHighlightUrl, err)
				return retryErr
			}
		}
	} else {
		taskDO.VideoUrl = subTaskDO.NoSubtitleMaterialHighlightUrl
	}
	taskDO.AssUrl = assFile
	taskDO.TenantID = subTask.TenantId
	taskDO.Subtitle = int(subTaskDO.SubtitleMode)
	taskDO.Vocal = 1
	if len(mergeTask.OutLocalPath) < 5 {
		g.Log().Warningf(ctx, "MergeActivity WaitCondition, vocal audio is null, disable audio. subtaskId: %v, len(items): %v",
			subTask.Id, len(subItems))
		taskDO.Vocal = 0
	}
	taskDO.AppId = task.AppId
	taskDO.VideoDuration = float32(subTask.VideoDuration)
	taskDO.Status = int(consts.MergeStatusPending)
	taskDO.PostVideoObjectName = processor_util.GetObjectName(subTaskDO.TenantId, subTask.Id, "subtitle_merge.mp4")
	err = a.mergeRepo.CreateSubtitleMergeTask(ctx, taskDO)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity WaitCondition, CreateSubtitleMergeTask failed, subTaskId: %v, err: %v", subTask.Id, err)
		return retryErr
	}
	return nil
}

func (a *MergeActivity) getVideoFromOrigin(ctx context.Context, subTask *do.CommentarySubTask, videoUrl string) (string, error) {
	g.Log().Infof(ctx, "MergeActivity getVideoFromOrigin, subTaskId: %v", subTask.Id)
	objectName, err := processor_util.GetObjectNameByHttpsUrl(videoUrl)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity getVideoFromOrigin, GetObjectNameByHttpsUrl failed, subTaskId: %v, videoUrl: %s, err: %v",
			subTask.Id, videoUrl, err)
		return "", err
	}
	localPath := audio_processor.GetLocalVideoPathById(ctx, subTask.Id)
	err = common.EnsureOutputDirectory(localPath)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity getVideoFromOrigin, EnsureOutputDirectory failed, subTaskId: %v, localPath: %s, err: %v",
			subTask.Id, localPath, err)
		return "", err
	}
	localFile := fmt.Sprintf("%s/pre_merge_%s%s", audio_processor.GetLocalVideoPathById(ctx, subTask.Id), uuid.New().String(), filepath.Ext(objectName))
	err = hobs.GetOsClient().DownloadFile(objectName, localFile)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity getVideoFromOrigin, DownloadFile failed, subTaskId: %v, videoUrl: %s, err: %v",
			subTask.Id, videoUrl, err)
		return "", err
	}
	avInfo, err := media.GetAudioVideoInfo(localFile)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity getVideoFromOrigin, GetAudioVideoInfo failed, subTaskId: %v, localFile: %s, err: %v",
			subTask.Id, localFile, err)
		return "", err
	}
	if avInfo.VideoInfo.Duration == -1 {
		return localFile, nil
	}
	localRemoveAudioFile := fmt.Sprintf("%s/pre_merge_remove_audio_%s%s", audio_processor.GetLocalVideoPathById(ctx, subTask.Id), uuid.New().String(), filepath.Ext(objectName))
	err = media.RemoveAudioFromVideo(localFile, localRemoveAudioFile)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity getVideoFromOrigin, RemoveAudioFromVideo failed, subTaskId: %v, localFile: %s, err: %v",
			subTask.Id, localFile, err)
		return "", err
	}
	return localRemoveAudioFile, nil
}

func (a *MergeActivity) handleLocalMerge(ctx context.Context, task *bo.CommentaryMainTaskBO,
	subTask *bo.CommentarySubTaskBO, mergeTask *do.WorkflowMergeTask) error {
	localPath := audio_processor.GetLocalVideoPathById(ctx, subTask.Id)
	err := common.EnsureOutputDirectory(localPath)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity handleLocalMerge, EnsureOutputDirectory failed, path: %v err: %v",
			localPath, err)
		return err
	}
	subTaskDo, err := a.subTaskRepo.GetSubTaskById(ctx, subTask.Id)
	if err != nil {
		return err
	}

	if mergeTask.Vocal == 0 {
		// 不配音，关闭字幕
		if strings.Contains(mergeTask.VideoUrl, "https") {
			subTaskDo.MergedMaterialHighlightUrl = mergeTask.VideoUrl
		} else {
			objectName := processor_util.GetObjectName(subTask.TenantId, subTask.Id, mergeTask.VideoUrl)
			subTaskDo.MergedMaterialHighlightUrl, err = hobs.GetOsClient().UploadFile(mergeTask.VideoUrl, objectName, acl.AclTypePublicRead)
			if err != nil {
				g.Log().Errorf(ctx, "MergeActivity handleLocalMerge, no vocal UploadFile failed, subTaskId: %v, videoUrl: %s, err: %v",
					subTask.Id, mergeTask.VideoUrl, err)
				return err
			}
		}
		err = a.subTaskRepo.UpdateSubTask(ctx, subTaskDo)
		if err != nil {
			return err
		}
		return nil
	}
	localVideoFile := fmt.Sprintf("%s/local_merge_%s%s", localPath, uuid.New().String(), filepath.Ext(mergeTask.VideoUrl))
	if strings.Contains(mergeTask.VideoUrl, "https://") {
		_, err = processor_util.DownloadRemoteObjectToLocalUseOs(mergeTask.VideoUrl, localVideoFile)
		if err != nil {
			g.Log().Errorf(ctx, "MergeActivity handleLocalMerge, DownloadRemoteObjectToLocalUseOs failed, subTaskId: %v, err: %v",
				subTask.Id, err)
			return err
		}
	} else {
		localVideoFile = mergeTask.VideoUrl
	}
	// 合成音频
	mergeFile := fmt.Sprintf("%s/local_merge_merge_audio%s", localPath, filepath.Base(localVideoFile))
	err = media.MergeAudioAndVideo(mergeTask.AudioUrl, localVideoFile, mergeFile)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity handleLocalMerge, MergeAudioAndVideo failed, audio: %v, video: %v, out: %v err: %v",
			mergeTask.AudioUrl, localVideoFile, mergeFile, err)
		return err
	}
	//defer func() {
	//	errD := os.Remove(mergeFile)
	//	if errD != nil {
	//		g.Log().Errorf(ctx, "MergeActivity handleLocalMerge, os.Remove(mergeFile) failed, err: %v",
	//			errD)
	//	}
	//}()
	objectName := processor_util.GetObjectName(subTask.TenantId, subTask.Id, mergeFile)
	remoteUrl, err := hobs.GetOsClient().UploadFile(mergeFile, objectName, acl.AclTypePublicRead)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity handleLocalMerge, UploadFile failed, err: %v", err)
		return err
	}

	subTaskDo.MergedMaterialHighlightUrl = remoteUrl
	err = a.subTaskRepo.UpdateSubTask(ctx, subTaskDo)
	if err != nil {
		return err
	}
	return nil
}

func (a *MergeActivity) HandleSubmit(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) ([]int64, error) {
	// 查找合成任务
	mergeTask, err := a.mergeRepo.GetLatestMergeTaskBySubId(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity HandleSubmit, GetLatestMergeTaskBySubId failed, err: %v", err)
		return nil, noRetryErr
	}
	if mergeTask.Subtitle == int(consts.SubtitleModeOff) {
		g.Log().Infof(ctx, "MergeActivity HandleSubmit, 用户关闭字幕，不要合成字幕, subTaskId: %v", subTask.Id)
		err = a.handleLocalMerge(ctx, task, subTask, mergeTask)
		if err != nil {
			g.Log().Errorf(ctx, "MergeActivity HandleSubmit, handleLocalMerge failed, subTaskId: %v, err: %v", subTask.Id, err)
			return nil, noRetryErr
		}
		return nil, nil
	}
	// 查找engine任务
	engineTask, err := a.engineRepo.GetWorkflowEngineTaskByBidAndType(ctx, mergeTask.Id, int(omniProto.TaskType_COMMENTARY_SUBTITLE_MERGE))
	if err != nil {
		g.Log().Warningf(ctx, "MergeActivity HandleSubmit, GetWorkflowEngineTaskByBid failed, err: %v", err)
		return nil, noRetryErr
	}
	if engineTask.Id > 0 {
		g.Log().Warningf(ctx, "MergeActivity HandleSubmit, engineTask is not nil: %v", engineTask)
	}
	if mergeTask.Status != int(consts.MergeStatusPending) {
		if engineTask.Id > 0 && engineTask.Bid == mergeTask.Id {
			g.Log().Warningf(ctx, "MergeActivity HandleSubmit, engineTask is not nil: %v", engineTask)
			return []int64{engineTask.EngineTaskId}, nil
		} else {
			g.Log().Errorf(ctx, "MergeActivity HandleSubmit, ocrTask.Stutus is not MergeStatusPending: %v", mergeTask.Status)
			return nil, noRetryErr
		}
	}
	engineTask, err = a.submitToEngine(ctx, task, subTask, mergeTask)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity HandleSubmit, submitToEngine failed, subTaskId: %v, mergeId: %v, err: %v",
			subTask.Id, mergeTask.SubTaskId, err)
		return nil, retryErr
	}
	return []int64{engineTask.EngineTaskId}, nil
}

func (a *MergeActivity) BuildSubtitleMergeReq(mergeTask *do.WorkflowMergeTask) *merge.SubtitleMergeSubmitReq {
	req := &merge.SubtitleMergeSubmitReq{
		MergeTaskId:      mergeTask.Id,
		SubTaskId:        mergeTask.SubTaskId,
		VocalAudioPath:   mergeTask.AudioUrl,
		AssFilePath:      mergeTask.AssUrl,
		IsWaterMark:      false,
		IsVocalTask:      mergeTask.Vocal == 1,
		NotitleVideoPath: mergeTask.VideoUrl,
		TargetObjectName: mergeTask.PostVideoObjectName,
		PostVideoPath:    "",
		VideoDuration:    mergeTask.VideoDuration,
		Crf:              consts.Crf,
		Preset:           consts.Preset,
	}
	return req
}

func (a *MergeActivity) submitToEngine(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, mergeTask *do.WorkflowMergeTask) (*do.BusinessEngineTask, error) {
	req := a.BuildSubtitleMergeReq(mergeTask)
	req.PostVideoPath = fmt.Sprintf("%s/post_video_%v_%v_%s.mp4",
		audio_processor.GetLocalVideoPathById(ctx, subTask.Id), subTask.MainTaskId, subTask.Id, uuid.New().String())

	reqBase := conv.CommentaryMainTaskBOToBaseTask(task, subTask, int64(omniProto.TaskSource_INTERNAL_SOURCE),
		map[string]string{
			consts.BaseTaskCost: fmt.Sprintf("%v", subTask.VideoDuration),
		})

	taskInfo := &omniProto.TaskInfo{}
	taskInfo.Name = fmt.Sprintf("commentary_subtitle_merge_%v", mergeTask.Id)
	taskInfo.TaskType = omniProto.TaskType_COMMENTARY_SUBTITLE_MERGE
	taskInfo.Quantity = 1
	taskInfo.RefererId = task.Id

	data, err := json.Marshal(req)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity submitToEngine, json.Marshal failed, err: %v", err)
		return nil, err
	}
	taskInfo.CommonInputContent = string(data)

	createReq := &omniProto.CreateTaskReq{
		TaskBase: reqBase,
		TaskList: []*omniProto.TaskInfo{taskInfo},
	}

	createRes, err := omni_engine.GetOmniEngineClient().CreateTask(ctx, createReq)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity submitToEngine, CreateTask MergeActivity failed, err: %v", err)
		return nil, retryErr
	}
	if len(createRes.TaskIds) != 1 {
		g.Log().Errorf(ctx, "MergeActivity submitToEngine, CreateTask MergeActivity failed, len is not 1: %v", len(createRes.TaskIds))
		return nil, retryErr
	}

	mergeTask.ProcessSubmitAt = time.Now()
	mergeTask.Status = int(consts.MergeStatusProcessing)
	handoutTime := time.Now().Add(time.Duration(config.GetConfig().Temporal.ScheduleToCloseTimeout) / 2 * time.Minute)

	enginetask := &do.BusinessEngineTask{
		EngineTaskId:  createRes.TaskIds[0],
		MainTaskId:    subTask.MainTaskId,
		Bid:           mergeTask.Id,
		TenantID:      mergeTask.TenantID,
		TaskType:      int(taskInfo.TaskType),
		EngineStatus:  consts.EngineWorkflowStatusInit,
		HandleTimeout: &handoutTime,
	}

	subTaskDO, err := a.subTaskRepo.GetSubTaskById(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity submitToEngine, MergeActivity submitToEngine, GetSubTaskById failed, subTaskId: %v, err: %v", subTask.Id, err)
		return nil, err
	}
	subTaskDO.MergeStatus = consts.MergeStatusProcessing
	err = a.mergeRepo.CreateEngineTasksAndUpdateMergeTask(ctx, []*do.BusinessEngineTask{enginetask}, mergeTask, subTaskDO)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity submitToEngine, CreateEngineTasksAndUpdateMergeTask failed, subTaskId: %v, err: %v", subTask.Id, err)
		return nil, err
	}
	return enginetask, nil
}

func (a *MergeActivity) HandleResult(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, result []*do.BusinessEngineTask) error {
	g.Log().Infof(ctx, "MergeActivity HandleResult:%v", task)
	if len(result) == 0 {
		g.Log().Errorf(ctx, "MergeActivity HandleResult, result is empty")
		return noRetryErr
	}
	engineTask := result[0]
	omniTask := &omniProto.Task{}
	err := json.Unmarshal([]byte(engineTask.TaskInfo), omniTask)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity HandleResult, unmarshal merge engine task info failed, err: %v, subTaskId: %v, taskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return noRetryErr
	}
	if engineTask.EngineStatus == consts.EngineWorkflowStatusSuccess {
		err := a.handleSuccess(ctx, task, subTask, engineTask, omniTask)
		if err != nil {
			g.Log().Errorf(ctx, "MergeActivity handleSuccess failed, err: %v", err)
			return noRetryErr
		}
		return nil
	} else if engineTask.EngineStatus == consts.EngineWorkflowStatusFailed ||
		engineTask.EngineStatus == consts.EngineWorkflowStatusTimeout {
		err := a.handleFail(ctx, task, subTask, engineTask, omniTask)
		if err != nil {
			g.Log().Errorf(ctx, "MergeActivity handleFail failed, err: %v", err)
		}
		return noRetryErr
	} else {
		g.Log().Warningf(ctx, "MergeActivity HandleResult, status maybe wrong, engineStatus: %v", engineTask.EngineStatus)
		return retryErr
	}
}

func (a *MergeActivity) handleSuccess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO,
	engineTask *do.BusinessEngineTask, omniTask *omniProto.Task) error {
	g.Log().Infof(ctx, "MergeActivity HandleSuccess, subTaskId: %v, mergeTaskId: %v", subTask.Id, engineTask.Bid)
	res := &merge.SubtitleMergeEngineRes{}
	err := json.Unmarshal([]byte(omniTask.CommonOutputContent), res)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity handleSuccess, unmarshal engine task info failed, err: %v, subTaskId: %v, taskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	mergeTask, err := a.mergeRepo.GetMergeTaskById(ctx, engineTask.Bid)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity handleSuccess, get merge task by bid failed, err: %v, subTaskId: %v, engineTaskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	mergeTask.Status = int(consts.MergeStatusCompleted)
	if res.MergeStartAt > 0 {
		mergeTask.MergeStartAt = time.UnixMilli(res.MergeStartAt)
		mergeTask.MergeEndAt = time.UnixMilli(res.MergeEndAt)
		mergeTask.UploadStartAt = time.UnixMilli(res.MergeUploadStartAt)
		mergeTask.UploadEndAt = time.UnixMilli(res.MergeUploadEndAt)
	}
	_, mergeTask.PostUrl, err = hobs.GetOsClient().GetObjectUrl(mergeTask.PostVideoObjectName)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity handleSuccess, get post video url failed, err: %v, subTaskId: %v, engineTaskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}

	subTaskDO, err := a.subTaskRepo.GetSubTaskById(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity handleSuccess, get subtask by id failed, err: %v, subTaskId: %v, engineTaskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	//subTaskDO.MergedVideoUrl = mergeTask.PostUrl
	subTaskDO.MergedMaterialHighlightUrl = mergeTask.PostUrl

	return a.mergeRepo.UpdateMergeTaskAndSubTask(ctx, mergeTask, subTaskDO)
}

func (a *MergeActivity) handleFail(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO,
	engineTask *do.BusinessEngineTask, omniTask *omniProto.Task) error {
	g.Log().Errorf(ctx, "MergeActivity handleFail, subTaskId: %v, engineTaskId: %v",
		subTask.Id, engineTask.EngineTaskId)
	res := &merge.SubtitleMergeEngineRes{}
	err := json.Unmarshal([]byte(omniTask.CommonOutputContent), res)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity handleFail, unmarshal engine task info failed, err: %v, subTaskId: %v, taskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	mergeTask, err := a.mergeRepo.GetMergeTaskById(ctx, engineTask.Bid)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity handleFail, merge task by bid failed, err: %v, subTaskId: %v, engineTaskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	mergeTask.Status = int(consts.MergeStatusFailed)

	subTaskDO, err := a.subTaskRepo.GetSubTaskById(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity handleFail, get subtask by id failed, err: %v, subTaskId: %v",
			err, subTask.Id)
		return err
	}
	subTaskDO.MergeStatus = consts.MergeStatusFailed
	err = a.mergeRepo.UpdateMergeTaskAndSubTask(ctx, mergeTask, subTaskDO)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity handleFail, UpdateMergeTaskAndSubTask failed, err: %v, subTaskId: %v, engineTaskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	return nil
	//return processor.HandleSubTaskFail(ctx, subTask, int(res.RetCode), res.ErrMsg)
}

func (a *MergeActivity) HandleFailed(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, aerr *temporal.ApplicationError) error {
	g.Log().Errorf(ctx, "MergeActivity HandleFailed, err: %v, subTask: %+v", aerr, subTask)

	if a.from == consts.WorkFlowFromCommentaryFirst {
		err := activities.UpdateSubTaskStatusFailed(ctx, "MergeActivity", subTask)
		if err != nil {
			g.Log().Errorf(ctx, "MergeActivity HandleFailed, UpdateSubTaskStatusFailed failed, err: %v", err)
			return temporal.NewNonRetryableApplicationError("UpdateSubTaskStatusFailed error", "db error", err)
		}
		return nil
	}
	err := activities.UpdateSubTaskMergeStatusFailed(ctx, "MergeActivity", subTask)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity HandleFailed, UpdateSubTaskMergeStatusFailed failed, err: %v", err)
		return temporal.NewNonRetryableApplicationError("UpdateSubTaskMergeStatus failed", "db error", err)
	}
	return nil
}

func MergeProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, from consts.WorkFlowFrom) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, task.RequestID)
	g.Log().Info(ctx, "MergeProcess task:%v, sub_task:%v", task, subTask)
	a := &activities.Processor{}
	return a.Process(ctx, &MergeActivity{
		subTaskRepo:  commentaryRepoImpl.NewCommentarySubTaskRepoImpl(),
		itemRepo:     commentaryRepoImpl.NewCommentarySubtitleItemRepoImpl(),
		mergeRepo:    mergeRepoImpl.NewSubtitleMergeTaskImpl(),
		engineRepo:   impl.NewEngineTaskRepoImpl(),
		snapshotRepo: commentaryRepoImpl.NewCommentarySubtitleItemSnapShotRepoImpl(),
		from:         from,
	}, task, subTask)
}
