package tts

import (
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"context"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	omni_balance "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_balance"
)

// 处理二次编辑tts扣费回滚等清理逻辑
func TTSCleanUpProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, subtitleItemId int64, payOrderId string) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, task.RequestID)
	g.Log().Info(ctx, "TTSCleanProcess Process, task: %+v", task)

	// 1. 参数验证
	if task == nil || subTask == nil {
		return nil, fmt.Errorf("task or subTask cannot be nil")
	}

	if payOrderId == "" {
		g.Log().Warningf(ctx, "[TTSCleanUpProcess] payOrderId is empty, skip refund")
		return task, nil
	}
	subtitleItem, err := getSubtitleItem(ctx, subtitleItemId)
	if err != nil {
		return nil, fmt.Errorf("get subtitle item failed: %w", err)
	}
	if subtitleItem == nil {
		return nil, fmt.Errorf("subtitle item is nil")
	}
	if subtitleItem.GenerateVoiceStatus != consts.SubtitleItemRegenerateStatusFailed {
		g.Log().Debugf(ctx, "[TTSCleanUpProcess] subtitle item generate voice status is not failed, skip refund")
		return nil, nil
	}

	// 2. 构建退款请求
	// 生成退款订单ID
	outOrderId := fmt.Sprintf("tts_refund_%s", payOrderId)
	mainTaskId := payOrderId

	refundReq := &omni_balance.AVoiceRefundEventCreditsRequest{
		AppId:      task.AppId,
		TenantId:   task.TenantId,
		PayOrderId: payOrderId, // 原扣款订单ID
		OutOrderId: outOrderId, // 退款订单ID
		MainTaskId: mainTaskId, // 主任务ID
	}

	// 3. 调用omni-balance服务进行退款
	balanceClient := omni_engine.GetOmniBalanceAVLBalance()
	if balanceClient == nil {
		g.Log().Errorf(ctx, "[TTSCleanUpProcess] balance client is nil")
		return nil, fmt.Errorf("balance client is not initialized")
	}

	_, err = balanceClient.AVoiceRefundEventCredits(ctx, refundReq)
	if err != nil {
		g.Log().Errorf(ctx, "[TTSCleanUpProcess] TTS refund failed: %v, request: %+v", err, refundReq)
		return nil, fmt.Errorf("TTS refund failed: %w", err)
	}

	g.Log().Infof(ctx, "[TTSCleanUpProcess] TTS refund successful, main_task_id: %d, sub_task_id: %d, subtitle_item_id: %d, pay_order_id: %s, out_order_id: %s",
		task.Id, subTask.Id, subtitleItemId, payOrderId, outOrderId)

	return task, nil
}
