package tts

import (
	"business-workflow/internal/activities"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/common"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/processor/sub_item_processor"
	"business-workflow/internal/repo/commentary_repo"
	commentary_repo_impl "business-workflow/internal/repo/commentary_repo/impl"
	enginetaskrepo "business-workflow/internal/repo/engine_task_repo"
	engine_task_impl "business-workflow/internal/repo/engine_task_repo/impl"
	"business-workflow/internal/util"
	"business-workflow/internal/util/obs"
	"encoding/json"
	"errors"
	"time"

	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	"gitlab.ttyuyin.com/tyr/x/utils"

	"context"
	"fmt"

	"business-workflow/internal/common/config"
	"business-workflow/internal/common/db"
	"business-workflow/internal/common/omni_engine"

	"github.com/gogf/gf/v2/frame/g"
	apiBusiness "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/api_business"
	pb "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"

	"github.com/samber/lo"
	"go.temporal.io/sdk/activity"
	"go.temporal.io/sdk/temporal"
	"gorm.io/gorm"
)

type TTSActivity struct {
	commentarySubtitleitemRepo commentary_repo.ICommentarySubtitleItemRepo
	engineTaskRepo             enginetaskrepo.IEngineTaskRepo
	commentarySubTaskRepo      commentary_repo.ICommentarySubTaskRepo
	SubtitleItemList           []*bo.CommentarySubtitleItemBO
	isFirst                    bool  // 是否为第一次直出与二次编辑的tts
	subtitleItemId             int64 //二次编辑的字幕id
}

func (a *TTSActivity) IsSkip(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) bool {
	if task.TaskType == consts.TaskTypeHighlight {
		g.Log().Infof(ctx, "highligh task, skip tts sub_task_id:%v", subTask.Id)
		return true
	}
	return false
}

func (a *TTSActivity) WaitCondition(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) error {

	//1. 检查voice_id是否存在
	if subTask.VoiceId == 0 {
		return temporal.NewNonRetryableApplicationError(
			"invalid voice_id",
			"InvalidVoiceIdError",
			nil,
		)
	}
	return nil
}

func (a *TTSActivity) HandleSubmit(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) ([]int64, error) {

	// 1. 获取字幕记录
	subtitleItems, err := a.getSubtitleItemList(ctx, subTask)
	if err != nil {
		g.Log().Errorf(ctx, "getSubtitleItemList error:%v", err)
		return nil, err
	}
	//	2.根据voice_id拿到音色信息
	voiceResp, err := omni_engine.GetOmniBusinessClient().GetVoice(ctx, &apiBusiness.GetVoiceRequest{
		AppId:    task.AppId,
		TenantId: task.TenantId,
		VoiceId:  subTask.VoiceId,
	})
	if err != nil {
		g.Log().Errorf(ctx, "GetVoice failed, err: %v, task_id: %d, subId: %d", err, task.Id, subTask.Id)
		return nil, err
	}
	sourceUrl := voiceResp.GetVoice().GetVocalInfo().GetVocalUrl()
	sourceText := voiceResp.GetVoice().GetVocalInfo().GetText()
	isPvc := voiceResp.GetVoice().GetVoiceType() != consts.TTSModelTypeIVC
	speakerName := voiceResp.GetVoice().GetSpeakerName()
	source := int64(pb.TaskSource_INTERNAL_SOURCE)
	isTrans := false
	priority := 0
	if !a.isFirst {
		source = int64(pb.TaskSource_WORKBENCH_SOURCE)
		priority = 9
	}

	// // 批量提交tts任务
	// taskBase := &pb.TaskBase{
	// 	AppId:    task.AppId,
	// 	TenantId: task.TenantId,
	// 	Priority: int64(priority),
	// 	Apikey:   task.ApiKey,
	// 	Source:   source,
	// 	TimeOut:  int64(15 * 60), //15分钟
	// 	ExtraMap: map[string]string{
	// 		// "video_translate_id": fmt.Sprintf("%v", subTask.Id),
	// 		consts.BaseTaskCommentrayMainTaskID: strconv.FormatInt(task.Id, 10),
	// 		consts.BaseTaskCommentraySubTaskID:  strconv.FormatInt(subTask.Id, 10),
	// 		consts.BaseTaskCommentrayBizMode:    strconv.FormatInt(int64(task.BizMode), 10),
	// 		consts.BaseTaskCommentrayTaskType:   strconv.FormatInt(int64(task.TaskType), 10),
	// 	},
	// }
	taskBase := conv.CommentaryMainTaskBOToBaseTask(task, subTask, source, map[string]string{})
	taskBase.Priority = int64(priority)
	taskBase.TimeOut = int64(15 * 60)

	var subtitleItemIdToEngineTaskId map[int64]int64 = make(map[int64]int64)
	var engineTaskIDs []int64
	for _, item := range subtitleItems {
		if activity.HasHeartbeatDetails(ctx) { // 显式检查
			activity.GetHeartbeatDetails(ctx, &subtitleItemIdToEngineTaskId)
		}
		// 如果已经创建过任务，直接跳过
		if engineTaskId, ok := subtitleItemIdToEngineTaskId[item.Id]; ok && engineTaskId != 0 {
			engineTaskIDs = append(engineTaskIDs, engineTaskId)
			continue
		}
		var expectDuration int64
		if !a.isFirst {
			expectDuration, _ = getExpectDuration(ctx, item)

		}
		ttsStoreObjectName := util.GenerateTargetUrl(task.TenantId, task.Id, "audio-clone", sourceUrl)

		content := &pb.TTSTaskInputContent{
			Type:           "tts",
			SourceUrl:      sourceUrl,
			TargetUrl:      ttsStoreObjectName,
			SourceText:     sourceText,
			TargetText:     item.TargetSubtitle,
			SourceLang:     item.SourceLangId,
			TargetLang:     item.TargetLangId,
			IsTrans:        isTrans,
			Speed:          1,
			VolumeNorm:     1,
			ExpectDuration: int64(expectDuration),
		}
		// 根据音色类型设置特定参数

		// 构建单个任务
		curTaskInfo := &pb.TaskInfo{
			Name:                  item.TargetSubtitle,
			BizType:               pb.VoiceBusinessType_VOICE_BUSINESS_TYPE_VIDEO_TRANSLATE,
			VoiceId:               subTask.VoiceId,
			TtsTaskInputContent:   content,
			BusinessScenariosType: int32(pb.BusinessScenariosType_BUSINESS_TTS_SERVICE),
			RefererId:             task.Id,
		}
		if isPvc {
			curTaskInfo.TaskType = pb.TaskType_TTS_SERVICE_PVC
			curTaskInfo.Quantity = 0
			curTaskInfo.TtsTaskInputContent.SpeakerName = speakerName
			curTaskInfo.TtsTaskInputContent.SourceText = item.OriginSubtitle
		} else if task.DubbingType > 0 {
			curTaskInfo.TaskType = pb.TaskType_TTS_SERVICE_THIRDPARTY_11LABS
			curTaskInfo.Quantity = float64(len([]rune(item.TargetSubtitle)))
		} else {
			curTaskInfo.TaskType = pb.TaskType_TTS_SERVICE_MULTILANG
			curTaskInfo.Quantity = float64(len([]rune(item.TargetSubtitle)))
		}
		taskBase.ExtraMap["task_cost"] = fmt.Sprintf("%d", len([]rune(item.TargetSubtitle)))
		// 构建单个任务请求
		createReq := &pb.CreateTaskReq{
			TaskBase: taskBase,
			TaskList: []*pb.TaskInfo{curTaskInfo},
		}

		// 创建单个任务
		g.Log().Infof(ctx, "creating single tts task req:%v", createReq)
		createRes, err := omni_engine.GetOmniEngineClient().CreateTask(ctx, createReq)
		if err != nil {
			g.Log().Errorf(ctx, "create single tts task err:%v", err)
			return nil, err
		}

		if len(createRes.TaskIds) != 1 {
			return nil, errors.New("create single tts task error")
		}
		handoutTime := time.Now().Add(time.Duration(config.GetConfig().Temporal.ScheduleToCloseTimeout) / 2 * time.Minute)
		enginetask := &do.BusinessEngineTask{
			AppId:         task.AppId,
			TenantID:      task.TenantId,
			MainTaskId:    task.Id,
			Bid:           item.Id,
			TaskType:      int(curTaskInfo.TaskType),
			EngineTaskId:  createRes.TaskIds[0],
			EngineStatus:  consts.EngineWorkflowStatusInit,
			HandleTimeout: &handoutTime,
		}
		err = a.engineTaskRepo.CreateWorkflowEngineTask(ctx, enginetask)
		if err != nil {
			return nil, temporal.NewNonRetryableApplicationError(
				"CreateWorkflowEngineTask error",
				"db error",
				err)
		}

		engineTaskIDs = append(engineTaskIDs, enginetask.EngineTaskId)
		//记录本次进度
		subtitleItemIdToEngineTaskId[item.Id] = enginetask.EngineTaskId
		activity.RecordHeartbeat(ctx, subtitleItemIdToEngineTaskId)
		g.Log().Infof(ctx, "summit task success, engine_task_id:%v", enginetask.EngineTaskId)
	}

	return engineTaskIDs, nil
}

func (a *TTSActivity) HandleResult(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, result []*do.BusinessEngineTask) error {
	// 1. 根据lastest_tts_info是否为空判断是否为第一次配音，如果非第一次，需要调速

	subtitleItems, err := a.getSubtitleItemList(ctx, subTask)
	if err != nil {
		g.Log().Errorf(ctx, "getSubtitleItemList error:%v", err)
		return err
	}

	// 开启gorm事务
	for _, subtitleItem := range subtitleItems {
		for _, engineTask := range result {
			if subtitleItem.Id != engineTask.Bid {
				continue
			}
			// 更新字幕记录
			taskDetail := &pb.Task{}
			err = json.Unmarshal([]byte(engineTask.TaskInfo), taskDetail)
			if err != nil {
				g.Log().Errorf(ctx, "unmarshal task_info error:%v", err)
				return err
			}
			switch taskDetail.GetStatus() {
			case pb.TaskStatus_COMPLETED:
				g.Log().Infof(ctx, "subtitle_item_id:%v tts HandleSuccess:%v", subtitleItem.Id, taskDetail)
				err = a.HandleSuccess(ctx, subTask, subtitleItem, taskDetail)
				if err != nil {
					g.Log().Errorf(ctx, "handle success error:%v", err)
					return err
				}
			case pb.TaskStatus_FAILED, pb.TaskStatus_QUEUING_TIMEOUT:
				g.Log().Errorf(ctx, "subtitle_item_id:%v, tts HandleFailed:%v", subtitleItem.Id, taskDetail)
				return temporal.NewNonRetryableApplicationError(
					"engine tts task failed",
					"task failed",
					fmt.Errorf("engine task failed, status: %v", taskDetail.GetStatus()))
			}
		}
	}

	g.Log().Infof(ctx, "TTSActivity HandleSuccess:%v", result)
	return nil
}
func (a *TTSActivity) HandleSuccess(ctx context.Context, subTask *bo.CommentarySubTaskBO, subtitleItem *do.CommentarySubtitleItem, taskDetail *pb.Task) error {
	g.Log().Infof(ctx, "HandleSuccess start ,isFirst: %v, sub_task_id: %d, subtitle_item_id: %d,item_info: %v", a.isFirst, subTask.Id, subtitleItem.Id, utils.ToJson(subtitleItem))
	if subtitleItem.AudioConfig == nil {
		subtitleItem.AudioConfig = &common.AudioConfig{}
	}
	ttsStoreObjectName := taskDetail.GetTtsTaskInputContent().GetTargetUrl()
	_, err := obs.GetOsClient().SetPublicRead(ttsStoreObjectName)
	if err != nil {
		g.Log().Errorf(ctx, "GenerateVoice SetPublicRead failed, err: %v,subTaskId: %d", err, subTask.Id)
		return err
	}
	subtitleItem.TTSUrl = fmt.Sprintf("https://%s/%s", config.GetConfig().Obs.CdnName, ttsStoreObjectName)
	ttsMp3durationMs, err := getTTSDurationMs(ctx, subtitleItem.TTSUrl)
	g.Log().Infof(ctx, "getTTSDurationMs success,sub_task_id: %d,item_id: %d, tts_url: %s, tts_mp3duration_ms: %d", subTask.Id, subtitleItem.Id, subtitleItem.TTSUrl, ttsMp3durationMs)
	if err != nil {
		g.Log().Errorf(ctx, "getTTSDurationMs failed, err: %v, tts_url: %s", err, subtitleItem.TTSUrl)
	}

	if a.isFirst {
		// 第一次不需要调速
		subtitleItem.AudioConfig.Speed = 1.15
	} else {
		// 二次编辑需要调速，先计算新的speed，然后根据speed反推subtitle_end_str
		newSpeed, _, adjustSpeed, _, err := adjustTTSSpeed(ctx, subtitleItem.TTSUrl, ttsMp3durationMs, subtitleItem)
		if err != nil {
			g.Log().Errorf(ctx, "adjustTTSSpeed failed, err: %v, sub_task_id: %d, subtitle_item_id: %d", err, subTask.Id, subtitleItem.Id)
		}

		if adjustSpeed {
			g.Log().Infof(ctx, "adjustTTSSpeed success, sub_task_id: %d, subtitle_item_id: %d, newSpeed: %v", subTask.Id, subtitleItem.Id, newSpeed)
			subtitleItem.AudioConfig.Speed = newSpeed
		}
	}
	//根据ttsAudio和倍速计算新的译文结束时间
	newSubtitleEndStr, err := computeNewSubtitleEndStr(ctx, ttsMp3durationMs, subtitleItem.SubtitleStartStr, float32(subtitleItem.AudioConfig.Speed))
	if err != nil {
		g.Log().Errorf(ctx, "computeNewSubtitleEndStr failed, err: %v, ub_task_id: %d, subtitle_item_id: %d,SubtitleStartStr:%s", err, subTask.Id, subtitleItem.Id, subtitleItem.SubtitleStartStr)
	} else {
		subtitleItem.SubtitleEndStr = newSubtitleEndStr
		g.Log().Infof(ctx, "computeNewSubtitleEndStr success, sub_task_id: %d, subtitle_item_id: %d, newSubtitleEndStr: %s", subTask.Id, subtitleItem.Id, newSubtitleEndStr)
	}

	subtitleItem.GenerateVoiceStatus = consts.SubtitleItemRegenerateStatusNone
	subtitleItem.TTSWords = lo.Map(taskDetail.GetTtsTaskOutputContent().GetData().GetWords(), func(word *pb.WordInfo, _ int) *common.WordInfo {
		return &common.WordInfo{
			Start: word.GetStart(),
			End:   word.GetEnd(),
			Word:  word.GetWord(),
		}
	})
	subtitleItem.LatestTTSInfo = &common.LatestTTSInfo{
		TargetSubtitle: subtitleItem.TargetSubtitle,
		VoiceInfo: &common.VoiceInfo{
			VoiceId:     subTask.VoiceId,
			VoiceSource: consts.VoiceSourceOfficial, // 官方音色库
		},
	}
	g.Log().Infof(ctx, "TTSActivity UpdateSubtitleItemSubList start, subTaskId: %v, subtitleItem: %v", subTask.Id, utils.ToJson(subtitleItem))
	sub_item_processor.UpdateSubtitleItemSubList(ctx, conv.CommentarySubTaskBOToDO(subTask), []*do.CommentarySubtitleItem{subtitleItem})
	err = a.commentarySubtitleitemRepo.UpdateSubtitleItemTtsInfo(ctx, subtitleItem)
	if err != nil {
		return temporal.NewNonRetryableApplicationError(
			"UpdateSubtitleItemTtsInfo error",
			"db error",
			err)

	}
	return nil
}

func (a *TTSActivity) HandleFailed(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, aerr *temporal.ApplicationError) error {
	subtitleItems, err := a.getSubtitleItemList(ctx, subTask)
	if err != nil {
		g.Log().Errorf(ctx, "getSubtitleItemList error:%v", err)
		return err
	}
	err = db.GetDB().Transaction(func(tx *gorm.DB) error {
		for _, subtitleItem := range subtitleItems {
			g.Log().Errorf(ctx, "subtitle_item_id:%v, agent text translate HandleFailed", subtitleItem.Id)
			subtitleItem.TextTranslateStatus = consts.SubtitleItemReTranslateStatusFailed
			err := a.commentarySubtitleitemRepo.UpdateTextTranslateStatus(ctx, subtitleItem.Id, subtitleItem.TextTranslateStatus, tx)
			if err != nil {
				g.Log().Errorf(ctx, "handle success error:%v", err)
				return err
			}
		}
		if a.isFirst {
			err := a.commentarySubTaskRepo.UpdateSubTaskStatus(ctx, subTask.Id, int(consts.SubTaskStatusFailed), aerr.Error(), tx)
			if err != nil {
				g.Log().Errorf(ctx, "update sub task status failed:%v", err)
				return err
			}
		}
		return nil
	})
	if err != nil {
		return temporal.NewNonRetryableApplicationError(
			"HandleFailed error",
			"db error",
			err)
	}
	return nil
}

func (a *TTSActivity) getSubtitleItemList(ctx context.Context, subTask *bo.CommentarySubTaskBO) ([]*do.CommentarySubtitleItem, error) {
	var subtitleItems []*do.CommentarySubtitleItem
	var err error
	//区分第一次直出与二次编辑的流程
	if a.isFirst {
		// 直出根据subTaskId获取所有字幕
		subtitleItems, err = a.commentarySubtitleitemRepo.GetSubtitleItemsBySubTaskId(ctx, subTask.Id)
		if err != nil {
			return nil, temporal.NewNonRetryableApplicationError(

				"GetSubtitleItemsBySubTaskId error",
				"db error",
				err)
		}
	} else {
		// 二次编辑只获取当前字幕
		subtitleItem, err := a.commentarySubtitleitemRepo.GetSubtitleItemById(ctx, a.subtitleItemId)
		if err != nil {
			return nil, temporal.NewNonRetryableApplicationError(
				"GetSubtitleItemById error",
				"db error",
				err)
		}
		subtitleItems = []*do.CommentarySubtitleItem{subtitleItem}
	}
	if len(subtitleItems) == 0 {
		return nil, temporal.NewNonRetryableApplicationError(
			"GetSubtitleItemsBySubTaskId no items",
			"db error",
			nil,
		)
	}
	return subtitleItems, nil
}

func TTSProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, subtitleItemId int64) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, activities.GetRequestId(ctx))
	g.Log().Infof(ctx, "TTSProcess:%+v, sub_task:%+v", task, subTask)
	a := &activities.Processor{}
	isFirst := true
	if subtitleItemId > 0 {
		// 如果传了字幕id，说明是二次编辑
		isFirst = false
	}
	return a.Process(ctx, &TTSActivity{
		engineTaskRepo:             engine_task_impl.NewEngineTaskRepoImpl(),
		commentarySubtitleitemRepo: commentary_repo_impl.NewCommentarySubtitleItemRepoImpl(),
		commentarySubTaskRepo:      commentary_repo_impl.NewCommentarySubTaskRepoImpl(),
		subtitleItemId:             subtitleItemId,
		isFirst:                    isFirst,
	}, task, subTask)
}
