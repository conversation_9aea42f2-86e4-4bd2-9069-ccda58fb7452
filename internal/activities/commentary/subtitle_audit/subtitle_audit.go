package subtitle_audit

import (
	"business-workflow/internal/activities"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/processor/audit_processor"
	"business-workflow/internal/repo/commentary_repo/impl"
	"business-workflow/internal/util/translate"
	"context"
	"strings"
	"time"

	"github.com/avast/retry-go"
	"github.com/samber/lo"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/utils/audit_util"
	"go.temporal.io/sdk/temporal"

	"github.com/gogf/gf/v2/frame/g"
)

func SubtitleAuditProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, activities.GetRequestId(ctx))
	g.Log().Info(ctx, "SubtitleAuditProcess task:%v, sub_task:%+v", task, subTask)
	// 直接出的 不用审核
	// 后续用户上传的srt 才审核
	repoImpl := impl.NewCommentarySubTaskRepoImpl()
	err := repoImpl.UpdateSubTaskFields(ctx, subTask.Id, map[string]interface{}{
		"audit_status": consts.AuditStatusSuccess,
	})
	if err != nil {
		g.Log().Error(ctx, "UpdateSubTaskFields err:%v", err)
		err := activities.UpdateSubTaskStatusFailed(ctx, "SubtitleAuditProcess", subTask)
		if err != nil {
			return nil, err
		}
		return nil, temporal.NewNonRetryableApplicationError(
			"UpdateSubTaskFields err",
			"oss error",
			err)
	}
	return task, nil
}

func SubtitleAuditForMerge(ctx context.Context, subTask *bo.CommentarySubTaskBO) (*bo.CommentarySubTaskBO, error) {
	g.Log().Info(ctx, "SubtitleAuditForMerge  sub_task:%+v", subTask)
	if !audit_processor.EnableAudit() {
		return subTask, nil
	}
	// 同步翻译
	shotRepoImpl := impl.NewCommentarySubtitleItemSnapShotRepoImpl()
	subtitleItems, err := shotRepoImpl.GetSubtitleSnapshotItemsBySubTaskId(ctx, subTask.Id)
	if err != nil {
		return subTask, err
	}

	fromText := lo.Map(subtitleItems, func(item *do.CommentarySubtitleItemSnapShot, _ int) string {
		return item.SubtitleItem.TargetSubtitle
	})

	// 中文不用翻译
	auditText := strings.Join(fromText, "")
	if subTask.TargetLangId != string(consts.LanguageZh) {
		subText, err := subtitleTranslate(ctx, fromText, subTask)
		if err != nil {
			// 可重试
			g.Log().Error(ctx, "subtitle translate error:%v", err)
			return subTask, temporal.NewApplicationError("subtitle translate error", "subtitle translate error", err)
		}
		g.Log().Info(ctx, "SubtitleAuditForMerge  subText:%s", subText)
		auditText = subText
	}
	// 同步审核

	info := &audit_util.AuditInfo{
		AppId:      subTask.AppId,
		UserId:     subTask.TenantId,
		BizScene:   audit_processor.AuditSceneKeyCommentaryText,
		DataFormat: audit_util.AuditDataFormatText,
		Text:       auditText,
	}

	err = audit_processor.GetAuditProcessor().ProcessAudit(ctx, info)
	errMsg := "审核异常"
	auditStatus := consts.AuditStatusError

	if audit_util.IsAuditRefused(err) {
		errMsg = "审核不通过"
		auditStatus = consts.AuditStatusRejected
		// 设置子任务审核失败
		err := impl.NewCommentarySubTaskRepoImpl().UpdateSubTaskFields(ctx, subTask.Id, map[string]interface{}{
			"audit_status": int(auditStatus),
			"audit_reason": errMsg + err.Error(),
			"merge_status": int(consts.MergeStatusFailed),
		})
		if err != nil {
			g.Log().Error(ctx, "UpdateSubTaskFields err:%v", err)
			// 修改状态
			activities.UpdateSubTaskMergeStatusFailed(ctx, "SubtitleAuditProcess", subTask)
			return nil, temporal.NewNonRetryableApplicationError("UpdateSubTaskFields err", "db error", err)
		}
		return nil, temporal.NewNonRetryableApplicationError("SubtitleAuditRefused", "audit refused", nil)
	}
	// 非审核拒绝
	if err != nil {
		// 设置审核异常 可重试
		err := impl.NewCommentarySubTaskRepoImpl().UpdateSubTaskFields(ctx, subTask.Id, map[string]interface{}{
			"audit_status": int(auditStatus),
			"audit_reason": errMsg,
			"merge_status": int(consts.MergeStatusFailed),
		})
		if err != nil {
			g.Log().Error(ctx, "UpdateSubTaskFields err:%v", err)
			activities.UpdateSubTaskMergeStatusFailed(ctx, "SubtitleAuditProcess", subTask)
			return nil, temporal.NewNonRetryableApplicationError("UpdateSubTaskFields err", "db error", err)
		}
		return nil, temporal.NewNonRetryableApplicationError("SubtitleAuditError", "audit error", err)
	}
	// 设置任务状态 审核通过
	subTask.AuditStatus = consts.AuditStatusSuccess
	err = impl.NewCommentarySubTaskRepoImpl().UpdateSubTaskFields(ctx, subTask.Id, map[string]interface{}{
		"audit_status": int(consts.AuditStatusSuccess),
	})
	if err != nil {
		g.Log().Error(ctx, "UpdateSubTaskFields err:%v", err)
		activities.UpdateSubTaskMergeStatusFailed(ctx, "SubtitleAuditProcess", subTask)
		return nil, temporal.NewNonRetryableApplicationError("UpdateSubTaskFields err", "db error", err)
	}
	g.Log().Error(ctx, "audit err:%v", err)
	return subTask, nil
}

func subtitleTranslate(ctx context.Context, fromText []string, subTask *bo.CommentarySubTaskBO) (string, error) {
	// 获取快照字幕

	text := strings.Join(fromText, "")
	// 非中文需要翻译成中文进行审核
	res := &translate.TranslateRes{}
	err := retry.Do(func() error {
		var reqErr error
		res, reqErr = translate.TranslateFullText(ctx, subTask.Id, subTask.SourceLangId, string(consts.LanguageZh), fromText)
		return reqErr
	},
		retry.Attempts(3),
		retry.Delay(time.Second),
		retry.MaxDelay(30*time.Second),
		retry.DelayType(retry.BackOffDelay)) // 指数退避
	if err != nil {
		g.Log().Errorf(ctx, "failed to TranslateFullText, fromLang:%s fromText:%s error:%v", subTask.SourceLangId, fromText, err)
		return "", err
	}
	text = ""
	for _, subtitle := range res.Data.TransJsonResult.Subtitles {
		text += subtitle.Text
	}

	return text, nil
}
