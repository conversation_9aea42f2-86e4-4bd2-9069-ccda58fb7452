package commentary

import (
	"business-workflow/internal/activities"
	"business-workflow/internal/common/config"
	"business-workflow/internal/common/db"
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/common"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/repo/commentary_repo"
	impl2 "business-workflow/internal/repo/commentary_repo/impl"
	enginetaskrepo "business-workflow/internal/repo/engine_task_repo"
	"business-workflow/internal/repo/engine_task_repo/impl"
	"business-workflow/internal/util"
	"business-workflow/internal/util/ffmpeg"
	"business-workflow/internal/util/id_generator"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	pb "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/utils"
	"gorm.io/gorm"

	"github.com/gogf/gf/v2/frame/g"
	"go.temporal.io/sdk/temporal"
)

type CommentaryActivity struct {
	commentarySubTaskRepo      commentary_repo.ICommentarySubTaskRepo
	commentarySourceDetailRepo commentary_repo.ICommentarySourceDetailRepo
	engineTaskRepo             enginetaskrepo.IEngineTaskRepo
	commentarySubtitleItemRepo commentary_repo.ICommentarySubtitleItemRepo
	mainTaskRepo               commentary_repo.ICommentaryMainTaskRepo
}

func (a *CommentaryActivity) IsSkip(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) bool {
	// 防重处理
	if subTask.CommentaryAgentRes != nil && subTask.CommentaryAgentRes.Result != nil && len(subTask.CommentaryAgentRes.Result.TimeFrameRange) > 0 {
		g.Log().Infof(ctx, "commentary already done, skip:%v", subTask.Id)
		return true
	}
	return false
}
func (a *CommentaryActivity) WaitCondition(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) error {

	return nil
}

func (a *CommentaryActivity) HandleSubmit(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) ([]int64, error) {

	mainTask, err := a.mainTaskRepo.GetTaskById(ctx, task.Id)
	if err != nil {
		return nil, temporal.NewNonRetryableApplicationError(
			"GetTaskById err",
			"db error",
			err)
	}

	req := &CreateCommentaryQiFeiReq{
		Urls:           mainTask.CompressVideoUrl,
		OriginLanguage: "中文",
		Timeline:       genTimeLine(task.TargetDurationRangeType),
	}
	// 获取时长
	duration, err := ffmpeg.ProbeDuration(mainTask.CompressVideoUrl)
	if err != nil {
		g.Log().Errorf(ctx, "ProbeDuration failed, err: %v", err)
		return nil, temporal.NewNonRetryableApplicationError(
			"ProbeDuration err",
			"ProbeDuration err",
			err)
	}
	totalCost := duration

	businessScenariosType := pb.BusinessScenariosType_BUSINESS_VIDEO_COMMENTARY_HIGHLIGHT_COMMENTARY
	if task.TaskType == consts.TaskTypeHighlight {
		businessScenariosType = pb.BusinessScenariosType_BUSINESS_VIDEO_COMMENTARY_HIGHLIGHT_CLIP
	}
	curTaskInfo := &pb.TaskInfo{
		BizType:               pb.VoiceBusinessType_VOICE_BUSINESS_TYPE_VIDEO_COMMENTARY,
		BusinessScenariosType: int32(businessScenariosType),
		CommonInputContent:    utils.ToJson(req),
		TaskType:              pb.TaskType_VIDEO_COMMENTARY_QIFEI,
		TaskCost:              totalCost,
		RefererId:             task.Id,
	}
	// taskBase := &pb.TaskBase{
	// 	AppId:    task.AppId,
	// 	TenantId: task.TenantId,
	// 	Priority: 0,
	// 	Apikey:   task.ApiKey,
	// 	Source:   int64(pb.TaskSource_INTERNAL_SOURCE),
	// 	ExtraMap: map[string]string{
	// 		consts.BaseTaskCost:                 fmt.Sprintf("%v", totalCost),
	// 		consts.BaseTaskCommentrayMainTaskID: strconv.FormatInt(task.Id, 10),
	// 		consts.BaseTaskCommentraySubTaskID:  strconv.FormatInt(subTask.Id, 10),
	// 		consts.BaseTaskCommentrayBizMode:    strconv.FormatInt(int64(task.BizMode), 10),
	// 		consts.BaseTaskCommentrayTaskType:   strconv.FormatInt(int64(task.TaskType), 10),
	// 	},
	// }
	taskBase := conv.CommentaryMainTaskBOToBaseTask(task, subTask, int64(pb.TaskSource_INTERNAL_SOURCE),
		map[string]string{
			consts.BaseTaskCost: fmt.Sprintf("%v", totalCost),
		})
	// 构建单个任务请求
	createReq := &pb.CreateTaskReq{
		TaskBase: taskBase,
		TaskList: []*pb.TaskInfo{curTaskInfo},
	}

	// 创建任务
	g.Log().Infof(ctx, "creating commentary  qifei agent  task req:%v", createReq)
	createRes, err := omni_engine.GetOmniEngineClient().CreateTask(ctx, createReq)
	if err != nil {
		g.Log().Errorf(ctx, "create commentary qifei agent  task err:%v", err)
		return nil, temporal.NewApplicationError(
			"create commentary qifei agent  task err",
			"create commentary qifei agent  task err",
			err)
	}

	// 提交后入engine任务表
	handoutTime := time.Now().Add(time.Duration(config.GetConfig().Temporal.ScheduleToCloseTimeout) / 2 * time.Minute)
	engineTask := &do.BusinessEngineTask{
		AppId:         task.AppId,
		TenantID:      task.TenantId,
		MainTaskId:    task.Id,
		Bid:           subTask.Id,
		TaskType:      int(curTaskInfo.TaskType),
		EngineTaskId:  createRes.TaskIds[0],
		EngineStatus:  consts.EngineWorkflowStatusInit,
		HandleTimeout: &handoutTime,
	}
	err = a.engineTaskRepo.CreateWorkflowEngineTask(ctx, engineTask)
	if err != nil {
		return nil, temporal.NewNonRetryableApplicationError(
			"db error",
			"CreateWorkflowEngineTask error",
			err)
	}
	return []int64{engineTask.EngineTaskId}, nil
}

func (a *CommentaryActivity) HandleResult(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, result []*do.BusinessEngineTask) error {
	one := result[0]
	detail, err := one.TaskInfoConvertPB()
	if err != nil {
		g.Log().Errorf(ctx, "TaskInfoConvertPB err:%v", err)
		return temporal.NewNonRetryableApplicationError("TaskInfoConvertPB err", "json err", err)
	}

	switch detail.GetStatus() {
	case pb.TaskStatus_COMPLETED:
		return a.handleSuccess(ctx, task, subTask, detail)
	case pb.TaskStatus_FAILED, pb.TaskStatus_QUEUING_TIMEOUT:
		return a.handleFailure(ctx, task, subTask, detail)
	}

	return nil
}

func (a *CommentaryActivity) handleSuccess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, detail *pb.Task) error {
	// 反序列化响应结果
	resp := &common.QueryCommentaryQiFeiResp{}
	err := json.Unmarshal([]byte(detail.CommonOutputContent), resp)
	if err != nil {
		g.Log().Errorf(ctx, "unmarshal commentary result error:%v", err)
		return temporal.NewNonRetryableApplicationError("parse error", "unmarshal commentary result error", err)
	}
	if resp.Data.Result == nil || len(resp.Data.Result.TimeFrameRange) == 0 {
		g.Log().Errorf(ctx, "commentary result is empty")
		return temporal.NewNonRetryableApplicationError("commentary result is empty", "commentary result is empty", nil)
	}

	//  还有前置处理  不是直接用结果 要转换
	// 生成字幕项
	subtitleItems, err := a.generateSubtitleItems(ctx, task, subTask, resp.Data.Result)
	if err != nil {
		g.Log().Errorf(ctx, "generateSubtitleItems error:%v", err)
		return temporal.NewNonRetryableApplicationError("generateSubtitleItems error", "generateSubtitleItems error", err)
	}
	err = db.GetDB().Transaction(func(tx *gorm.DB) error {
		// 批量创建字幕项
		err = a.commentarySubtitleItemRepo.CreateSubtitleItems(ctx, subtitleItems, tx)
		if err != nil {
			g.Log().Errorf(ctx, "CreateSubtitleItems error:%v", err)
			return temporal.NewNonRetryableApplicationError("db error", "CreateSubtitleItems error", err)
		}

		err = a.commentarySubTaskRepo.UpdateSubTaskCommentaryAgentRes(ctx, subTask.Id, resp.Data, tx)
		if err != nil {
			g.Log().Errorf(ctx, "UpdateSubTaskFields error:%v", err)
			return temporal.NewNonRetryableApplicationError("db error", "UpdateSubTaskFields error", err)
		}
		g.Log().Infof(ctx, "commentary success, created %d subtitle items", len(subtitleItems))
		return nil
	})

	return nil
}

func (a *CommentaryActivity) handleFailure(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, detail *pb.Task) error {
	g.Log().Errorf(ctx, "commentary failed:%v", detail.GetStatus())

	// 更新子任务状态为失败
	err := a.commentarySubTaskRepo.UpdateSubTaskStatus(ctx, subTask.Id, int(consts.SubTaskStatusFailed), "commentary qifei failed: "+detail.GetStatus().String())
	if err != nil {
		g.Log().Errorf(ctx, "UpdateSubTaskStatus error:%v", err)
		return temporal.NewNonRetryableApplicationError("db error", "UpdateSubTaskStatus error", err)
	}

	return temporal.NewNonRetryableApplicationError("commentary failed", "commentary qifei failed", nil)
}

func (a *CommentaryActivity) generateSubtitleItems(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, result *common.CommentaryQiFeiResult) ([]*do.CommentarySubtitleItem, error) {
	subtitleItems := make([]*do.CommentarySubtitleItem, 0, len(result.TimeFrameRange))

	var total int64

	for i, timeFrame := range result.TimeFrameRange {
		id, err := id_generator.GenerateId()
		if err != nil {
			g.Log().Errorf(ctx, "GenerateId error:%v", err)
			return nil, err
		}
		// 计算原文相对时间戳
		startTimeStr, err := util.SrtTimeParseAndFormat(timeFrame.StartTimestamp)
		if err != nil {
			g.Log().Errorf(ctx, "ParseAndFormat err:%v", err)
			return nil, err
		}
		endTimeStr, err := util.SrtTimeParseAndFormat(timeFrame.EndTimestamp)
		if err != nil {
			g.Log().Errorf(ctx, "ParseAndFormat err:%v", err)
			return nil, err
		}

		startTime, err := util.SrtStringTime2Ms(startTimeStr)
		if err != nil {
			g.Log().Errorf(ctx, "ParseAndFormat err:%v", err)
			return nil, err
		}
		endTime, err := util.SrtStringTime2Ms(endTimeStr)
		if err != nil {
			g.Log().Errorf(ctx, "ParseAndFormat err:%v", err)
			return nil, err
		}

		diff := endTime - startTime

		newStart := total
		newEnd := total + diff
		total = newEnd

		// 转回 SRT 格式字符串
		originSubtitleStartStr := util.SrtTimeMs2String(newStart)

		originSubtitleEndStr := util.SrtTimeMs2String(newEnd)

		subtitleItem := &do.CommentarySubtitleItem{
			Id:                         id,
			ItemIdx:                    int32(i + 1),
			SubTaskId:                  subTask.Id,
			MainTaskId:                 task.Id,
			OriginSubtitle:             timeFrame.Context,
			TargetSubtitle:             timeFrame.Context,
			SourceLangId:               "zh",
			TargetLangId:               task.TargetLangId,
			OriginSubtitleStartStr:     originSubtitleStartStr,
			OriginSubtitleEndStr:       originSubtitleEndStr,
			SubtitleStartStr:           originSubtitleStartStr,
			SubtitleEndStr:             originSubtitleEndStr,
			ClipOriginSubtitleStartStr: startTimeStr,
			ClipOriginSubtitleEndStr:   endTimeStr,
			ClipSubtitleStartStr:       startTimeStr, // 先给默认值 后面 tts 节点会调整
			ClipSubtitleEndStr:         endTimeStr,
			GenerateVoiceStatus:        consts.SubtitleItemRegenerateStatusNone,
			TextTranslateStatus:        consts.SubtitleItemReTranslateStatusNone,
		}
		subtitleItems = append(subtitleItems, subtitleItem)
	}

	return subtitleItems, nil
}
func (a *CommentaryActivity) HandleFailed(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, aerr *temporal.ApplicationError) error {
	//更新子任务状态为失败
	err := a.commentarySubTaskRepo.UpdateSubTaskStatus(ctx, subTask.Id, int(consts.SubTaskStatusFailed), "commentary qifei failed: ")
	if err != nil {
		g.Log().Errorf(ctx, "UpdateSubTaskStatus error:%v", err)
		return temporal.NewNonRetryableApplicationError("db error", "UpdateSubTaskStatus error", err)
	}
	return nil
}
func CommentaryProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, activities.GetRequestId(ctx))
	g.Log().Infof(ctx, "CommentaryProcess task:%+v, sub_task:%+v", task, subTask)
	_, err := activities.UpdateSubTaskProcessing(ctx, task, subTask)
	if err != nil {
		return nil, err
	}
	a := &activities.Processor{}
	return a.Process(ctx, &CommentaryActivity{
		commentarySubTaskRepo:      impl2.NewCommentarySubTaskRepoImpl(),
		commentarySourceDetailRepo: impl2.NewCommentarySourceDetailRepoImpl(),
		engineTaskRepo:             impl.NewEngineTaskRepoImpl(),
		commentarySubtitleItemRepo: impl2.NewCommentarySubtitleItemRepoImpl(),
		mainTaskRepo:               impl2.NewCommentaryMainTaskRepoImpl(),
	}, task, subTask)
}

// 入参
type CreateCommentaryQiFeiReq struct {
	Urls           string `json:"urls"`
	OriginLanguage string `json:"origin_language"` // 原语言，现在仅支持 zh
	Timeline       int    `json:"timeline"`        // 生成的解说长度预期值，单位秒
}

func genTimeLine(rangeType consts.TargetDurationRangeType) int {
	////1min以内默认按 30s左右1-3min：120s左右3-5min：240s左右5-10min：450s左右
	switch rangeType {
	case consts.TargetDurationRangeType1Minute:
		return 30
	case consts.TargetDurationRangeType1To3Minute:
		return 120
	case consts.TargetDurationRangeType3To5Minute:
		return 240
	case consts.TargetDurationRangeType5To10Minute:
		return 450
	}
	return 30
}
