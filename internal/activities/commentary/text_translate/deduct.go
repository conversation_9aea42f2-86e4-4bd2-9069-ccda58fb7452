package texttranslate

import (
	"business-workflow/internal/activities"
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/do"
	commentary_repo_impl "business-workflow/internal/repo/commentary_repo/impl"
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	omni_balance "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_balance"
	pb "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
	omni_tenant "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_tenant"
)

// 处理二次编辑翻译扣费逻辑
func TextTranslateDeductProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, subtitleItemId int64) (*string, error) {
	ctx = context.WithValue(ctx, trace.ReqId, activities.GetRequestId(ctx))
	payOrderId, err := ProcessTextTranslateDeduct(ctx, task, subTask, subtitleItemId)
	if activities.IsNonRetryableError(err) {
		g.Log().Errorf(ctx, "TextTranslateDeductProcess failed, err: %v", err)
		err := commentary_repo_impl.NewCommentarySubtitleItemRepoImpl().UpdateTextTranslateStatus(ctx, subtitleItemId, consts.SubtitleItemReTranslateStatusFailed)
		if err != nil {
			g.Log().Errorf(ctx, "handle success error:%v", err)
			return nil, err
		}
		return nil, err
	}
	return payOrderId, nil
}
func ProcessTextTranslateDeduct(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, subtitleItemId int64) (*string, error) {
	g.Log().Info(ctx, "TextTranslateDeductProcess Process, task: %+v", task)
	// 1. 参数验证
	if task == nil || subTask == nil {
		return nil, fmt.Errorf("task or subTask cannot be nil")
	}

	// 2. 通过subtitleItemId获取字幕项详情
	subtitleItemRepo := commentary_repo_impl.NewCommentarySubtitleItemRepoImpl()
	subtitleItem, err := subtitleItemRepo.GetSubtitleItemBySubtitleItemId(ctx, subtitleItemId)
	if err != nil {
		g.Log().Errorf(ctx, "[ProcessTextTranslateDeduct] failed to get subtitle item by id %d: %v", subtitleItemId, err)
		return nil, fmt.Errorf("failed to get subtitle item: %w", err)
	}
	if subtitleItem == nil {
		g.Log().Errorf(ctx, "[ProcessTextTranslateDeduct] subtitle item not found for id %d", subtitleItemId)
		return nil, fmt.Errorf("subtitle item not found for id %d", subtitleItemId)
	}
	originSubtitleLen := len([]rune(subtitleItem.OriginSubtitle))
	g.Log().Infof(ctx, "[ProcessTextTranslateDeduct] subtitle item details: id=%d, content=%s, start_time=%s, end_time=%s, OriginSubtitle=%d",
		subtitleItem.Id, subtitleItem.OriginSubtitle, subtitleItem.OriginSubtitleStartStr, subtitleItem.OriginSubtitleEndStr, originSubtitleLen)

	// 2. 通过omni-tenant服务获取apikey_id
	tenantClient := omni_engine.GetOmniTenantClient()
	tenantReq := &omni_tenant.GetTenantApikeyByApikeyReq{
		Apikey: task.ApiKey,
	}
	tenantResp, err := tenantClient.GetTenantApikeyByApikey(ctx, tenantReq)
	if err != nil {
		g.Log().Errorf(ctx, "[TextTranslateDeductProcess] failed to get tenant apikey info: %v apiKey:%v", err, task.ApiKey)
		return nil, fmt.Errorf("failed to get tenant apikey info: %w", err)
	}

	if tenantResp.TenantApikeyInfo == nil {
		g.Log().Errorf(ctx, "[TextTranslateDeductProcess] tenant apikey info is empty")
		return nil, fmt.Errorf("tenant apikey info is empty")
	}

	apikeyId := tenantResp.TenantApikeyInfo.ApikeyId

	// 3. 构建扣款请求
	// 根据任务类型确定业务场景类型
	businessScenariosType := pb.BusinessScenariosType_BUSINESS_TEXT_TRANSLATE_SERVICE
	avoiceBusinessType := pb.VoiceBusinessType_VOICE_BUSINESS_TYPE_VIDEO_COMMENTARY_EDIT_PAGE
	if task.TaskType == consts.TaskTypeCommentary {
		avoiceBusinessType = pb.VoiceBusinessType_VOICE_BUSINESS_TYPE_VIDEO_COMMENTARY_EDIT
	}

	// 构建积分消耗列表 - 文本翻译任务按字符数计费
	// 使用字幕内容长度作为计费基础
	now := time.Now()
	exOrderId := fmt.Sprintf("text_translate_task_%d_%d", now.Unix(), subtitleItemId)

	creditsCostList := []*omni_balance.AVoiceCreditsCost{
		{
			Quantity:  int64(originSubtitleLen), // 使用字幕内容长度作为用量
			ExOrderId: exOrderId,
		},
	}

	// 记录编辑操作流水
	editLogRepo := commentary_repo_impl.NewCommentaryEditOperationLogRepoImpl()
	editLog := &do.CommentaryEditOperationLog{
		MainTaskId:    task.Id,
		SubTaskId:     subTask.Id,
		SubTaskName:   subTask.Name,
		BizMode:       int(task.BizMode),
		DeductOrderId: exOrderId,
		OperationType: "TEXT_TRANSLATE_DEDUCT",
		OperationDesc: fmt.Sprintf("文本翻译扣费操作，视频时长: %.2f秒", subTask.VideoDuration),
	}
	if err := editLogRepo.CreateEditOperationLog(ctx, editLog); err != nil {
		g.Log().Errorf(ctx, "[TextTranslateDeductProcess] 记录编辑操作流水失败: %v", err)
		// 记录流水失败不影响主流程，只记录错误日志
	}

	// 构建扣款请求
	deductionReq := &omni_balance.AVoiceVideoEditDeductionCreditsRequest{
		AppId:               task.AppId,
		TenantId:            task.TenantId,
		ApikeyId:            apikeyId,
		CreditsCostList:     creditsCostList,
		OpenapiBusinessType: int32(businessScenariosType),
		AvoiceBusinessType:  int32(avoiceBusinessType),
		SourceTag:           0,                                                           // 默认来源标签
		DubbingId:           subTask.Id,                                                  // 使用主任务ID作为dubbing_id
		ApiSourcePath:       "/commentary/text_translate/deduct",                         // API来源路径
		ApiSourceEnum:       int32(omni_balance.ApiSourceEnum_API_SOURCE_TEXT_TRANSLATE), // 文本翻译服务
	}

	// 4. 调用omni-balance服务进行扣款
	balanceClient := omni_engine.GetOmniBalanceAVLBalance()
	if balanceClient == nil {
		g.Log().Errorf(ctx, "[TextTranslateDeductProcess] balance client is nil")
		return nil, fmt.Errorf("balance client is not initialized")
	}

	_, err = balanceClient.AVoiceVideoEditDeductionCredits(ctx, deductionReq)
	if err != nil {
		g.Log().Errorf(ctx, "[TextTranslateDeductProcess] text translate deduction failed: %v, request: %+v", err, deductionReq)
		return nil, fmt.Errorf("text translate deduction failed: %w", err)
	}

	g.Log().Infof(ctx, "[TextTranslateDeductProcess] text translate deduction successful, main_task_id: %d, sub_task_id: %d, apikey_id: %d, quantity: %d",
		task.Id, subTask.Id, apikeyId, int64(subTask.VideoDuration))
	return &exOrderId, nil
}
