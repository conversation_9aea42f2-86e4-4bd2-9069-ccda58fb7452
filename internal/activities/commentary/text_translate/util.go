package texttranslate

import (
	"business-workflow/internal/entity/do"
	commentary_repo_impl "business-workflow/internal/repo/commentary_repo/impl"
	"context"

	"go.temporal.io/sdk/temporal"
)

func getSubtitleItem(ctx context.Context, subtitleItemId int64) (*do.CommentarySubtitleItem, error) {
	if subtitleItemId <= 0 {
		//直出不用扣费
		return nil, nil
	}
	commentarySubtitleitemRepo := commentary_repo_impl.NewCommentarySubtitleItemRepoImpl()
	subtitleItem, err := commentarySubtitleitemRepo.GetSubtitleItemById(ctx, subtitleItemId)
	if err != nil {
		return nil, temporal.NewNonRetryableApplicationError(
			"GetSubtitleItemById error",
			"db error",
			err)
	}

	return subtitleItem, nil
}
