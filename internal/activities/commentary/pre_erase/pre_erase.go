package preerase

import (
	"business-workflow/internal/activities"
	"business-workflow/internal/common"
	"business-workflow/internal/common/config"
	"business-workflow/internal/common/erase"
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/processor/processor_util"
	"business-workflow/internal/repo/engine_task_repo/impl"
	"business-workflow/internal/repo/inpainting"
	impaintImpl "business-workflow/internal/repo/inpainting/impl"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	omniProto "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
	"google.golang.org/grpc"

	"github.com/gogf/gf/v2/frame/g"
	"go.temporal.io/sdk/temporal"
)

// 确保在init时自动注册

type PreEraseActivity struct {
	ocrDb inpainting.IInpaintingOcrRepo
}

var (
	noRetryErr = temporal.NewNonRetryableApplicationError(
		"defaultPreEraseNoRetryError",
		"InvalidOrderError",
		nil)

	retryErr = temporal.NewApplicationError( // 需要重试的错误
		"defaultPreEraseRetryError",
		"InvalidOrderError",
		nil)
)

func (a *PreEraseActivity) IsSkip(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) bool {
	if task.EraseMode == consts.EraseModeOff {
		return true
	}
	ocrTask, err := impaintImpl.NewInpaintingOcrTaskImpl().GetOcrTaskBySubId(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "PreEraseActivity skip, subTaskId: %v", subTask.Id)
		return true
	}
	if ocrTask.Status == consts.InpaintingStatusPreprocessComplete ||
		ocrTask.Status == consts.InpaintingStatusPostprocrssComplete ||
		ocrTask.Status == consts.InpaintingStatusEraseComplete {
		return true
	}
	return false
}
func (a *PreEraseActivity) WaitCondition(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) error {
	return nil
}

func (a *PreEraseActivity) buildPreprocessReq(ctx context.Context, ocrTask *do.WorkflowOcrTask) (*erase.PreprocessSubmitReq, error) {
	req := &erase.PreprocessSubmitReq{}
	req.Id = ocrTask.Bid
	objectName, err := processor_util.GetObjectNameByHttpsUrl(ocrTask.VideoUrl)
	if err != nil {
		g.Log().Errorf(ctx, "PreEraseActivity buildPreprocessReq, GetObjectNameByHttpsUrl failed, err: %v", err)
		return nil, err
	}
	req.VideoUrl = objectName
	req.StartMSecond = -1
	req.EndMSecond = -1
	req.FullScreen = ocrTask.FullScreen
	req.TaskType = "video_inpaint"
	bboxesData, err := processor_util.StringDecompressBytes(ocrTask.BboxsCompress)
	if err != nil {
		g.Log().Errorf(ctx, "PreEraseActivity buildPreprocessReq, StringDecompressBytes failed, err: %v", err)
		return nil, err
	}
	err = json.Unmarshal(bboxesData, &req.BboxesFile)
	if err != nil {
		g.Log().Errorf(ctx, "PreEraseActivity buildPreprocessReq, Unmarshal failed, err: %v", err)
		return nil, err
	}
	req.InpaintMasks = make([]erase.EraseArea, 0)
	rect := common.Rectangle{}
	err = json.Unmarshal([]byte(ocrTask.OcrRectInfo), &rect)
	if err != nil {
		g.Log().Errorf(ctx, "PreEraseActivity buildPreprocessReq, Unmarshal OcrRectInfo failed, err: %v", err)
	}
	if len(rect.TopLeft) == 2 && len(rect.BottomRight) == 2 {
		// 到这一步，OcrRectInfo总是有效的，但只有用户指定区域时，才需要填下面的信息
		if ocrTask.UserRect == 1 {
			area := erase.EraseArea{}
			area.ActionType = "keep" // keep代表要擦除
			area.Start = 0
			area.End = ocrTask.VideoDuration + 1
			area.Region = []float64{rect.TopLeft[0], rect.TopLeft[1], rect.BottomRight[0], rect.BottomRight[1]}
			req.InpaintMasks = append(req.InpaintMasks, area)
		}
	}
	return req, nil
}

func (a *PreEraseActivity) submitToEngine(ctx context.Context, task *bo.CommentaryMainTaskBO,
	subTask *bo.CommentarySubTaskBO, ocrTask *do.WorkflowOcrTask) (*do.BusinessEngineTask, error) {
	req, err := a.buildPreprocessReq(ctx, ocrTask)
	if err != nil {
		g.Log().Errorf(ctx, "PreEraseActivity submitToEngine, buildPreprocessReq failed, err: %v", err)
		return nil, noRetryErr
	}
	// reqBase := &omniProto.TaskBase{
	// 	ExtraMap: map[string]string{
	// 		// "task_cost": fmt.Sprintf("%d", int(subTask.VideoDuration)),
	// 		consts.BaseTaskCost:                 fmt.Sprintf("%v", subTask.VideoDuration),
	// 		consts.BaseTaskCommentrayMainTaskID: strconv.FormatInt(task.Id, 10),
	// 		consts.BaseTaskCommentraySubTaskID:  strconv.FormatInt(subTask.Id, 10),
	// 		consts.BaseTaskCommentrayBizMode:    strconv.FormatInt(int64(task.BizMode), 10),
	// 		consts.BaseTaskCommentrayTaskType:   strconv.FormatInt(int64(task.TaskType), 10),
	// 	},
	// }
	reqBase := conv.CommentaryMainTaskBOToBaseTask(task, subTask, int64(omniProto.TaskSource_INTERNAL_SOURCE),
		map[string]string{
			consts.BaseTaskCost: fmt.Sprintf("%v", subTask.VideoDuration),
		})

	taskInfo := &omniProto.TaskInfo{}
	taskInfo.Name = fmt.Sprintf("commentary_preprocess_%v", ocrTask.Bid)
	taskInfo.TaskType = omniProto.TaskType_VIDEO_TRANSLATION_PREPROCESS
	taskInfo.Quantity = 1
	taskInfo.RefererId = task.Id
	if ocrTask.EraseEdition == consts.EraseEditionProfessional {
		taskInfo.TaskType = omniProto.TaskType_VIDEO_TRANSLATION_PREPROCESS_PRO
		taskInfo.Name = fmt.Sprintf("commentary_preprocess_pro_%v", ocrTask.Bid)
	}

	data, err := json.Marshal(req)
	if err != nil {
		g.Log().Error(ctx, "json.Marshal failed, err: %v", err)
		return nil, err
	}
	taskInfo.CommonInputContent = string(data)

	createReq := &omniProto.CreateTaskReq{
		TaskBase: reqBase,
		TaskList: []*omniProto.TaskInfo{taskInfo},
	}

	createRes, err := omni_engine.GetOmniEngineClient().CreateTask(ctx, createReq, grpc.MaxCallSendMsgSize(100*1024*1024))
	if err != nil {
		g.Log().Error(ctx, "CreateTask PreEraseActivity failed, err: %v", err)
		return nil, retryErr
	}
	if len(createRes.TaskIds) != 1 {
		g.Log().Error(ctx, "CreateTask PreEraseActivity failed, len is not 1: %v", len(createRes.TaskIds))
		return nil, retryErr
	}
	ocrTask.PreprocessSubmitAt = time.Now()
	ocrTask.Status = consts.InpaintingStatusPreprocessSubmitted

	handoutTime := time.Now().Add(time.Duration(config.GetConfig().Temporal.ScheduleToCloseTimeout) / 2 * time.Minute)
	enginetask := &do.BusinessEngineTask{
		EngineTaskId:  createRes.TaskIds[0],
		MainTaskId:    subTask.MainTaskId,
		Bid:           ocrTask.Id,
		TenantID:      ocrTask.TenantID,
		TaskType:      int(taskInfo.TaskType),
		EngineStatus:  consts.EngineWorkflowStatusInit,
		AppId:         task.AppId,
		HandleTimeout: &handoutTime,
	}

	err = impaintImpl.NewInpaintingOcrTaskImpl().CreateEngineTasksAndUpdateOcrTask(ctx, []*do.BusinessEngineTask{enginetask}, ocrTask)
	if err != nil {
		return nil, err
	}
	return enginetask, nil
}

func (a *PreEraseActivity) HandleSubmit(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) ([]int64, error) {
	ocrTask, err := impaintImpl.NewInpaintingOcrTaskImpl().GetOcrTaskBySubId(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "PreEraseActivity HandleSubmit, GetOcrTaskBySubId failed, err: %v", err)
		return nil, err
	}
	if ocrTask.Status == consts.InpaintingStatusDelete {
		return nil, noRetryErr
	}
	engineTaskRepo := impl.NewEngineTaskRepoImpl()
	// 根据bid查找engine记录
	taskType := int(omniProto.TaskType_VIDEO_TRANSLATION_PREPROCESS)
	if ocrTask.EraseEdition == consts.EraseEditionProfessional {
		taskType = int(omniProto.TaskType_VIDEO_TRANSLATION_PREPROCESS_PRO)
	}
	engineTask, err := engineTaskRepo.GetWorkflowEngineTaskByBidAndType(ctx, ocrTask.Bid, taskType)
	if err != nil {
		g.Log().Warningf(ctx, "PreEraseActivity HandleSubmit, GetWorkflowEngineTaskByBid failed, err: %v", err)
		return nil, noRetryErr
	}
	if engineTask.Id > 0 {
		g.Log().Warningf(ctx, "PreEraseActivity HandleSubmit, engineTask is not nil: %v", engineTask)
	}
	// OCR完成状态，才应该提交
	if ocrTask.Status != consts.InpaintingStatusOcrComplete {
		if engineTask.Id > 0 && engineTask.Bid == ocrTask.Bid {
			g.Log().Warningf(ctx, "PreEraseActivity HandleSubmit, engineTask is not nil: %v", engineTask)
			return []int64{engineTask.EngineTaskId}, nil
		} else {
			g.Log().Errorf(ctx, "PreEraseActivity HandleSubmit, ocrTask.Status is not InpaintingStatusOcrComplete: %v", ocrTask.Status)
			return nil, err
		}
	}
	enginetask, err := a.submitToEngine(ctx, task, subTask, ocrTask)
	if err != nil {
		g.Log().Errorf(ctx, "PreEraseActivity HandleSubmit, submitToEngine failed, err: %v", err)
		return nil, err
	}
	return []int64{enginetask.EngineTaskId}, nil
}

func (a *PreEraseActivity) HandleResult(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, result []*do.BusinessEngineTask) error {
	// 处理成功...
	g.Log().Infof(ctx, "PreProcess HandleResult, %+v", subTask)
	if len(result) == 0 {
		g.Log().Errorf(ctx, "PreEraseActivity HandleResult, result is empty")
		return noRetryErr
	}
	engineTask := result[0]
	if engineTask.EngineStatus == consts.EngineWorkflowStatusSuccess {
		err := a.handleSuccess(ctx, task, subTask, engineTask)
		if err != nil {
			g.Log().Errorf(ctx, "PreEraseActivity handleSuccess failed, err: %v", err)
			return noRetryErr
		}
		return nil
	} else if engineTask.EngineStatus == consts.EngineWorkflowStatusFailed ||
		engineTask.EngineStatus == consts.EngineWorkflowStatusTimeout {
		err := a.handleFail(ctx, task, subTask, engineTask)
		if err != nil {
			g.Log().Errorf(ctx, "PreEraseActivity handleFail failed, err: %v", err)
		}
		return err
	} else {
		g.Log().Warningf(ctx, "PreEraseActivity HandleResult, status maybe wrong, engineStatus: %v", engineTask.EngineStatus)
		return retryErr
	}
}

func (a *PreEraseActivity) handleSuccess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, engineTask *do.BusinessEngineTask) error {
	omniTask := &omniProto.Task{}
	err := json.Unmarshal([]byte(engineTask.TaskInfo), omniTask)
	if err != nil {
		g.Log().Errorf(ctx, "PreEraseActivity, unmarshal engine task info failed, err: %v, subTaskId: %v, taskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	g.Log().Infof(ctx, "PreEraseActivity, handleSuccess, mainTaskId: %v, subTaskId: %v, engineTaskId: %v",
		task.Id, subTask.Id, engineTask.EngineTaskId)
	res := &erase.PreprocessResultRes{}
	err = json.Unmarshal([]byte(omniTask.CommonOutputContent), res)
	if err != nil {
		return fmt.Errorf("PreEraseActivity, handleSuccess 完成时返回, 解析结果失败, taskId: %v, err: %s", res.Id, err.Error())
	}
	if res.Code != erase.AiCodeOk {
		return fmt.Errorf("PreEraseActivity, handleSuccess code is not ok, code: %v", res.Code)
	}
	ocrTask, err := impaintImpl.NewInpaintingOcrTaskImpl().GetOcrTaskBySubId(ctx, subTask.Id)
	if err != nil {
		return fmt.Errorf("PreEraseActivity, handleSuccess 完成时返回, 查询预处理任务失败, bid: %v, err: %s", engineTask.Bid, err.Error())
	}
	if len(res.Chunks) < 1 {
		return a.handleZeroChunk(ctx, task, subTask, engineTask, ocrTask)
	}
	data, err := json.Marshal(res.Chunks)
	if err != nil {
		g.Log().Errorf(ctx, "PreEraseActivity, json.Marshal failed, err: %v", err)
		return err
	}
	ocrTask.ChunksCompress, err = processor_util.BytesCompress(data)
	if err != nil {
		g.Log().Errorf(ctx, "PreEraseActivity, handler.BytesCompress failed, err: %v", err)
		return err
	}
	chunkTask, err := a.createChunks(ctx, subTask, ocrTask, res.Chunks)
	if err != nil {
		g.Log().Errorf(ctx, "PreEraseActivity, createChunks failed, subTaskId: %v, taskId: %v, err: %v", subTask.Id, ocrTask.Id, err)
		return err
	}
	ocrTask.Status = consts.InpaintingStatusPreprocessComplete
	//保存新的状态并创建chunks
	return impaintImpl.NewInpaintingOcrTaskImpl().UpdateOcrAndCreateChunks(ctx, ocrTask, chunkTask)
}

func (a *PreEraseActivity) handleZeroChunk(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, engineTask *do.BusinessEngineTask, ocrTask *do.WorkflowOcrTask) error {
	g.Log().Warningf(ctx, "PreEraseActivity handleZeroChunk, mainTaskId: %v, subTaskId: %v, ocrId: %v", ocrTask.MainTaskId, ocrTask.SubTaskId, ocrTask.Id)
	return processor_util.HandleSubTaskFail(ctx, subTask, int(erase.AiCodeUnkown), "zero chunk")
}

func (a *PreEraseActivity) createChunks(ctx context.Context, subTask *bo.CommentarySubTaskBO, ocrTask *do.WorkflowOcrTask, chunks []erase.PreprocessChunk) ([]*do.WorkflowEraseChunkTask, error) {
	chunkIdMap := make(map[int32]struct{})
	isWrong := false
	errMsg := ""
	vecs := make([]*do.WorkflowEraseChunkTask, 0, len(chunks))
	for _, chunk := range chunks {
		pre := &do.WorkflowEraseChunkTask{}
		pre.Status = consts.InpaintingStatusEraseInit
		pre.OcrId = ocrTask.Id
		pre.SubTaskId = subTask.Id
		chData, err := json.Marshal(chunk)
		if err != nil {
			isWrong = true
			errMsg = "json.Marshal(chunk) failed"
			break
		}
		pre.HandleTimeout = time.Now()
		pre.Chunk = string(chData)
		pre.ChunkId = chunk.ChunkId
		pre.ChunkSize = int32(101 / len(chunks))
		if _, ok := chunkIdMap[chunk.ChunkId]; ok {
			g.Log().Errorf(ctx, "chunkId重复, orcId: %v, chunkId: %v", pre.OcrId, chunk.ChunkId)
			isWrong = true
			errMsg = fmt.Sprintf("chunkId重复, orcId: %v, chunkId: %v", pre.OcrId, chunk.ChunkId)
			break
		}
		chunkIdMap[chunk.ChunkId] = struct{}{}
		vecs = append(vecs, pre)
	}
	if isWrong {
		return nil, fmt.Errorf(errMsg)
	}
	return vecs, nil
}

func (a *PreEraseActivity) handleFail(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, engineTask *do.BusinessEngineTask) error {
	omniTask := &omniProto.Task{}
	err := json.Unmarshal([]byte(engineTask.TaskInfo), omniTask)
	if err != nil {
		g.Log().Errorf(ctx, "PreEraseActivity, unmarshal engine task info failed, err: %v, subTaskId: %v, taskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return noRetryErr
	}
	g.Log().Infof(ctx, "PreEraseActivity, handleFail, mainTaskId: %v, subTaskId: %v, engineTaskId: %v",
		task.Id, subTask.Id, engineTask.EngineTaskId)
	res := &erase.PreprocessResultRes{}
	err = json.Unmarshal([]byte(omniTask.CommonOutputContent), res)
	if err != nil {
		g.Log().Errorf(ctx, "PreEraseActivity,  handleFail json.Unmarshal failed, err: %v, subTaskId: %v, engineTaskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return noRetryErr
	}
	if res.Code == erase.AiCodeCudaOutOfMemory ||
		res.Code == erase.AiCodeTaskTimeout ||
		res.Code == erase.AiCodeTaskNotExist {
		err = a.retry(ctx, subTask)
		if err != nil {
			g.Log().Errorf(ctx, "PreEraseActivity handleFail retry failed, subTaskId: %v, err: %s", subTask.Id, err.Error())
			return noRetryErr
		}
		/// 可重试的擦除
		g.Log().Errorf(ctx, "PreEraseActivity, handleFail, 擦除前处理返回了可重试的错误。retry code: %v, msg: %v", res.Code, res.Msg)
		return retryErr
	}
	/// to do... 其他错误， 任务失败
	return processor_util.HandleSubTaskFail(ctx, subTask, int(res.Code), fmt.Sprintf("擦除前处理失败, code: %v, msg: %s", res.Code, res.Msg))
}

func (a *PreEraseActivity) retry(ctx context.Context, task *bo.CommentarySubTaskBO) error {
	subTaskDo, err := a.ocrDb.GetOcrTaskBySubId(ctx, task.Id)
	if err != nil {
		return err
	}
	subTaskDo.Status = consts.InpaintingStatusOcrComplete
	return a.ocrDb.UpdateOcrTask(ctx, subTaskDo)
}

func (a *PreEraseActivity) HandleFailed(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, aerr *temporal.ApplicationError) error {
	g.Log().Errorf(ctx, "PreEraseActivity HandleFailed, err: %v, subTask: %+v", aerr, subTask)
	_ = processor_util.HandleSubTaskFail(ctx, subTask, -1, "pre erase failed")
	return nil
}

func PreEraseProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, activities.GetRequestId(ctx))
	g.Log().Infof(ctx, "PreEraseProcess task: %v, sub_task:%+v", task, subTask)
	a := &activities.Processor{}
	return a.Process(ctx, &PreEraseActivity{
		ocrDb: impaintImpl.NewInpaintingOcrTaskImpl(),
	}, task, subTask)
}
