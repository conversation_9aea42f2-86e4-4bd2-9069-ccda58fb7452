package refund

import (
	"business-workflow/internal/activities"
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/do"
	commentary_repo_impl "business-workflow/internal/repo/commentary_repo/impl"
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	omni_balance "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_balance"
	"go.temporal.io/sdk/temporal"
)

// RefundProcess 退款处理入口函数
// 参数:
//   - ctx: 上下文
//   - task: 主任务业务对象
//   - subTask: 子任务业务对象，如果为nil则退款全部，否则只退款指定子任务
//
// 返回值:
//   - *bo.CommentaryMainTaskBO: 处理后的主任务业务对象
//   - error: 处理错误
func RefundProcess(ctx context.Context, task *bo.CommentaryMainTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, activities.GetRequestId(ctx))
	tmpTask, err := commentary_repo_impl.NewCommentaryMainTaskRepoImpl().GetTaskById(ctx, task.Id)
	if err != nil {
		return nil, fmt.Errorf("failed to get main task: %w", err)
	}
	task.PayOrderId = tmpTask.PayOrderId
	task.PayOrderStatus = tmpTask.PayOrderStatus
	task.Status = tmpTask.Status
	g.Log().Info(ctx, "[RefundProcess] task:%v", task)

	// 执行退款逻辑
	if err := executeRefund(ctx, task); err != nil {
		return handleRefundFailure(ctx, task, err)
	}

	// 退款成功，更新状态
	return handleRefundSuccess(ctx, task)
}

// checkAllSubTasksFailed 检查主任务下的所有子任务是否都失败
func checkAllSubTasksFailed(ctx context.Context, mainTaskId int64) (bool, error) {
	subTaskRepo := commentary_repo_impl.NewCommentarySubTaskRepoImpl()
	subTasks, err := subTaskRepo.GetSubTasksByMainTaskId(ctx, mainTaskId)
	if err != nil {
		return false, fmt.Errorf("failed to get sub task list: %w", err)
	}

	if len(subTasks) == 0 {
		return false, nil
	}

	// 检查是否所有子任务都失败
	for _, subTask := range subTasks {
		if subTask.Status != consts.SubTaskStatusFailed {
			return false, nil
		}
	}

	g.Log().Info(ctx, "[checkAllSubTasksFailed] all sub tasks under main task %d have failed", mainTaskId)
	return true, nil
}

// executeRefund 执行退款逻辑
func executeRefund(ctx context.Context, task *bo.CommentaryMainTaskBO) error {
	// 1. 先根据子任务状态进行退款处理
	if err := refundFailedSubTasks(ctx, task); err != nil {
		g.Log().Error(ctx, "[executeRefund] failed to refund failed sub tasks: %v", err)
		return fmt.Errorf("failed to refund failed sub tasks: %w", err)
	}

	// 2. 再检查是否所有子任务都失败，如果是则需要退款主任务
	allFailed, err := checkAllSubTasksFailed(ctx, task.Id)
	if err != nil {
		g.Log().Error(ctx, "[executeRefund] failed to check sub task status: %v", err)
		return fmt.Errorf("failed to check sub task status: %w", err)
	}

	if allFailed {
		// 所有子任务都失败，需要退款主任务
		g.Log().Info(ctx, "[executeRefund] all sub tasks failed, starting main task refund, main task ID: %d", task.Id)
		return refundMainTask(ctx, task)
	}

	g.Log().Info(ctx, "[executeRefund] partial sub tasks failed, completed failed sub task refund, main task ID: %d", task.Id)
	return nil
}

// refundMainTask 退款主任务
func refundMainTask(ctx context.Context, task *bo.CommentaryMainTaskBO) error {
	refundOrderId := fmt.Sprintf("refund_%d_%d", task.Id, time.Now().Unix())
	g.Log().Info(ctx, "[refundMainTask] 退款主任务，主任务ID: %d, 退款订单ID: %s", task.Id, refundOrderId)
	return callRefundAPI(ctx, task, refundOrderId)
}

// refundAllSubTasks 遍历退款所有子任务
func refundAllSubTasks(ctx context.Context, task *bo.CommentaryMainTaskBO) error {
	subTaskRepo := commentary_repo_impl.NewCommentarySubTaskRepoImpl()
	subTasks, err := subTaskRepo.GetSubTasksByMainTaskId(ctx, task.Id)
	if err != nil {
		g.Log().Error(ctx, "[refundAllSubTasks] failed to get sub task list: %v", err)
		return fmt.Errorf("failed to get sub task list: %w", err)
	}

	for _, subTaskItem := range subTasks {
		refundOrderId := fmt.Sprintf("refund_subTask_%d_%d", subTaskItem.Id, time.Now().Unix())
		g.Log().Info(ctx, "[refundAllSubTasks] refunding sub task, sub task ID: %d, refund order ID: %s", subTaskItem.Id, refundOrderId)

		// 使用子任务的支付订单ID进行退款
		if err := callRefundAPIWithSubTask(ctx, task, subTaskItem, refundOrderId); err != nil {
			g.Log().Error(ctx, "[refundAllSubTasks] failed to call sub task refund API, sub task ID: %d, error: %v", subTaskItem.Id, err)
			// 继续处理其他子任务，不中断整个流程
			continue
		}

		g.Log().Info(ctx, "[refundAllSubTasks] sub task refund API call successful, sub task ID: %d, refund order ID: %s", subTaskItem.Id, refundOrderId)
	}

	g.Log().Info(ctx, "[refundAllSubTasks] all sub tasks refund processing completed, main task ID: %d", task.Id)
	return nil
}

// callRefundAPI 调用退款接口的公共方法（使用主任务的支付订单ID）
func callRefundAPI(ctx context.Context, task *bo.CommentaryMainTaskBO, refundOrderId string) error {
	return callRefundAPIWithPayOrderId(ctx, task.AppId, task.TenantId, task.PayOrderId, task.PayOrderId, refundOrderId)
}

// callRefundAPIWithSubTask 使用子任务的支付订单ID调用退款接口
func callRefundAPIWithSubTask(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *do.CommentarySubTask, refundOrderId string) error {
	return callRefundAPIWithPayOrderId(ctx, task.AppId, task.TenantId, subTask.PayOrderId, task.PayOrderId, refundOrderId)
}

// callRefundAPIWithPayOrderId 通用的退款接口调用方法
func callRefundAPIWithPayOrderId(ctx context.Context, appId, tenantId int64, payOrderId, mainTaskPayOrderId, refundOrderId string) error {
	// 构建退款请求
	refundReq := &omni_balance.AVoiceRefundEventCreditsRequest{
		AppId:      appId,
		TenantId:   tenantId,
		PayOrderId: payOrderId,         // 原扣费订单ID
		MainTaskId: mainTaskPayOrderId, // 主任务支付订单ID
		OutOrderId: refundOrderId,      // 退款订单ID
	}

	// 调用omni-balance退款接口
	balanceClient := omni_engine.GetOmniBalanceAVLBalance()
	//ctx = context.WithValue(ctx, "x-qw-traffic-mark", "JetDev-T0568")
	_, err := balanceClient.AVoiceRefundEventCredits(ctx, refundReq)
	if err != nil {
		g.Log().Error(ctx, "[callRefundAPIWithPayOrderId] failed to call refund API: %v", err)
		return fmt.Errorf("failed to call refund API: %w", err)
	}

	g.Log().Info(ctx, "[callRefundAPIWithPayOrderId] refund API call successful, refund order ID: %s", refundOrderId)
	return nil
}

// handleRefundFailure 处理退款失败
func handleRefundFailure(ctx context.Context, task *bo.CommentaryMainTaskBO, err error) (*bo.CommentaryMainTaskBO, error) {
	g.Log().Error(ctx, "[handleRefundFailure] 退款失败: %v", err)

	// 退款失败时不更新支付状态，保持原状态
	// 只记录错误并返回

	return task, temporal.NewNonRetryableApplicationError("RefundError", "refund error", nil)
}

// handleRefundSuccess 处理退款成功
func handleRefundSuccess(ctx context.Context, task *bo.CommentaryMainTaskBO) (*bo.CommentaryMainTaskBO, error) {
	// 检查是否所有子任务都失败来决定退款范围
	allFailed, err := checkAllSubTasksFailed(ctx, task.Id)
	if err != nil {
		g.Log().Error(ctx, "[handleRefundSuccess] failed to check sub task status: %v", err)
		// 即使检查失败，也认为退款成功
	}

	if allFailed {
		// 全部退款成功，更新主任务和所有子任务的支付状态
		updatePayOrderStatus(ctx, task.Id, consts.PayOrderStatusRefund)
		updateSubTaskPayOrderStatus(ctx, task.Id, consts.PayOrderStatusRefund)
		g.Log().Info(ctx, "[handleRefundSuccess] full refund successful, main task ID: %d", task.Id)
	} else {
		// 部分退款成功，只更新失败子任务的支付状态
		updateFailedSubTasksPayOrderStatus(ctx, task.Id)
		g.Log().Info(ctx, "[handleRefundSuccess] partial refund successful, main task ID: %d", task.Id)
	}

	return task, nil
}

// updatePayOrderStatus 更新主任务支付状态
func updatePayOrderStatus(ctx context.Context, taskId int64, payOrderStatus consts.PayOrderStatus) {
	fields := map[string]interface{}{
		"pay_order_status": payOrderStatus,
	}
	if err := commentary_repo_impl.NewCommentaryMainTaskRepoImpl().UpdateTaskFields(ctx, taskId, fields); err != nil {
		g.Log().Error(ctx, "[updatePayOrderStatus] UpdatePayOrderStatus err:%v", err)
	}
}

// updateSubTaskPayOrderStatus 批量更新主任务下所有子任务的支付状态
func updateSubTaskPayOrderStatus(ctx context.Context, mainTaskId int64, payOrderStatus consts.PayOrderStatus) {
	// 批量更新主任务下所有子任务的支付状态
	rowsAffected, err := commentary_repo_impl.NewCommentarySubTaskRepoImpl().UpdateSubTaskPayOrderStatusByMainTaskId(ctx, mainTaskId, payOrderStatus)
	if err != nil {
		g.Log().Error(ctx, "[updateSubTaskPayOrderStatus] UpdateSubTaskPayOrderStatusByMainTaskId err:%v, mainTaskId:%d", err, mainTaskId)
		return
	}

	g.Log().Info(ctx, "[updateSubTaskPayOrderStatus] batch update sub task pay order status successful, mainTaskId:%d, payOrderStatus:%d, rowsAffected:%d", mainTaskId, payOrderStatus, rowsAffected)
}

// updateSubTaskPayOrderStatusById 更新指定子任务的支付状态
func updateSubTaskPayOrderStatusById(ctx context.Context, subTaskId int64, payOrderStatus consts.PayOrderStatus) {
	fields := map[string]interface{}{
		"pay_order_status": payOrderStatus,
	}
	if err := commentary_repo_impl.NewCommentarySubTaskRepoImpl().UpdateSubTaskFields(ctx, subTaskId, fields); err != nil {
		g.Log().Error(ctx, "[updateSubTaskPayOrderStatusById] UpdateSubTaskPayOrderStatusById err:%v, subTaskId:%d", err, subTaskId)
		return
	}

	g.Log().Info(ctx, "[updateSubTaskPayOrderStatusById] update sub task pay order status successful, subTaskId:%d, payOrderStatus:%d", subTaskId, payOrderStatus)
}

// refundFailedSubTasks 退款失败的子任务
func refundFailedSubTasks(ctx context.Context, task *bo.CommentaryMainTaskBO) error {
	// 获取所有失败的子任务
	subTasks, err := commentary_repo_impl.NewCommentarySubTaskRepoImpl().GetSubTasksByMainTaskId(ctx, task.Id)
	if err != nil {
		g.Log().Error(ctx, "[refundFailedSubTasks] failed to get sub task list: %v", err)
		return fmt.Errorf("failed to get sub task list: %w", err)
	}

	for _, subTask := range subTasks {
		if subTask.Status == consts.SubTaskStatusFailed {
			refundOrderId := fmt.Sprintf("refund_subTask_%d", subTask.Id)
			g.Log().Info(ctx, "[refundFailedSubTasks] refunding failed sub task, sub task ID: %d, refund order ID: %s", subTask.Id, refundOrderId)
			// 使用子任务的支付订单ID进行退款
			if err := callRefundAPIWithSubTask(ctx, task, subTask, refundOrderId); err != nil {
				g.Log().Error(ctx, "[refundFailedSubTasks] failed to refund failed sub task: %v", err)
				return err
			}
		}
	}

	return nil
}

// updateFailedSubTasksPayOrderStatus 更新失败子任务的支付状态
func updateFailedSubTasksPayOrderStatus(ctx context.Context, mainTaskId int64) {
	// 获取所有失败的子任务
	subTasks, err := commentary_repo_impl.NewCommentarySubTaskRepoImpl().GetSubTasksByMainTaskId(ctx, mainTaskId)
	if err != nil {
		g.Log().Error(ctx, "[updateFailedSubTasksPayOrderStatus] failed to get sub task list: %v", err)
		return
	}

	for _, subTask := range subTasks {
		if subTask.Status == consts.SubTaskStatusFailed {
			updateSubTaskPayOrderStatusById(ctx, subTask.Id, consts.PayOrderStatusRefund)
		}
	}
}

// VideoBatchSynthesisRefund 视频批量合成退款处理
// 参数:
//   - ctx: 上下文
//   - task: 主任务业务对象
//   - subTasks: 子任务业务对象列表
//
// 返回值:
//   - error: 处理错误
func VideoBatchSynthesisRefund(ctx context.Context, task *bo.CommentaryMainTaskBO, subTasks []*bo.CommentarySubTaskBO) error {
	ctx = context.WithValue(ctx, trace.ReqId, activities.GetRequestId(ctx))
	g.Log().Info(ctx, "[VideoBatchSynthesisRefund] 开始视频合成退款处理, 主任务ID: %d", task.Id)

	// 检查子任务的合成状态，对合成失败的子任务进行退款
	for _, subTask := range subTasks {
		g.Log().Info(ctx, "[VideoBatchSynthesisRefund] 检查子任务合成状态, 子任务ID: %d, 合并支付订单ID: %s", subTask.Id, subTask.MergePayOrderId)

		// 先从数据库获取子任务的最新状态，确保状态可信
		subTaskRepo := commentary_repo_impl.NewCommentarySubTaskRepoImpl()
		subTaskDO, err := subTaskRepo.GetSubTaskById(ctx, subTask.Id)
		if err != nil {
			g.Log().Error(ctx, "[VideoBatchSynthesisRefund] 获取子任务DO对象失败, 子任务ID: %d, 错误: %v", subTask.Id, err)
			continue // 继续处理其他子任务
		}

		g.Log().Info(ctx, "[VideoBatchSynthesisRefund] 从数据库获取到子任务最新合成状态, 子任务ID: %d, 合成状态: %d", subTask.Id, subTaskDO.MergeStatus)

		// 如果子任务合成失败，进行退款处理
		if subTaskDO.MergeStatus == consts.MergeStatusFailed {
			g.Log().Info(ctx, "[VideoBatchSynthesisRefund] 子任务合成失败，开始退款处理, 子任务ID: %d", subTask.Id)

			// 生成退款订单ID
			refundOrderId := fmt.Sprintf("refund_synthesis_%s", subTask.MergePayOrderId)
			subTaskDO.PayOrderId = subTask.MergePayOrderId
			// 调用退款接口
			if err := callRefundAPIWithSubTask(ctx, task, subTaskDO, refundOrderId); err != nil {
				g.Log().Error(ctx, "[VideoBatchSynthesisRefund] 子任务退款失败, 子任务ID: %d, 退款订单ID: %s, 错误: %v", subTask.Id, refundOrderId, err)
				return err
			}

			g.Log().Info(ctx, "[VideoBatchSynthesisRefund] 子任务退款成功, 子任务ID: %d, 退款订单ID: %s", subTask.Id, refundOrderId)
		} else {
			g.Log().Info(ctx, "[VideoBatchSynthesisRefund] 子任务合成状态正常，无需退款, 子任务ID: %d, 合成状态: %d", subTask.Id, subTask.MergeStatus)
		}
	}

	g.Log().Info(ctx, "[VideoBatchSynthesisRefund] 视频合成退款处理完成, 主任务ID: %d", task.Id)
	return nil
}
