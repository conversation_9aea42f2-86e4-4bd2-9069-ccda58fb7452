package concat

import (
	"business-workflow/internal/activities"
	"business-workflow/internal/common/config"
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/common"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/repo/commentary_repo"
	"business-workflow/internal/repo/commentary_repo/impl"
	engine_task_impl "business-workflow/internal/repo/engine_task_repo/impl"
	"business-workflow/internal/util"
	"business-workflow/internal/util/ffmpeg"
	"business-workflow/internal/util/hls"
	"business-workflow/internal/util/id_generator"
	"business-workflow/internal/util/obs"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/google/uuid"
	"github.com/samber/lo"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/concurrent"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/utils/acl"
	pb "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
	"gitlab.ttyuyin.com/tyr/x/utils"
	"go.temporal.io/sdk/temporal"
)

type ConcatActivity struct {
	subTaskRepo         commentary_repo.ICommentarySubTaskRepo
	noSubtitleVideoUrls []string
	finalMergeVideoUrls []string
	from                consts.WorkFlowFrom
}

func (a *ConcatActivity) IsSkip(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) bool {
	return false
}

func (a *ConcatActivity) WaitCondition(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) error {
	return nil
}

// buildIdUrlMap 构建URL到ID的映射
func (a *ConcatActivity) buildIdUrlMap() map[string]int64 {
	idUrlMap := make(map[string]int64)
	allUrls := append(a.noSubtitleVideoUrls, a.finalMergeVideoUrls...)

	for _, url := range allUrls {
		if _, ok := idUrlMap[url]; !ok {
			idUrlMap[url] = id_generator.GenerateIdNotStrict()
		}
	}
	return idUrlMap
}

// buildVideoParam 构建视频参数
func (a *ConcatActivity) buildVideoParam(urls []string, idUrlMap map[string]int64, task *bo.CommentaryMainTaskBO) *common.VideoParam {
	return &common.VideoParam{
		VideoInfos: lo.Map(urls, func(url string, _ int) *common.VideoInfo {
			id, ok := idUrlMap[url]
			if !ok {
				id = id_generator.GenerateIdNotStrict()
			}
			return &common.VideoInfo{
				ID:             id,
				OriginVideoUrl: url,
			}
		}),
		Resolution:    task.TargetResolution,
		FPS:           30,
		CRF:           23,
		Mode:          1,
		MergeVideoUrl: obs.GeneratePublicOBSUrlPubOrPri(consts.ObsModeVideoCommentary, "merge", ".mp4", "public"),
	}
}

// calculateTotalCost 计算总成本
func (a *ConcatActivity) calculateTotalCost(ctx context.Context) float64 {
	return lo.SumBy(a.noSubtitleVideoUrls, func(url string) float64 {
		duration, err := ffmpeg.ProbeDuration(url)
		if err != nil {
			g.Log().Errorf(ctx, "ProbeDuration failed, err: %v", err)
			return 0
		}
		return duration
	}) * 2 // 无字幕和有字幕的时长是一样的
}

// extractAndUploadCover 提取并上传封面
func (a *ConcatActivity) extractAndUploadCover(ctx context.Context, videoUrl string) (string, error) {
	imagePath := util.GenLocalPathWithPrefix("concat", uuid.New().String(), ".jpg")
	defer os.Remove(imagePath)
	err := ffmpeg.ExtractNonBlackFrame(obs.GetCdnUrlByObjectName(videoUrl), imagePath)
	if err != nil {
		g.Log().Errorf(ctx, "ExtractNonBlackFrame failed, err: %v", err)
		return "", err
	}

	publicObsName := obs.GeneratePublicOBSUrl(consts.ObsModeVideoCommentary, "cover", ".jpg")
	coverUrl, err := obs.GetOsClient().UploadFile(imagePath, publicObsName, acl.AclTypePublicRead)
	if err != nil {
		g.Log().Errorf(ctx, "UploadFile failed, err: %v", err)
		return "", err
	}

	return coverUrl, nil
}

// updateSubTaskToCompleted 更新子任务为完成状态
func (a *ConcatActivity) updateSubTaskToCompleted(ctx context.Context, subTaskId int64, duration float64, mergedVideoUrl, noSubtitleVideoUrl, coverUrl string) error {
	return a.subTaskRepo.UpdateSubTaskFields(ctx, subTaskId, map[string]interface{}{
		"status":                int(consts.SubTaskStatusCompleted),
		"merge_status":          int(consts.MergeStatusCompleted),
		"need_merge":            0,
		"video_duration":        duration,
		"merged_video_url":      mergedVideoUrl,
		"no_subtitle_video_url": noSubtitleVideoUrl,
		"cover_url":             coverUrl,
	})
}

func (a *ConcatActivity) HandleSubmit(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) ([]int64, error) {
	ctx = context.WithValue(ctx, trace.ReqId, task.RequestID)
	g.Log().Info(ctx, "[commentary] ConcatProcess task:%v, sub_task:%v", task, subTask)

	// 获取子任务详情
	sub, err := a.subTaskRepo.GetSubTaskById(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "GetSubTaskById failed, err: %v", err)
		return nil, temporal.NewNonRetryableApplicationError("GetSubTaskById error", "db error", err)
	}

	g.Log().Infof(ctx, "[commentary] SubTaskId:%d Found %d videos to merge: %v", sub.Id, len(a.noSubtitleVideoUrls), a.noSubtitleVideoUrls)

	// 构建ID映射和视频参数
	idUrlMap := a.buildIdUrlMap()
	noSubTitleVideoParams := a.buildVideoParam(a.noSubtitleVideoUrls, idUrlMap, task)
	finalMergeVideoParams := a.buildVideoParam(a.finalMergeVideoUrls, idUrlMap, task)

	req := &common.VideoTranscodingRequest{
		VideoParams: []*common.VideoParam{noSubTitleVideoParams, finalMergeVideoParams},
	}

	totalCost := a.calculateTotalCost(ctx)
	g.Log().Infof(ctx, "TransCodingActivity HandleSubmit req:%v", req)

	businessScenariosType := pb.BusinessScenariosType_BUSINESS_VIDEO_COMMENTARY_HIGHLIGHT_COMMENTARY
	if task.TaskType == consts.TaskTypeHighlight {
		businessScenariosType = pb.BusinessScenariosType_BUSINESS_VIDEO_COMMENTARY_HIGHLIGHT_CLIP
	}

	curTaskInfo := &pb.TaskInfo{
		BizType:               pb.VoiceBusinessType_VOICE_BUSINESS_TYPE_VIDEO_COMMENTARY,
		BusinessScenariosType: int32(businessScenariosType),
		CommonInputContent:    utils.ToJson(req),
		TaskType:              pb.TaskType_VIDEO_TRANSCODING,
		TaskCost:              totalCost,
		RefererId:             task.Id,
	}

	taskBase := conv.CommentaryMainTaskBOToBaseTask(task, subTask, int64(pb.TaskSource_INTERNAL_SOURCE),
		map[string]string{
			consts.BaseTaskCost: fmt.Sprintf("%v", totalCost),
		})

	createReq := &pb.CreateTaskReq{
		TaskBase: taskBase,
		TaskList: []*pb.TaskInfo{curTaskInfo},
	}

	// 创建任务
	g.Log().Infof(ctx, "creating concat task %v req:%v", subTask, createReq)
	createRes, err := omni_engine.GetOmniEngineClient().CreateTask(ctx, createReq)
	if err != nil {
		g.Log().Errorf(ctx, "create concat task  %verr:%v", subTask, err)
		return nil, temporal.NewNonRetryableApplicationError("create concat task err", "create concat task err", err)
	}

	// 写入业务task表
	handoutTime := time.Now().Add(time.Duration(config.GetConfig().Temporal.ScheduleToCloseTimeout) / 2 * time.Minute)
	engineTaskRepo := engine_task_impl.NewEngineTaskRepoImpl()
	engineTask := &do.BusinessEngineTask{
		AppId:         task.AppId,
		TenantID:      task.TenantId,
		MainTaskId:    task.Id,
		Bid:           subTask.Id,
		TaskType:      int(curTaskInfo.TaskType),
		EngineTaskId:  createRes.TaskIds[0],
		EngineStatus:  consts.EngineWorkflowStatusInit,
		HandleTimeout: &handoutTime,
	}

	err = engineTaskRepo.CreateWorkflowEngineTask(ctx, engineTask)
	if err != nil {
		g.Log().Errorf(ctx, "CreateWorkflowEngineTask failed, err: %v", err)
		return nil, temporal.NewNonRetryableApplicationError("db error", "GetWorkflowEngineTask error", err)
	}

	return []int64{engineTask.EngineTaskId}, nil
}
func (a *ConcatActivity) HandleResult(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, result []*do.BusinessEngineTask) error {
	g.Log().Infof(ctx, "TransCodingActivity HandleResult:%v", result)

	one := result[0]
	detail, err := one.TaskInfoConvertPB()
	if err != nil {
		g.Log().Errorf(ctx, "TaskInfoConvertPB err:%v", err)
		return err
	}

	switch detail.GetStatus() {
	case pb.TaskStatus_COMPLETED:
		return a.handleSuccess(ctx, task, subTask, detail)
	case pb.TaskStatus_FAILED, pb.TaskStatus_QUEUING_TIMEOUT:
		return a.handleFailure(ctx, task, subTask, detail)
	}

	return nil
}
func (a *ConcatActivity) HandleFailed(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, aerr *temporal.ApplicationError) error {
	updateSubTaskMergeStatusFailed(ctx, subTask, a.from)
	return temporal.NewNonRetryableApplicationError("concat error", "concat error", nil)
}

func (a *ConcatActivity) handleSuccess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, detail *pb.Task) error {
	req := &common.VideoTranscodingRequest{}
	err := json.Unmarshal([]byte(detail.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "unmarshal task_info error:%v", err)
		return temporal.NewNonRetryableApplicationError("db error", "Unmarshal error", err)
	}

	// 无字幕和最终视频参数
	noSubtitleParam := req.VideoParams[0]
	finalParam := req.VideoParams[1]

	// 获取视频时长
	duration, err := ffmpeg.ProbeDuration(obs.GetCdnUrlByObjectName(finalParam.MergeVideoUrl))
	if err != nil {
		return temporal.NewNonRetryableApplicationError("ProbeDuration error", "ffmpeg error", err)
	}

	// 提取并上传封面
	coverUrl, err := a.extractAndUploadCover(ctx, obs.GetCdnUrlByObjectName(finalParam.MergeVideoUrl))
	if err != nil {
		g.Log().Errorf(ctx, "extractAndUploadCover failed, err: %v", err)
		// 封面上传失败不影响主流程，继续执行
	}

	// 更新子任务状态
	err = a.updateSubTaskToCompleted(ctx, subTask.Id, duration,
		obs.GetCdnUrlByObjectName(finalParam.MergeVideoUrl),
		obs.GetCdnUrlByObjectName(noSubtitleParam.MergeVideoUrl),
		coverUrl)
	if err != nil {
		return temporal.NewNonRetryableApplicationError("UpdateTaskFields error", "db error", err)
	}
	concurrent.GoSafe(func() {
		err := hls.SendHlsRequest(context.Background(), []string{fmt.Sprintf("%d", subTask.Id)})
		if err != nil {
			g.Log().Errorf(ctx, "SendHlsRequest failed, err: %v", err)
			return
		}
	})
	g.Log().Infof(ctx, "concat success")
	return nil
}

func (a *ConcatActivity) parseTranscodingResponse(ctx context.Context, detail *pb.Task) (*common.VideoTranscodingResponse, error) {
	resp := &common.VideoTranscodingResponse{}
	err := json.Unmarshal([]byte(detail.CommonOutputContent), resp)
	if err != nil {
		g.Log().Errorf(ctx, "unmarshal task_info error:%v", err)
		return nil, temporal.NewNonRetryableApplicationError("db error", "Unmarshal error", err)
	}
	return resp, nil
}

func (a *ConcatActivity) handleFailure(ctx context.Context, task *bo.CommentaryMainTaskBO, sub *bo.CommentarySubTaskBO, detail *pb.Task) error {
	g.Log().Errorf(ctx, "ConcatActivity UpdateSubTaskStatusFailed:%v", detail)
	return temporal.NewNonRetryableApplicationError("transcoding error", "transcoding error", nil)
}

// collectVideoUrls 收集视频URL
func collectVideoUrls(sub *do.CommentarySubTask) (noSubtitleUrls, finalMergeUrls []string) {
	// 无擦除模式选择不同的素材URL
	if sub.EraseMode == consts.EraseModeOff {
		noSubtitleUrls = append(noSubtitleUrls, sub.MaterialHighlightUrl)
		finalMergeUrls = append(finalMergeUrls, sub.MergedMaterialHighlightUrl)
	} else {
		noSubtitleUrls = append(noSubtitleUrls, sub.NoSubtitleMaterialHighlightUrl)
		finalMergeUrls = append(finalMergeUrls, sub.MergedMaterialHighlightUrl)
	}

	// 添加高光片段URL
	for _, episodeUrl := range sub.HighlightEpisodesUrls {
		noSubtitleUrls = append(noSubtitleUrls, episodeUrl)
		finalMergeUrls = append(finalMergeUrls, episodeUrl)
	}

	// 添加结尾标签URL
	if sub.EndTagUrl != "" {
		noSubtitleUrls = append(noSubtitleUrls, sub.EndTagUrl)
		finalMergeUrls = append(finalMergeUrls, sub.EndTagUrl)
	}

	return noSubtitleUrls, finalMergeUrls
}

// handleSingleVideo 处理单个视频的情况
func handleSingleVideo(ctx context.Context, subTaskRepo commentary_repo.ICommentarySubTaskRepo, sub *do.CommentarySubTask, finalMergeUrls, noSubtitleUrls []string) (*bo.CommentaryMainTaskBO, error) {
	// 提取并上传封面
	coverUrl := ""
	imagePath := util.GenLocalPathWithPrefix("concat", uuid.New().String(), ".jpg")
	err := ffmpeg.ExtractNonBlackFrame(obs.GetCdnUrlByObjectName(finalMergeUrls[0]), imagePath)
	if err != nil {
		g.Log().Errorf(ctx, "ExtractNonBlackFrame failed, err: %v", err)
	} else {
		publicObsName := obs.GeneratePublicOBSUrl(consts.ObsModeVideoCommentary, "cover", ".jpg")
		coverUrl, err = obs.GetOsClient().UploadFile(imagePath, publicObsName, acl.AclTypePublicRead)
		if err != nil {
			g.Log().Errorf(ctx, "UploadFile failed, err: %v", err)
		}
	}

	// 更新子任务状态
	err = subTaskRepo.UpdateSubTaskFields(ctx, sub.Id, map[string]interface{}{
		"status":                int(consts.SubTaskStatusCompleted),
		"merge_status":          int(consts.MergeStatusCompleted),
		"need_merge":            0,
		"video_duration":        sub.MaterialHighlightDuration,
		"merged_video_url":      obs.GetCdnUrlByObjectName(finalMergeUrls[0]),
		"no_subtitle_video_url": obs.GetCdnUrlByObjectName(noSubtitleUrls[0]),
		"cover_url":             coverUrl,
	})
	if err != nil {
		return nil, temporal.NewNonRetryableApplicationError("UpdateTaskFields error", "db error", err)
	}
	concurrent.GoSafe(func() {
		err := hls.SendHlsRequest(context.Background(), []string{fmt.Sprintf("%d", sub.Id)})
		if err != nil {
			g.Log().Errorf(context.Background(), "SendHlsRequest failed, err: %v", err)
			return
		}
	})

	return nil, nil // 返回task会在调用处处理
}

func ConcatProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, from consts.WorkFlowFrom) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, task.RequestID)
	g.Log().Infof(ctx, "ConcatProcess task:%v, sub_task:%v", task, subTask)

	subTaskRepo := impl.NewCommentarySubTaskRepoImpl()

	// 获取子任务详情
	sub, err := subTaskRepo.GetSubTaskById(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "GetSubTaskById failed, err: %v", err)
		return nil, temporal.NewNonRetryableApplicationError("GetSubTaskById error", "db error", err)
	}
	// 收集视频URL
	noSubtitleVideoUrls, finalMergeVideoUrls := collectVideoUrls(sub)
	// 只有一个视频就不用合并了
	if len(finalMergeVideoUrls) == 1 && len(noSubtitleVideoUrls) == 1 {
		_, err := handleSingleVideo(ctx, subTaskRepo, sub, finalMergeVideoUrls, noSubtitleVideoUrls)
		if err != nil {
			g.Log().Errorf(ctx, "handleSingleVideo failed, err: %v", err)
			updateSubTaskMergeStatusFailed(ctx, subTask, from)
			if from == consts.WorkFlowFromCommentaryFirst {
				err := activities.UpdateSubTaskStatusFailed(ctx, "ConcatActivity", subTask)
				if err != nil {
					return nil, err
				}
			}
			return nil, err
		}
		// 修改状态
		return task, nil
	}

	// 多个视频需要合并
	a := &activities.Processor{}
	return a.Process(ctx, &ConcatActivity{
		subTaskRepo:         subTaskRepo,
		noSubtitleVideoUrls: noSubtitleVideoUrls,
		finalMergeVideoUrls: finalMergeVideoUrls,
		from:                from,
	}, task, subTask)

}

func updateSubTaskMergeStatusFailed(ctx context.Context, subTask *bo.CommentarySubTaskBO, from consts.WorkFlowFrom) error {
	if from == consts.WorkFlowFromCommentaryFirst {
		// 修改 合成 和 任务失败
		err := impl.NewCommentarySubTaskRepoImpl().UpdateSubTaskFields(ctx, subTask.Id, map[string]interface{}{
			"merge_status": int(consts.MergeStatusFailed),
			"status":       int(consts.SubTaskStatusFailed),
		})
		if err != nil {
			g.Log().Errorf(ctx, "UpdateSubTaskMergeStatusFailed err:%v", err)
			return temporal.NewNonRetryableApplicationError(
				"UpdateSubTaskMergeStatusFailed err",
				"db error", err)
		}

	} else {
		// 修改 合成失败
		err := impl.NewCommentarySubTaskRepoImpl().UpdateSubTaskFields(ctx, subTask.Id, map[string]interface{}{
			"merge_status": int(consts.MergeStatusFailed),
		})
		if err != nil {
			g.Log().Errorf(ctx, "UpdateSubTaskMergeStatusFailed err:%v", err)
			return temporal.NewNonRetryableApplicationError(
				"UpdateSubTaskMergeStatusFailed err",
				"db error", err)
		}

	}
	return nil
}
