package deduction

import (
	"business-workflow/internal/activities"
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	commentary_repo_impl "business-workflow/internal/repo/commentary_repo/impl"
	"context"
	"fmt"
	"strconv"

	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	omni_balance "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_balance"
	pb "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
	omni_tenant "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_tenant"
	"go.temporal.io/sdk/temporal"
)

func DeductionProcess(ctx context.Context, task *bo.CommentaryMainTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, activities.GetRequestId(ctx))
	g.Log().Info(ctx, "[DeductionProcess] task:%v", task)
	// 获取所有子任务
	subTasks, err := commentary_repo_impl.NewCommentarySubTaskRepoImpl().GetSubTasksByMainTaskId(ctx, task.Id)
	if err != nil {
		g.Log().Errorf(ctx, "[DeductionProcess] failed to get sub task list: %v", err)
		return handleDeductionFailure(ctx, task, fmt.Errorf("failed to get sub task list: %w", err))
	}

	// 转换为BO对象
	subTaskBOs := make([]*bo.CommentarySubTaskBO, len(subTasks))
	for i, st := range subTasks {
		subTaskBOs[i] = &bo.CommentarySubTaskBO{
			Id:                        st.Id,
			VideoDuration:             st.VideoDuration,
			MaterialHighlightDuration: st.MaterialHighlightDuration,
		}
	}

	// 执行扣款逻辑
	err = executeDeduction(ctx, task, subTaskBOs)
	if err != nil {
		return handleDeductionFailure(ctx, task, err)
	}

	// 扣款成功，更新状态
	return handleDeductionSuccess(ctx, task, subTaskBOs)
}

func executeDeduction(ctx context.Context, task *bo.CommentaryMainTaskBO, subTasks []*bo.CommentarySubTaskBO) error {
	// 1. 通过omni-tenant服务获取apikey_id
	tenantClient := omni_engine.GetOmniTenantClient()
	tenantReq := &omni_tenant.GetTenantApikeyByApikeyReq{
		Apikey: task.ApiKey,
	}
	tenantResp, err := tenantClient.GetTenantApikeyByApikey(ctx, tenantReq)
	if err != nil {
		g.Log().Errorf(ctx, "[executeDeduction] failed to get tenant apikey info: %v apiKey:%v", err, task.ApiKey)
		return fmt.Errorf("failed to get tenant apikey info: %w", err)
	}

	if tenantResp.TenantApikeyInfo == nil {
		g.Log().Errorf(ctx, "[executeDeduction] tenant apikey info is empty")
		return fmt.Errorf("tenant apikey info is empty")
	}

	apikeyId := tenantResp.TenantApikeyInfo.ApikeyId

	// 2. 计算总时长
	var totalDuration float64
	for _, subTask := range subTasks {
		totalDuration = subTask.MaterialHighlightDuration
		break
	}

	// 3. 构建扣款请求的segments和子任务扣款订单ID
	segments := make([]*omni_balance.VideoCommentarySegment, len(subTasks))
	for i, subTask := range subTasks {
		g.Log().Debugf(ctx, "[executeDeduction] subTask:%v", subTask)
		deductionOrderId := fmt.Sprintf("subTask_%d", subTask.Id)
		subTask.PayOrderId = deductionOrderId
		segments[i] = &omni_balance.VideoCommentarySegment{
			SegmentId:         deductionOrderId,
			GeneratedDuration: int64(task.TargetDurationRangeType),
		}
	}

	// 4. 确定业务类型
	OpenapiBusinessType := pb.BusinessScenariosType_BUSINESS_VIDEO_COMMENTARY_HIGHLIGHT_CLIP
	avoiceBusinessType := int32(pb.VoiceBusinessType_VOICE_BUSINESS_TYPE_VIDEO_COMMENTARY_HIGHLIGHT_CLIP)
	if task.TaskType == consts.TaskTypeCommentary {
		OpenapiBusinessType = pb.BusinessScenariosType_BUSINESS_VIDEO_COMMENTARY_HIGHLIGHT_COMMENTARY
		avoiceBusinessType = int32(pb.VoiceBusinessType_VOICE_BUSINESS_TYPE_VIDEO_COMMENTARY_HIGHLIGHT_COMMENTARY)
	}

	// 5. 调用omni-balance服务进行扣款
	balanceClient := omni_engine.GetOmniBalanceAVLBalance()
	exOrderId := fmt.Sprintf("%d", task.Id)
	deductionReq := &omni_balance.AVoiceVideoCommentaryDeductionCreditsRequest{
		AppId:               task.AppId,
		TenantId:            task.TenantId,
		ApikeyId:            apikeyId,
		OpenapiBusinessType: int32(OpenapiBusinessType), // 扣费类型
		AvoiceBusinessType:  avoiceBusinessType,
		OriginalDuration:    int64(totalDuration),
		Segments:            segments,
		MainTaskId:          exOrderId,
		ExOrderId:           exOrderId,
		EraseType:           int32(task.EraseEdition), // 使用擦除模式作为擦除类型
		EraseMode:           int32(task.EraseMode),
	}

	_, err = balanceClient.AVoiceVideoCommentaryDeductionCredits(ctx, deductionReq)
	if err != nil {
		g.Log().Errorf(ctx, "[executeDeduction] video commentary deduction failed: %v, req:%v", err, deductionReq)
		return fmt.Errorf("video commentary deduction failed: %w", err)
	}

	g.Log().Infof(ctx, "[executeDeduction] video commentary deduction successful, task ID: %d, total duration: %.2f seconds, apikey_id: %d", task.Id, totalDuration, apikeyId)
	return nil
}

func handleDeductionFailure(ctx context.Context, task *bo.CommentaryMainTaskBO, err error) (*bo.CommentaryMainTaskBO, error) {
	g.Log().Error(ctx, "[handleDeductionFailure] deduction failed: %v", err)

	// 更新主任务支付状态
	updatePayOrderStatus(ctx, task.Id, consts.PayOrderStatusFailed, consts.MainTaskStatusFailed)

	// 更新子任务支付状态
	updateSubTaskPayOrderStatus(ctx, task.Id, consts.PayOrderStatusFailed, consts.SubTaskStatusFailed)

	return task, temporal.NewNonRetryableApplicationError("DeductionError", "deduction error", nil)
}

func handleDeductionSuccess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTasks []*bo.CommentarySubTaskBO) (*bo.CommentaryMainTaskBO, error) {
	// 更新主任务支付状态为已支付（同时更新扣款订单ID）
	updatePayOrderStatus(ctx, task.Id, consts.PayOrderStatusPaid, consts.MainTaskStatusProcessing)

	// 更新子任务支付状态为已支付，使用传入的扣款订单ID
	updateSubTaskPayOrderStatusWithOrderIds(ctx, task.Id, consts.PayOrderStatusPaid, subTasks)

	return task, nil
}

func updatePayOrderStatus(ctx context.Context, taskId int64, payOrderStatus consts.PayOrderStatus, mainTaskStatus consts.CommentaryMainTaskStatus) {
	// 构建扣款订单ID
	exOrderId := fmt.Sprintf("%d", taskId)

	fields := map[string]interface{}{
		"pay_order_status": payOrderStatus,
		"pay_order_id":     exOrderId,
		"status":           mainTaskStatus,
	}
	if err := commentary_repo_impl.NewCommentaryMainTaskRepoImpl().UpdateTaskFields(ctx, taskId, fields); err != nil {
		g.Log().Error(ctx, "[updatePayOrderStatus] UpdatePayOrderStatus err:%v", err)
		return
	}

	g.Log().Info(ctx, "[updatePayOrderStatus] update pay order status and deduction order successful, taskId:%d, payOrderStatus:%d, exOrderId:%s", taskId, payOrderStatus, exOrderId)
}

func updateSubTaskPayOrderStatus(ctx context.Context, mainTaskId int64, payOrderStatus consts.PayOrderStatus, subTaskStatus consts.CommentarySubTaskStatus) {
	// 先获取所有子任务以生成扣款订单ID
	subTasksDO, err := commentary_repo_impl.NewCommentarySubTaskRepoImpl().GetSubTasksByMainTaskId(ctx, mainTaskId)
	if err != nil {
		g.Log().Error(ctx, "[updateSubTaskPayOrderStatus] GetSubTasksByMainTaskId err:%v, mainTaskId:%d", err, mainTaskId)
		return
	}

	if len(subTasksDO) == 0 {
		g.Log().Info(ctx, "[updateSubTaskPayOrderStatus] no sub tasks under main task ID %d, no update needed", mainTaskId)
		return
	}

	// 将DO转换为BO并为每个子任务生成扣款订单ID
	subTasksBO := make([]*bo.CommentarySubTaskBO, len(subTasksDO))
	for i, subTask := range subTasksDO {
		subTasksBO[i] = &bo.CommentarySubTaskBO{
			Id:         subTask.Id,
			PayOrderId: fmt.Sprintf("subTask_%d", subTask.Id),
		}
	}

	// 使用批量更新方法
	affectedRows, err := commentary_repo_impl.NewCommentarySubTaskRepoImpl().UpdateSubTasksPayOrderStatusAndDeductionOrder(ctx, payOrderStatus, subTaskStatus, subTasksBO)
	if err != nil {
		g.Log().Error(ctx, "[updateSubTaskPayOrderStatus] batch update sub task pay order status and deduction order ID failed: %v", err)
		return
	}

	g.Log().Info(ctx, "[updateSubTaskPayOrderStatus] batch update sub task pay order status successful, main task ID: %d, pay order status: %d, affected rows: %d", mainTaskId, payOrderStatus, affectedRows)
}

func updateSubTaskPayOrderStatusWithOrderIds(ctx context.Context, mainTaskId int64, payOrderStatus consts.PayOrderStatus, subTasks []*bo.CommentarySubTaskBO) {
	if len(subTasks) == 0 {
		g.Log().Info(ctx, "[updateSubTaskPayOrderStatusWithOrderIds] no sub tasks under main task ID %d, no update needed", mainTaskId)
		return
	}

	// 使用批量更新方法
	affectedRows, err := commentary_repo_impl.NewCommentarySubTaskRepoImpl().UpdateSubTasksPayOrderStatusAndDeductionOrder(ctx, payOrderStatus, consts.SubTaskStatusPending, subTasks)
	if err != nil {
		g.Log().Error(ctx, "[updateSubTaskPayOrderStatusWithOrderIds] batch update sub task pay order status and deduction order ID failed: %v", err)
		return
	}

	g.Log().Info(ctx, "[updateSubTaskPayOrderStatusWithOrderIds] batch update sub task pay order status successful, main task ID: %d, pay order status: %d, affected rows: %d", mainTaskId, payOrderStatus, affectedRows)
}

// VideoBatchSynthesisDeduction 视频批量合成扣费活动
func VideoBatchSynthesisDeduction(ctx context.Context, task *bo.CommentaryMainTaskBO, subTasks []*bo.CommentarySubTaskBO) error {
	g.Log().Infof(ctx, "[VideoBatchSynthesisDeduction] 开始视频合成扣费, mainTaskId: %d, subTaskCount: %d", task.Id, len(subTasks))

	// 构造TaskInfo列表
	taskDurationList := make([]*omni_balance.TaskInfo, 0, len(subTasks))
	for _, subTask := range subTasks {
		taskInfo := &omni_balance.TaskInfo{
			TaskId:     strconv.FormatInt(subTask.Id, 10),
			PayOrderId: subTask.MergePayOrderId,
			Duration:   int64(subTask.VideoDuration), // 将float64转换为int64秒

		}
		taskDurationList = append(taskDurationList, taskInfo)
	}

	// 4. 确定业务类型
	openapiBusinessType := pb.BusinessScenariosType_BUSINESS_VIDEO_COMMENTARY_HIGHLIGHT_CLIP
	avoiceBusinessType := pb.VoiceBusinessType_VOICE_BUSINESS_TYPE_VIDEO_COMMENTARY_HIGHLIGHT_CLIP
	if task.TaskType == consts.TaskTypeCommentary {
		openapiBusinessType = pb.BusinessScenariosType_BUSINESS_VIDEO_COMMENTARY_HIGHLIGHT_COMMENTARY
		avoiceBusinessType = pb.VoiceBusinessType_VOICE_BUSINESS_TYPE_VIDEO_COMMENTARY_HIGHLIGHT_COMMENTARY
	}
	// 构造扣费请求
	req := &omni_balance.AVoiceBatchSynthesisDeductionRequest{
		AppId:              task.AppId,
		TenantId:           task.TenantId,
		BusinessType:       int32(openapiBusinessType), // 视频解说业务类型
		AvoiceBusinessType: int32(avoiceBusinessType),
		TaskDurationList:   taskDurationList,
		OperatorId:         task.TenantId, // 使用租户ID作为操作人ID
	}

	// 调用omni-balance服务进行扣费
	client := omni_engine.GetOmniBalanceAVLBalance()
	// 添加流量标识
	//ctx = context.WithValue(ctx, "x-qw-traffic-mark", "JetDev-T0568")
	resp, err := client.AVoiceBatchSynthesisDeduction(ctx, req)
	if err != nil {
		g.Log().Errorf(ctx, "[VideoBatchSynthesisDeduction] 调用omni-balance扣费失败: %v", err)
		return err
	}

	g.Log().Infof(ctx, "[VideoBatchSynthesisDeduction] 视频合成扣费成功, totalCost: %d", resp.TotalCost)
	return nil
}
