package subtitlegenerate

import (
	"business-workflow/internal/entity/bo"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"

	"context"

	"github.com/gogf/gf/v2/frame/g"
)

func SubtitleGenerateProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, task.RequestID)
	g.Log().Info(ctx, "SubtitleGenerateProcess task:%v, sub_task:%+v", task, subTask)
	//  字幕文件生成 后续使用
	return task, nil
}
