package transcoding

import (
	"business-workflow/internal/activities"
	"business-workflow/internal/common/config"
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/common"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/repo/commentary_repo"
	impl2 "business-workflow/internal/repo/commentary_repo/impl"
	enginetaskrepo "business-workflow/internal/repo/engine_task_repo"
	"business-workflow/internal/repo/engine_task_repo/impl"
	"business-workflow/internal/util/obs"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/samber/lo"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	pb "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
	"gitlab.ttyuyin.com/tyr/x/utils"

	"github.com/gogf/gf/v2/frame/g"
	"go.temporal.io/sdk/temporal"
)

const prefix = "transcoding"

type TransCodingActivity struct {
	engineTaskRepo   enginetaskrepo.IEngineTaskRepo
	sourceDetailRepo commentary_repo.ICommentarySourceDetailRepo
	subTaskRepo      commentary_repo.ICommentarySubTaskRepo
	mainTaskRepo     commentary_repo.ICommentaryMainTaskRepo
}

func (a *TransCodingActivity) IsSkip(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) bool {
	mainTask, err := a.mainTaskRepo.GetTaskById(ctx, task.Id)
	if err != nil {
		g.Log().Errorf(ctx, "GetTaskById error:%v", err)
		return false
	}
	// 执行过就不执行了
	if len(mainTask.ClipMergeVideoUrl) > 0 && len(mainTask.CompressVideoUrl) > 0 {
		g.Log().Infof(ctx, "transcoding already done, skip id:%v", task.Id)
		return true
	}
	return false
}

func (a *TransCodingActivity) WaitCondition(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) error {
	// 实现等待逻辑...

	return nil
}

func (a *TransCodingActivity) HandleSubmit(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) ([]int64, error) {
	// 批量获取
	//获取全部视频
	sourceDetails, err := a.sourceDetailRepo.GetSourceDetailsByMainTaskIdWithFileType(ctx, task.Id, []consts.SourceFileType{consts.SourceFileTypeVideo})
	if err != nil {
		return nil, temporal.NewNonRetryableApplicationError(
			"db error",
			"GetSourceDetailsByMainTaskIdWithFileType error",
			err)
	}
	// 只转码解说
	sourceDetails = lo.Filter(sourceDetails, func(detail *do.CommentarySourceDetail, _ int) bool {
		return detail.BizType == consts.SourceBizTypeCommentary
	})

	g.Log().Infof(ctx, "sourceDetails:%v", sourceDetails)
	resolutionList := []string{string(consts.Resolution720x1280), string(consts.Resolution480x854)}
	if task.AspectRatio == string(consts.AspectRatio16_9) {
		resolutionList = []string{string(consts.Resolution1280x720), string(consts.Resolution854x480)}
	}
	req := &common.VideoTranscodingRequest{
		VideoParams: lo.Map(resolutionList, func(resolution string, i int) *common.VideoParam {
			FPS := 30
			CRF := 23
			Preset := 0
			if i == 1 { // 低分辨率 质量对应降低
				FPS = 25
				CRF = 28
				Preset = 2
			}
			return &common.VideoParam{
				VideoInfos: lo.Map(sourceDetails, func(detail *do.CommentarySourceDetail, _ int) *common.VideoInfo {
					return &common.VideoInfo{
						ID:             detail.Id,
						OriginVideoUrl: detail.SourceUrl,
						ResultVideoUrl: obs.GeneratePublicOBSUrlPubOrPri(consts.ObsModeVideoCommentary, "transcoding", ".mp4", "public"),
					}
				}),
				Resolution:    resolution,
				FPS:           FPS,
				CRF:           CRF,
				Mode:          1,
				Preset:        Preset,
				MergeVideoUrl: obs.GeneratePublicOBSUrlPubOrPri(consts.ObsModeVideoCommentary, "merge", ".mp4", "public"),
			}
		}),
	}
	// 请求前 先更新表  写入key
	updateData := make([]struct {
		ID     int64
		Fields map[string]interface{}
	}, len(sourceDetails))

	for i, detail := range sourceDetails {
		updateData[i] = struct {
			ID     int64
			Fields map[string]interface{}
		}{
			ID: detail.Id,
			Fields: map[string]interface{}{
				"target_resolution_key": req.VideoParams[0].VideoInfos[i].ResultVideoUrl, // 高分辨率
				"target_resolution_url": obs.GetCdnUrlByObjectName(req.VideoParams[0].VideoInfos[i].ResultVideoUrl),
				"low_resolution_key":    req.VideoParams[1].VideoInfos[i].ResultVideoUrl, // 低分辨率
				"low_resolution_url":    obs.GetCdnUrlByObjectName(req.VideoParams[1].VideoInfos[i].ResultVideoUrl),
			},
		}
	}

	err = a.sourceDetailRepo.BatchUpdateSourceDetailFields(ctx, updateData)

	if err != nil {
		g.Log().Errorf(ctx, "BatchUpdateSourceDetailFields error:%v", err)
		return nil, temporal.NewNonRetryableApplicationError(
			"db error",
			"BatchUpdateSourceDetailFields error",
			err)
	}
	totalCost := lo.SumBy(sourceDetails, func(detail *do.CommentarySourceDetail) float64 {
		return detail.Duration
	})
	g.Log().Infof(ctx, "TransCodingActivity HandleSubmit req:%v", req)
	businessScenariosType := pb.BusinessScenariosType_BUSINESS_VIDEO_COMMENTARY_HIGHLIGHT_COMMENTARY
	if task.TaskType == consts.TaskTypeHighlight {
		businessScenariosType = pb.BusinessScenariosType_BUSINESS_VIDEO_COMMENTARY_HIGHLIGHT_CLIP
	}
	curTaskInfo := &pb.TaskInfo{
		BizType:               pb.VoiceBusinessType_VOICE_BUSINESS_TYPE_VIDEO_COMMENTARY,
		BusinessScenariosType: int32(businessScenariosType),
		CommonInputContent:    utils.ToJson(req),
		TaskType:              pb.TaskType_VIDEO_TRANSCODING,
		TaskCost:              totalCost,
		RefererId:             task.Id,
	}
	taskBase := conv.CommentaryMainTaskBOToBaseTask(task, subTask, int64(pb.TaskSource_INTERNAL_SOURCE),
		map[string]string{
			consts.BaseTaskCost: fmt.Sprintf("%v", totalCost),
		})
	// 构建单个任务请求
	createReq := &pb.CreateTaskReq{
		TaskBase: taskBase,
		TaskList: []*pb.TaskInfo{curTaskInfo},
	}

	// 创建任务
	g.Log().Infof(ctx, "creating transcoding  task req:%v", createReq)
	createRes, err := omni_engine.GetOmniEngineClient().CreateTask(ctx, createReq)
	if err != nil {
		g.Log().Errorf(ctx, "create transcoding  task err:%v", err)
		return nil, temporal.NewNonRetryableApplicationError(
			"create transcoding  task err",
			"create transcoding  task err",
			err)
	}

	// 写入业务task表
	handoutTime := time.Now().Add(time.Duration(config.GetConfig().Temporal.ScheduleToCloseTimeout) / 2 * time.Minute)
	engineTaskRepo := impl.NewEngineTaskRepoImpl()
	engineTask := &do.BusinessEngineTask{
		AppId:         task.AppId,
		TenantID:      task.TenantId,
		MainTaskId:    task.Id,
		TaskType:      int(curTaskInfo.TaskType),
		Bid:           task.Id,
		EngineTaskId:  createRes.TaskIds[0],
		EngineStatus:  consts.EngineWorkflowStatusInit,
		HandleTimeout: &handoutTime,
	}

	err = engineTaskRepo.CreateWorkflowEngineTask(ctx, engineTask)
	if err != nil {
		return nil, temporal.NewNonRetryableApplicationError(
			"db error",
			"GetWorkflowEngineTask error",
			err)
	}
	return []int64{engineTask.EngineTaskId}, nil
}

func (a *TransCodingActivity) HandleResult(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, result []*do.BusinessEngineTask) error {
	g.Log().Infof(ctx, "TransCodingActivity HandleResult:%v", result)

	one := result[0]
	detail, err := one.TaskInfoConvertPB()
	if err != nil {
		g.Log().Errorf(ctx, "TaskInfoConvertPB err:%v", err)
		return err
	}

	switch detail.GetStatus() {
	case pb.TaskStatus_COMPLETED:
		return a.handleSuccess(ctx, task, detail)
	case pb.TaskStatus_FAILED, pb.TaskStatus_QUEUING_TIMEOUT:
		return a.handleFailure(ctx, task, detail)
	}

	return nil
}

func (a *TransCodingActivity) HandleFailed(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, aerr *temporal.ApplicationError) error {
	_, err := a.subTaskRepo.UpdateSubTaskStatusByMainTaskId(ctx, task.Id, consts.SubTaskStatusFailed, "transcoding error ")

	return temporal.NewNonRetryableApplicationError("transcoding error", "transcoding error", err)
}

func (a *TransCodingActivity) handleSuccess(ctx context.Context, task *bo.CommentaryMainTaskBO, detail *pb.Task) error {
	content := detail.GetCommonInputContent()
	req := &common.VideoTranscodingRequest{}
	err := json.Unmarshal([]byte(content), req)
	if err != nil {
		g.Log().Errorf(ctx, "unmarshal task_info error:%v", err)
		return temporal.NewNonRetryableApplicationError("db error", "Unmarshal error", err)
	}

	highParam := req.VideoParams[0]
	lowParam := req.VideoParams[1]

	highUrl := obs.GetCdnUrlByObjectName(highParam.MergeVideoUrl)
	lowUrl := obs.GetCdnUrlByObjectName(lowParam.MergeVideoUrl)
	// 更新数据库
	err = a.mainTaskRepo.UpdateTaskFields(ctx, task.Id, map[string]interface{}{"clip_merge_video_url": highUrl, "compress_video_url": lowUrl})
	if err != nil {
		return temporal.NewNonRetryableApplicationError(
			"UpdateTaskFields error",
			"db error",
			err)
	}

	g.Log().Infof(ctx, "transcoding success")
	return nil
}

func (a *TransCodingActivity) parseTranscodingResponse(ctx context.Context, detail *pb.Task) (*common.VideoTranscodingResponse, error) {
	resp := &common.VideoTranscodingResponse{}
	err := json.Unmarshal([]byte(detail.CommonOutputContent), resp)
	if err != nil {
		g.Log().Errorf(ctx, "unmarshal task_info error:%v", err)
		return nil, temporal.NewNonRetryableApplicationError("db error", "Unmarshal error", err)
	}
	return resp, nil
}

func (a *TransCodingActivity) handleFailure(ctx context.Context, task *bo.CommentaryMainTaskBO, detail *pb.Task) error {
	g.Log().Errorf(ctx, "TransCodingActivity HandleFailed:%v", detail)
	return temporal.NewNonRetryableApplicationError("transcoding error", "transcoding error", nil)
}

func TransCodingProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, activities.GetRequestId(ctx))
	g.Log().Infof(ctx, "TransCodingProcess task:%v, sub_task:%v", task, subTask)
	a := &activities.Processor{}
	return a.Process(ctx, &TransCodingActivity{
		sourceDetailRepo: impl2.NewCommentarySourceDetailRepoImpl(),
		engineTaskRepo:   impl.NewEngineTaskRepoImpl(),
		mainTaskRepo:     impl2.NewCommentaryMainTaskRepoImpl(),
		subTaskRepo:      impl2.NewCommentarySubTaskRepoImpl(),
	}, task, subTask)
}
