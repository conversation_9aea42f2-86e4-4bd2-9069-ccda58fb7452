package bo

import (
	"time"
)

// CommentaryEditOperationLogBO 解说编辑操作流水业务对象
type CommentaryEditOperationLogBO struct {
	Id            int64     `json:"id,omitempty"`
	MainTaskId    int64     `json:"main_task_id,omitempty"`
	SubTaskId     int64     `json:"sub_task_id,omitempty"`
	SubTaskName   string    `json:"sub_task_name,omitempty"`
	BizMode       int       `json:"biz_mode,omitempty"`
	DeductOrderId string    `json:"deduct_order_id,omitempty"`
	OperationType string    `json:"operation_type,omitempty"`
	OperationDesc string    `json:"operation_desc,omitempty"`
	CreatedAt     time.Time `json:"created_at,omitempty"`
	UpdatedAt     time.Time `json:"updated_at,omitempty"`
}