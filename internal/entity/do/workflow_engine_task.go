package do

import (
	"encoding/json"
	"time"

	pb "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"

	"gorm.io/gorm"
)

// engine 任务记录
type BusinessEngineTask struct {
	Id            int64          `gorm:"column:id;primary_key" json:"id"`                               // 唯一ID
	AppId         int64          `gorm:"column:app_id;type:bigint" json:"app_id"`                       // 应用ID
	MainTaskId    int64          `gorm:"column:main_task_id;type:bigint;index" json:"main_task_id"`     // 业务任务ID
	Bid           int64          `gorm:"column:bid;type:bigint;index" json:"bid"`                       // 子业务任务ID
	TenantID      int64          `gorm:"column:tenant_id;type:bigint;index" json:"tenant_id"`           // 用户ID
	EngineTaskId  int64          `gorm:"column:engine_task_id;type:bigint;index" json:"engine_task_id"` // engine任务ID
	TaskType      int            `gorm:"column:task_type;type:int" json:"task_type"`                    // 任务类型
	EngineStatus  int            `gorm:"column:engine_status;type:int;index" json:"engine_status"`      // omni_engine状态
	CreatedAt     time.Time      `gorm:"column:created_at;default:CURRENT_TIMESTAMP(3);comment:'创建时间'"`
	HandleTimeout *time.Time     `gorm:"column:handle_timeout;comment:'处理超时时间'" json:"handle_timeout"`
	ProcessAt     *time.Time     `gorm:"column:process_at;comment:'处理时间'"`
	FinishAt      *time.Time     `gorm:"column:finish_at;comment:'完成时间'"`
	UpdatedAt     time.Time      `gorm:"column:updated_at;default:CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3);comment:'更新时间'"`
	DeletedAt     gorm.DeletedAt `gorm:"column:deleted_at;index;comment:'删除时间'"`
	TaskInfo      string         `gorm:"column:task_info;type:longtext;comment:'任务信息'" json:"task_info"` //序列化后的pb.Task
}

func (BusinessEngineTask) TableName() string {
	return "business_engine_task"
}

func (w *BusinessEngineTask) TaskInfoConvertPB() (*pb.Task, error) {
	if w == nil {
		return nil, nil
	}
	// 更新字幕记录
	taskDetail := &pb.Task{}
	err := json.Unmarshal([]byte(w.TaskInfo), taskDetail)
	if err != nil {
		return nil, err
	}
	return taskDetail, nil
}
