package do

import (
	"business-workflow/internal/consts"
	"time"

	"gorm.io/gorm"
)

type WorkflowOcrTask struct {
	Id                  int64                         `gorm:"column:id;primary_key;comment:'唯一ID'" json:"id"`                          // 唯一ID
	AppId               int64                         `gorm:"column:app_id;type:bigint;comment:'应用ID'" json:"app_id"`                  // 应用ID
	MainTaskId          int64                         `gorm:"column:main_task_id;type:bigint;comment:'业务任务ID'" json:"main_task_id"`    // 业务任务ID
	SubTaskId           int64                         `gorm:"column:sub_task_id;type:bigint;index;comment:'子任务ID'" json:"sub_task_id"` // 子任务ID
	Bid                 int64                         `gorm:"column:bid;type:bigint;index;comment:'业务ID'" json:"bid"`
	TenantID            int64                         `gorm:"column:tenant_id;type:bigint;comment:'用户ID'" json:"tenant_id"`                           // 用户ID
	VideoUrl            string                        `gorm:"column:video_url;type:varchar(512);comment:'视频URL'" json:"video_url"`                    // 视频URL
	NotitleVideoUrl     string                        `gorm:"type:varchar(512);column:notitle_video_url;comment:'擦除后视频URL'" json:"notitle_video_url"` // 无标题视频路径
	Status              int                           `gorm:"column:status;type:int;index:idx_status;comment:'状态'" json:"status"`
	LangId              string                        `gorm:"column:lang_id;type:varchar(16);comment:'语言ID'" json:"lang_id"`
	EraseEdition        consts.CommentaryEraseEdition `gorm:"column:erase_edition;type:int;comment:'字幕擦除版本'" json:"erase_edition"`
	VideoDuration       float32                       `gorm:"column:video_duration;type:float;comment:'视频时长'" json:"video_duration"` //视频时长
	UserRect            int                           `gorm:"column:user_rect;type:smallint;comment:'用户自定义矩形区域'" json:"user_rect"`
	OcrRectInfo         string                        `gorm:"column:ocr_rect_info;type:varchar(256);comment:'OCR矩形区域信息'" json:"ocr_rect_info"`
	FullScreen          bool                          `gorm:"column:full_screen;type:tinyint;comment:'是否全屏'" json:"full_screen"`
	Fps                 int32                         `gorm:"column:fps;type:int;comment:'视频帧率'" json:"fps"` // 视频帧率
	Region              string                        `gorm:"column:region;type:varchar(128);comment:'主要文本的OCR区域'" json:"region"`
	TextsCompress       string                        `gorm:"column:texts_compress;comment:'OCR文本信息'" json:"texts_compress"`
	BboxsCompress       string                        `gorm:"column:bboxes_compress;comment:'OCR文本boxes信息'" json:"bboxes_compress"`
	ChunksCompress      string                        `gorm:"column:chunks_compress;comment:'预处理分块'" json:"chunks_compress"`
	OcrSubmitAt         time.Time                     `gorm:"column:ocr_submit_at;not null;default:CURRENT_TIMESTAMP(3);comment:'提交到OCR任务时间'"`
	OcrStartAt          time.Time                     `gorm:"column:ocr_start_at;not null;default:CURRENT_TIMESTAMP(3);comment:'OCR任务开始时间'"`
	OcrEndAt            time.Time                     `gorm:"column:ocr_end_at;not null;default:CURRENT_TIMESTAMP(3);comment:'OCR任务结束时间'"`
	PreprocessSubmitAt  time.Time                     `gorm:"column:preprocess_submit_at;not null;default:CURRENT_TIMESTAMP(3);comment:'提交到预处理任务时间'"`
	PreprocessStartAt   time.Time                     `gorm:"column:preprocess_start_at;not null;default:CURRENT_TIMESTAMP(3);comment:'预处理任务开始时间'"`
	PreprocessEndAt     time.Time                     `gorm:"column:preprocess_end_at;not null;default:CURRENT_TIMESTAMP(3);comment:'预处理任务结束时间'"`
	PostprocessSubmitAt time.Time                     `gorm:"column:postprocess_submit_at;not null;default:CURRENT_TIMESTAMP(3);comment:'提交到后处理任务时间'"`
	PostprocessStartAt  time.Time                     `gorm:"column:postprocess_start_at;not null;default:CURRENT_TIMESTAMP(3);comment:'后处理任务开始时间'"`
	PostprocessEndAt    time.Time                     `gorm:"column:postprocess_end_at;not null;default:CURRENT_TIMESTAMP(3);comment:'后处理任务结束时间'"`
	CreatedAt           time.Time                     `gorm:"column:created_at;default:CURRENT_TIMESTAMP(3);comment:'创建时间'"`                                // 创建时间
	UpdatedAt           time.Time                     `gorm:"column:updated_at;default:CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3);comment:'更新时间'"` // 更新时间
	DeletedAt           gorm.DeletedAt                `gorm:"column:deleted_at;default:null;comment:'删除时间'"`                                                // 删除时间
}

func (WorkflowOcrTask) TableName() string {
	return "workflow_ocr_task"
}
