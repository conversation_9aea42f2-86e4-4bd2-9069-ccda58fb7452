package do

import (
	"time"

	"gorm.io/gorm"
)

// 解说字幕表
type CommentarySubtitleItemSnapShot struct {
	Id             int64                   `gorm:"column:id;primary_key" json:"id"`
	SubTaskId      int64                   `gorm:"column:sub_task_id;type:bigint;not null;index" json:"sub_task_id"`           // 子任务ID
	MainTaskId     int64                   `gorm:"column:main_task_id;type:bigint;not null;index" json:"main_task_id"`         // 主任务ID
	SubtitleItemId int64                   `gorm:"column:subtitle_item_id;type:bigint;not null;index" json:"subtitle_item_id"` // 字幕项ID
	SubtitleItem   *CommentarySubtitleItem `gorm:"column:subtitle_item;type:json;serializer:json;" json:"sub_title_item"`
	CreatedAt      time.Time               `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP(3);comment:'创建时间'"`
	UpdatedAt      time.Time               `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3);comment:'更新时间'"`
	DeletedAt      gorm.DeletedAt          `gorm:"column:deleted_at;index;comment:'删除时间'"`
}

func (CommentarySubtitleItemSnapShot) TableName() string {
	return "commentary_subtitle_item_snapshot"
}
