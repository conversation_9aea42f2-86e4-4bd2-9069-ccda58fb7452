package do

import (
	"time"

	"gorm.io/gorm"
)

// CommentaryEditOperationLog 解说编辑操作流水表
type CommentaryEditOperationLog struct {
	Id            int64          `gorm:"column:id;primary_key" json:"id"`
	MainTaskId    int64          `gorm:"column:main_task_id;type:bigint;not null;index" json:"main_task_id"`       // 主任务ID
	SubTaskId     int64          `gorm:"column:sub_task_id;type:bigint;not null;index" json:"sub_task_id"`         // 子任务ID
	SubTaskName   string         `gorm:"column:sub_task_name;type:varchar(255);not null" json:"sub_task_name"`     // 子任务名称
	BizMode       int            `gorm:"column:biz_mode;type:int;not null" json:"biz_mode"`                        // 主任务的业务模式
	DeductOrderId string         `gorm:"column:deduct_order_id;type:varchar(100);not null" json:"deduct_order_id"` // 扣费订单ID
	OperationType string         `gorm:"column:operation_type;type:varchar(50);not null" json:"operation_type"`    // 操作类型：TTS_DEDUCT等
	OperationDesc string         `gorm:"column:operation_desc;type:varchar(500)" json:"operation_desc"`            // 操作描述
	CreatedAt     time.Time      `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP(3);comment:'创建时间'" json:"created_at"`
	UpdatedAt     time.Time      `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3);comment:'更新时间'" json:"updated_at"`
	DeletedAt     gorm.DeletedAt `gorm:"column:deleted_at;index;comment:'删除时间'" json:"deleted_at"`
}

func (CommentaryEditOperationLog) TableName() string {
	return "commentary_edit_operation_log"
}
