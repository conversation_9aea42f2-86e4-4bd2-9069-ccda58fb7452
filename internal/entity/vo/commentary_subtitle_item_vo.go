package vo

import (
	"business-workflow/internal/entity/common"
	"time"

	"github.com/gogf/gf/v2/frame/g"
)

// CommentarySubtitleItemDeleteReq 删除字幕
type DeleteSubtitleItemReq struct {
	g.Meta `path:"/commentary/subtitle_item/delete_subtitle" tags:"CommentarySubtitleItem" method:"delete" summary:"删除解说字幕项"`
	common.BaseParam
	Tid   int64 `json:"tid,string" dc:"任务ID"`
	SubId int64 `json:"sub_id,string" dc:"字幕项ID"`
}

type DeleteSubtitleItemRes struct {
}

// 文本翻译
type TextTranslateReq struct {
	g.Meta `path:"/commentary/subtitle_item/text_translate" tags:"CommentarySubtitleItem" method:"post" summary:"翻译(包括回译)"`
	common.BaseParam
	Tid           int64  `json:"tid,string" dc:"任务ID"`
	SubId         int64  `json:"sub_id,string" dc:"字幕项ID"`
	TranslateBack bool   `json:"translate_back" dc:"是否回译"`
	CustomStyle   string `json:"custom_style"`
}

type TextTranslateRes struct {
	TextTranslateStatus int `json:"text_translate_status"`
}

type GetBatchTextTranslateStatusReq struct {
	g.Meta `path:"/commentary/subtitle_item/get_batch_text_translate_status" tags:"CommentarySubtitleItem" method:"post" summary:"获取翻译状态"`
	common.BaseParam
	Tid    int64    `json:"tid,string"`
	SubIds []string `json:"sub_ids"`
}
type GetBatchTextTranslateStatusRes struct {
	ReTranslateStatusList []*ReTranslateStatusInfo `json:"retranslate_status_list"`
}

type ReTranslateStatusInfo struct {
	SubId             int64  `json:"sub_id,string"`
	ReTranslateStatus int    `json:"retranslate_status"`
	PostText          string `json:"post_text"`
	*SubtitleItem     `json:"subtitle_item"`
}

type GenerateVoiceReq struct {
	g.Meta `path:"/commentary/subtitle_item/generate_voice" tags:"CommentarySubtitleItem" method:"post" summary:"配音"`
	common.BaseParam
	Tid   int64 `json:"tid,string"`
	SubId int64 `json:"sub_id,string"`
}

type GenerateVoiceRes struct {
	RegenerateStatus int `json:"regenerate_status"`
}

type GetBatchGenerateVoiceStatusReq struct {
	g.Meta `path:"/commentary/subtitle_item/get_batch_generate_voice_status" tags:"CommentarySubtitleItem" method:"post" summary:"查询配音状态"`
	common.BaseParam
	Tid    int64    `json:"tid,string"`
	SubIds []string `json:"sub_ids"`
}
type GetBatchGenerateVoiceStatusRes struct {
	RegenerateStatusList []*RegenerateStatusInfo `json:"regenerate_status_list"`
}

type RegenerateStatusInfo struct {
	SubId            int64        `json:"sub_id,string"`
	RegenerateStatus int          `json:"regenerate_status"`
	TtsUrl           string       `json:"tts_url"`
	SubItemList      []*SubItemVO `json:"sub_item_list"`
	*SubtitleItem    `json:"subtitle_item"`
}

// 修改字幕
type ModifySubtitleReq struct {
	g.Meta `path:"/commentary/subtitle_item/modify_subtitle" tags:"CommentarySubtitleItem" method:"post" summary:"修改字幕"`
	common.BaseParam
	Tid            int64   `json:"tid,string" comment:"任务id"`
	SubId          int64   `json:"sub_id,string" comment:"字幕id"`
	OriginText     string  `json:"origin_text"`
	PostText       string  `json:"post_text"`
	StartMs        string  `json:"start_ms"`
	EndMs          string  `json:"end_ms"`
	SrcLang        string  `json:"src_lang"`
	TgtLang        string  `json:"tgt_lang"`
	SpeakerId      int     `json:"speaker_id"`
	TargetSubtitle string  `json:"rich_post_text"` // 带标签的译文片段
	Speed          float32 `json:"speed"`          // 语速
}

type ModifySubtitleRes struct {
	*SubtitleItem
}

type SubtitleItem struct {
	Id                        int64                 `json:"id,string"`
	UuId                      int64                 `json:"uuid,string"`
	OriginText                string                `json:"origin"`
	PostText                  string                `json:"post"`
	WrapText                  string                `json:"wrap_text"`
	StartTime                 string                `json:"start_time"`
	EndTime                   string                `json:"end_time"`
	OriginStartTime           string                `json:"origin_start_time"` //原文开始时间
	OriginEndTime             string                `json:"origin_end_time"`   //原文结束时间
	OriginAudio               string                `json:"origin_audio"`
	TransAudio                string                `json:"trans_audio"`
	TextRect                  string                `json:"text_rect"`
	IsUpdate                  bool                  `json:"is_update"`
	LastModify                int64                 `json:"last_modify,string"`
	ModifyRect                string                `json:"modify_rect"`
	BackTranslateSimilarity   int                   `json:"back_translate_similarity"` // 回译相似度
	BackTranslateText         string                `json:"back_translate_text"`       // 回译译文
	SpeakerId                 int                   `json:"speaker_id"`
	SpeakerName               string                `json:"speaker_name"`
	RegenerateStatus          int                   `json:"regenerate_status"`            // 重新生成状态, see const RegenerateStatusXXX
	ReTranslateStatus         int                   `json:"retranslate_status"`           // 重新翻译状态, see const ReTranslateStatusXXX
	AudioConfig               *common.AudioConfig   `json:"audio_config"`                 // 音频配置
	VoiceInfo                 *common.VoiceInfo     `json:"voice_info"`                   // 音色信息
	LatestTranslateOriginText string                `json:"latest_translate_origin_text"` // 最新翻译的原文
	LatestTranslateBackText   string                `json:"latest_translate_back_text"`   // 最近回译的译文
	TargetSubtitle            string                `json:"rich_post_text"`               // 带标签的译文
	OriginWords               []*common.WordInfo    `json:"origin_words"`                 // 原文拆词列表
	SubtitleWords             []*common.WordInfo    `json:"subtitle_words"`               // 译文拆词列表
	LatestTTSInfo             *common.LatestTTSInfo `json:"latest_tts_info"`
	SubItemList               []*SubItemVO          `json:"sub_item_list"`
}

// 添加字幕
type AddSubtitleReq struct {
	g.Meta `path:"/commentary/subtitle_item/add_subtitle" tags:"CommentarySubtitleItem" method:"post" summary:"添加字幕"`
	common.BaseParam
	Tid           int64             `json:"tid,string"`
	OffId         int64             `json:"off_id"`
	ItemIdx       int32             `json:"item_idx"`
	OriginText    string            `json:"origin_text"`
	PostText      string            `json:"post_text"`
	StartMs       string            `json:"start_ms"`
	EndMs         string            `json:"end_ms"`
	SpeakerId     int               `json:"speaker_id"`
	VoiceInfo     *common.VoiceInfo `json:"voice_info"` // 音色信息
	OriginStartMs string            `json:"origin_start_ms"`
	OriginEndMs   string            `json:"origin_end_ms"`
}
type AddSubtitleRes struct {
	SubtitleItem *SubtitleItem `json:"subtitle_item"`
}

// 修改片段语速
type ModifySubtitleSpeedReq struct {
	g.Meta `path:"/commentary/subtitle_item/modify_subtitle_speed" tags:"CommentarySubtitleItem" method:"post" summary:"修改片段语速"`
	common.BaseParam
	Tid            int64   `json:"tid,string"`              // 任务ID
	SubtitleItemId int64   `json:"subtitle_item_id,string"` // 片段的ID
	Speed          float32 `json:"speed"`                   // 语速
	ApplyToAll     bool    `json:"apply_to_all"`            // 是否应用到全部片段
}

type ModifySubtitleSpeedRes struct {
	Items []*SubtitleItem `json:"items"` // 片段列表
}

// 修改片段音量
type ModifySubtitleVolumeReq struct {
	g.Meta `path:"/commentary/subtitle_item/modify_subtitle_volume" tags:"CommentarySubtitleItem" method:"post" summary:"修改片段音量"`
	common.BaseParam
	Tid            int64   `json:"tid,string"`              // 任务ID
	SubtitleItemId int64   `json:"subtitle_item_id,string"` // 片段的ID
	VolumeGainDB   float32 `json:"volume_gain_db"`          // 音量加减值
	ApplyToAll     bool    `json:"apply_to_all"`            // 是否应用到全部片段
}
type ModifySubtitleVolumeRes struct {
}

// 拆分片段
type SplitSubtitleReq struct {
	g.Meta `path:"/commentary/subtitle_item/split_subtitle" tags:"CommentarySubtitleItem" method:"post" summary:"拆分片段"`
	common.BaseParam
	Tid            int64  `json:"tid,string"`       // 任务ID
	SubId          int64  `json:"sub_id,string"`    // 片段ID
	OriginText     string `json:"origin_text"`      // 原文
	OriginTextUp   string `json:"origin_text_up"`   // 拆分的第一段原文
	OriginTextDown string `json:"origin_text_down"` // 拆分的第二段原文
}

type SplitSubtitleRes struct {
	SubtitleItemList []*SubtitleItem `json:"subtitle_item_list"` // 分割后的字幕列表
}

// 合并片段
type MergeSubtitleReq struct {
	g.Meta `path:"/commentary/subtitle_item/merge_subtitle" tags:"CommentarySubtitleItem" method:"post" summary:"合并片段"`
	common.BaseParam
	MergeSubtitleItemList []*MergeSubtitleItem `json:"merge_subtitle_item_list"`
}
type MergeSubtitleItem struct {
	Tid   int64 `json:"tid,string"`
	SubId int64 `json:"sub_id,string"`
}

type MergeSubtitleRes struct {
	*SubtitleItem
}

// CommentarySubtitleItemRes 获取解说字幕项响应
type CommentarySubtitleItemRes struct {
	Id                      int64                 `json:"id"`
	SubtitleItemId          int64                 `json:"subtitle_item_id,string"`
	SubTaskId               int64                 `json:"sub_task_id"`
	MainTaskId              int64                 `json:"main_task_id"`
	ItemIdx                 int                   `json:"item_idx"`
	OriginSubtitle          string                `json:"origin_subtitle"`
	TargetSubtitle          string                `json:"target_subtitle"`
	SourceLangId            string                `json:"source_lang_id"`
	TargetLangId            string                `json:"target_lang_id"`
	SubtitleStartStr        string                `json:"subtitle_start_str"`
	SubtitleEndStr          string                `json:"subtitle_end_str"`
	OriginSubtitleStartStr  string                `json:"origin_subtitle_start_str"`
	OriginSubtitleEndStr    string                `json:"origin_subtitle_end_str"`
	GenerateVoiceStatus     int                   `json:"generate_voice_status"`
	TextTranslateStatus     int                   `json:"text_translate_status"`
	LatestTTSInfo           *common.LatestTTSInfo `json:"latest_tts_info"`
	LatestTranslateBackText string                `json:"latest_translate_back_text"`
	TtsUrl                  string                `json:"tts_url"`
	SpeakerId               int64                 `json:"speaker_id"`
	CustomPrompt            string                `json:"custom_prompt"`
	AudioConfig             *common.AudioConfig   `json:"audio_config"`
	TtsWords                []*common.WordInfo    `json:"tts_words"`
	SubItemList             []*SubItemVO          `json:"sub_item_list"`
	LastModify              *time.Time            `json:"last_modify"`
	CreatedAt               *time.Time            `json:"created_at"`
	UpdatedAt               *time.Time            `json:"updated_at"`
}
