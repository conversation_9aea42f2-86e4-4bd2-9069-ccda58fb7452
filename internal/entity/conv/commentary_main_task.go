package conv

import (
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/common"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/entity/vo"
	"business-workflow/internal/util/id_generator"
	"maps"
	"strconv"

	pb "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"

	"errors"
	"time"
)

// CommentaryMainTaskCreateReqToDO 将创建请求转换为DO模型
func CommentaryMainTaskCreateReqToDO(req *bo.CommentaryMainTaskCreateReqBO, tenantId int64, requestId string) (*do.CommentaryMainTask, error) {
	if req == nil {
		return nil, errors.New("请求参数不能为空")
	}

	// 转换 TextRect 为 OcrRectInfo
	var ocrRectInfo *common.OcrRectInfo
	if len(req.TextRect) == 4 {
		ocrRectInfoVO := vo.ConvertTextRectToOcrRectInfo(req.TextRect, req.AspectRatio)
		if ocrRectInfoVO != nil {
			ocrRectInfo = &common.OcrRectInfo{
				TopLeft:     ocrRectInfoVO.TopLeft,
				TopRight:    ocrRectInfoVO.TopRight,
				BottomLeft:  ocrRectInfoVO.BottomLeft,
				BottomRight: ocrRectInfoVO.BottomRight,
			}
		}
	}
	//

	// 根据高宽比设置分辨率
	targetResolution := "720x1280" // 默认9:16
	switch req.AspectRatio {
	case "16:9":
		targetResolution = "1280x720"
	case "9:16":
		targetResolution = "720x1280"
	}
	id, err := id_generator.GenerateId()
	if err != nil {
		return nil, err
	}
	name := "高光解说"
	if req.TaskType == consts.TaskTypeHighlight {
		name = "高光剪辑"
	}
	now := time.Now()
	// 正确初始化 JSON 数组字段
	voiceIDList := make([]int64, 0)
	if len(req.VoiceIDList) > 0 {
		voiceIDList = req.VoiceIDList
	}
	bgmMode := consts.BgmModeOff
	if len(req.BgmSourceList) > 0 {
		bgmMode = consts.BgmModeOn
	}
	return &do.CommentaryMainTask{
		Id:                      id,
		AppId:                   req.AppId,
		ApiKey:                  req.ApiKey,
		Name:                    name,
		TenantId:                tenantId,
		TaskType:                req.TaskType,
		BizMode:                 req.BizMode,
		BgmMode:                 bgmMode,
		SubtitleMode:            req.SubtitleMode,
		EraseMode:               req.EraseMode,
		EraseEdition:            req.EraseEdition,
		OcrRectInfo:             ocrRectInfo,
		TargetLangId:            req.TargetLangId,
		TargetResolution:        targetResolution,
		AspectRatio:             req.AspectRatio,
		TargetDurationRangeType: req.TargetDurationRangeType,
		TargetDurationRangeStr:  req.TargetDurationRangeStr,
		Status:                  consts.MainTaskStatusPending,
		TargetNumber:            req.TargetNumber,
		VoiceIDList:             voiceIDList,
		RequestID:               requestId,
		CreatedAt:               now,
		UpdatedAt:               now,
	}, nil
}

// CommentaryMainTaskDOToBO 从 DO 转换为 BO
func CommentaryMainTaskDOToBO(doObj *do.CommentaryMainTask) *bo.CommentaryMainTaskBO {
	if doObj == nil {
		return nil
	}

	bo := &bo.CommentaryMainTaskBO{
		Id:                      doObj.Id,
		AppId:                   doObj.AppId,
		ApiKey:                  doObj.ApiKey,
		Name:                    doObj.Name,
		TenantId:                doObj.TenantId,
		TaskType:                doObj.TaskType,
		BizMode:                 doObj.BizMode,
		BgmMode:                 doObj.BgmMode,
		SubtitleMode:            doObj.SubtitleMode,
		EraseMode:               doObj.EraseMode,
		EraseEdition:            doObj.EraseEdition,
		TargetLangId:            doObj.TargetLangId,
		TargetResolution:        doObj.TargetResolution,
		AspectRatio:             doObj.AspectRatio,
		TargetDurationRangeType: doObj.TargetDurationRangeType,
		TargetDurationRangeStr:  doObj.TargetDurationRangeStr,
		Status:                  doObj.Status,
		ErrMsg:                  doObj.ErrMsg,
		PayOrderId:              doObj.PayOrderId,
		TargetNumber:            doObj.TargetNumber,
		ClipMergeVideoUrl:       doObj.ClipMergeVideoUrl,
		VoiceIDList:             doObj.VoiceIDList,
		DubbingType:             doObj.DubbingType,
		RequestID:               doObj.RequestID,
		OcrRectInfo:             doObj.OcrRectInfo.Clone(),
		CreatedAt:               doObj.CreatedAt,
		UpdatedAt:               doObj.UpdatedAt,
	}

	return bo
}

// CommentaryMainTaskBOToDO 从 BO 转换为 DO
func CommentaryMainTaskBOToDO(bo *bo.CommentaryMainTaskBO) *do.CommentaryMainTask {
	if bo == nil {
		return nil
	}

	doObj := &do.CommentaryMainTask{
		Id:                      bo.Id,
		AppId:                   bo.AppId,
		ApiKey:                  bo.ApiKey,
		Name:                    bo.Name,
		TenantId:                bo.TenantId,
		TaskType:                bo.TaskType,
		BizMode:                 bo.BizMode,
		BgmMode:                 bo.BgmMode,
		SubtitleMode:            bo.SubtitleMode,
		EraseMode:               bo.EraseMode,
		EraseEdition:            bo.EraseEdition,
		TargetLangId:            bo.TargetLangId,
		TargetResolution:        bo.TargetResolution,
		AspectRatio:             bo.AspectRatio,
		TargetDurationRangeType: bo.TargetDurationRangeType,
		TargetDurationRangeStr:  bo.TargetDurationRangeStr,
		Status:                  bo.Status,
		ErrMsg:                  bo.ErrMsg,
		PayOrderId:              bo.PayOrderId,
		TargetNumber:            bo.TargetNumber,
		ClipMergeVideoUrl:       bo.ClipMergeVideoUrl,
		VoiceIDList:             bo.VoiceIDList,
		DubbingType:             bo.DubbingType,
		RequestID:               bo.RequestID,
		OcrRectInfo:             bo.OcrRectInfo.Clone(),
		CreatedAt:               bo.CreatedAt,
		UpdatedAt:               bo.UpdatedAt,
	}

	return doObj
}

// VO 转 BO 转换方法

// CommentaryMainTaskCreateReqBOToVO BO 转 VO
func CommentaryMainTaskCreateReqBOToVO(bo *bo.CommentaryMainTaskCreateReqBO) *vo.CommentaryMainTaskCreateReq {
	if bo == nil {
		return nil
	}

	voReq := &vo.CommentaryMainTaskCreateReq{
		TaskType:                bo.TaskType,
		BizMode:                 bo.BizMode,
		BgmMode:                 bo.BgmMode,
		SubtitleMode:            bo.SubtitleMode,
		EraseMode:               bo.EraseMode,
		EraseEdition:            bo.EraseEdition,
		TextRect:                bo.TextRect,
		TargetLangId:            bo.TargetLangId,
		AspectRatio:             bo.AspectRatio,
		TargetDurationRangeType: bo.TargetDurationRangeType,
		TargetDurationRangeStr:  bo.TargetDurationRangeStr,
		TargetNumber:            bo.TargetNumber,
		VoiceIDList:             bo.VoiceIDList,
		DubbingType:             bo.DubbingType,
	}

	// 设置 BaseParam 字段
	voReq.BaseParam.Appid = bo.AppId
	voReq.BaseParam.TenantId = bo.TenantId
	voReq.BaseParam.ApiKey = bo.ApiKey

	// 转换复杂嵌套对象
	if len(bo.MaterialSourceList) > 0 {
		voReq.MaterialSourceList = make([]*vo.MaterialSourceVO, len(bo.MaterialSourceList))
		for i, material := range bo.MaterialSourceList {
			voReq.MaterialSourceList[i] = &vo.MaterialSourceVO{
				MainTaskCommonSourceVO: vo.MainTaskCommonSourceVO{
					Name:           material.Name,
					SourceUrl:      material.SourceUrl,
					SourceFileType: material.SourceFileType,
					FromType:       material.FromType,
				},
				NoSubtitlePreference: material.NoSubtitlePreference,
			}
		}
	}

	if len(bo.HighlightEpisodesSourceList) > 0 {
		voReq.HighlightEpisodesSourceList = make([]*vo.HighlightEpisodesSource, len(bo.HighlightEpisodesSourceList))
		for i, highlight := range bo.HighlightEpisodesSourceList {
			voReq.HighlightEpisodesSourceList[i] = &vo.HighlightEpisodesSource{
				SourceList: make([]*vo.MainTaskCommonSourceVO, len(highlight.SourceList)),
			}
			for j, source := range highlight.SourceList {
				voReq.HighlightEpisodesSourceList[i].SourceList[j] = &vo.MainTaskCommonSourceVO{
					Name:           source.Name,
					SourceUrl:      source.SourceUrl,
					SourceFileType: source.SourceFileType,
					FromType:       source.FromType,
				}
			}
		}
	}

	if len(bo.EndTagSourceList) > 0 {
		voReq.EndTagSourceList = make([]*vo.MainTaskCommonSourceVO, len(bo.EndTagSourceList))
		for i, endTag := range bo.EndTagSourceList {
			voReq.EndTagSourceList[i] = &vo.MainTaskCommonSourceVO{
				Name:           endTag.Name,
				SourceUrl:      endTag.SourceUrl,
				SourceFileType: endTag.SourceFileType,
				FromType:       endTag.FromType,
			}
		}
	}

	if len(bo.BgmSourceList) > 0 {
		voReq.BgmSourceList = make([]*vo.MainTaskCommonSourceVO, len(bo.BgmSourceList))
		for i, bgm := range bo.BgmSourceList {
			voReq.BgmSourceList[i] = &vo.MainTaskCommonSourceVO{
				Name:           bgm.Name,
				SourceUrl:      bgm.SourceUrl,
				SourceFileType: bgm.SourceFileType,
				FromType:       bgm.FromType,
			}
		}
	}

	return voReq
}

// CommentaryMainTaskCreateReqVOToBO VO 转 BO
func CommentaryMainTaskCreateReqVOToBO(vo *vo.CommentaryMainTaskCreateReq) *bo.CommentaryMainTaskCreateReqBO {
	if vo == nil {
		return nil
	}

	boReq := &bo.CommentaryMainTaskCreateReqBO{
		AppId:                   vo.BaseParam.Appid,
		TenantId:                vo.BaseParam.TenantId,
		ApiKey:                  vo.BaseParam.ApiKey,
		TaskType:                vo.TaskType,
		BizMode:                 vo.BizMode,
		BgmMode:                 vo.BgmMode,
		SubtitleMode:            vo.SubtitleMode,
		EraseMode:               vo.EraseMode,
		EraseEdition:            vo.EraseEdition,
		TextRect:                vo.TextRect,
		TargetLangId:            vo.TargetLangId,
		AspectRatio:             vo.AspectRatio,
		TargetDurationRangeType: vo.TargetDurationRangeType,
		TargetDurationRangeStr:  vo.TargetDurationRangeStr,
		TargetNumber:            vo.TargetNumber,
		VoiceIDList:             vo.VoiceIDList,
		DubbingType:             vo.DubbingType,
	}

	// 转换复杂嵌套对象
	if len(vo.MaterialSourceList) > 0 {
		boReq.MaterialSourceList = make([]*bo.MaterialSourceBO, len(vo.MaterialSourceList))
		for i, material := range vo.MaterialSourceList {
			boReq.MaterialSourceList[i] = &bo.MaterialSourceBO{
				MainTaskCommonSourceBO: bo.MainTaskCommonSourceBO{
					Name:           material.Name,
					SourceUrl:      material.SourceUrl,
					SourceFileType: material.SourceFileType,
					FromType:       material.FromType,
				},
				NoSubtitlePreference: material.NoSubtitlePreference,
			}
		}
	}

	if len(vo.HighlightEpisodesSourceList) > 0 {
		boReq.HighlightEpisodesSourceList = make([]*bo.HighlightEpisodesSourceBO, len(vo.HighlightEpisodesSourceList))
		for i, highlight := range vo.HighlightEpisodesSourceList {
			boReq.HighlightEpisodesSourceList[i] = &bo.HighlightEpisodesSourceBO{
				SourceList: make([]*bo.MainTaskCommonSourceBO, len(highlight.SourceList)),
			}
			for j, source := range highlight.SourceList {
				boReq.HighlightEpisodesSourceList[i].SourceList[j] = &bo.MainTaskCommonSourceBO{
					Name:           source.Name,
					SourceUrl:      source.SourceUrl,
					SourceFileType: source.SourceFileType,
					FromType:       source.FromType,
				}
			}
		}
	}

	if len(vo.EndTagSourceList) > 0 {
		boReq.EndTagSourceList = make([]*bo.MainTaskCommonSourceBO, len(vo.EndTagSourceList))
		for i, endTag := range vo.EndTagSourceList {
			boReq.EndTagSourceList[i] = &bo.MainTaskCommonSourceBO{
				Name:           endTag.Name,
				SourceUrl:      endTag.SourceUrl,
				SourceFileType: endTag.SourceFileType,
				FromType:       endTag.FromType,
			}
		}
	}

	if len(vo.BgmSourceList) > 0 {
		boReq.BgmSourceList = make([]*bo.MainTaskCommonSourceBO, len(vo.BgmSourceList))
		for i, bgm := range vo.BgmSourceList {
			boReq.BgmSourceList[i] = &bo.MainTaskCommonSourceBO{
				Name:           bgm.Name,
				SourceUrl:      bgm.SourceUrl,
				SourceFileType: bgm.SourceFileType,
				FromType:       bgm.FromType,
			}
		}
	}

	return boReq
}

// CommentaryMainTaskCreateResBOToVO BO 转 VO
func CommentaryMainTaskCreateResBOToVO(bo *bo.CommentaryMainTaskCreateResBO) *vo.CommentaryMainTaskCreateRes {
	if bo == nil {
		return nil
	}

	return &vo.CommentaryMainTaskCreateRes{
		Id: bo.Id,
	}
}

// GetCommentaryTaskHistoryReqVOToBO VO 转 BO
func GetCommentaryTaskHistoryReqVOToBO(vo *vo.GetCommentaryTaskHistoryReq) *bo.GetCommentaryTaskHistoryReqBO {
	if vo == nil {
		return nil
	}

	return &bo.GetCommentaryTaskHistoryReqBO{
		TenantId:               vo.TenantId,
		Page:                   vo.Page,
		PageSize:               vo.PageSize,
		CommentaryTaskFilterBO: CommentaryTaskFilterVOToBO(&vo.CommentaryTaskFilterVO),
	}
}

// GetCommentaryTaskInfoResBOToVO BO 转 VO
func GetCommentaryTaskInfoResBOToVO(historys []*bo.CommentaryTaskHistoryBO) *vo.GetCommentaryTaskInfoRes {
	if len(historys) == 0 {
		return nil
	}

	res := &vo.GetCommentaryTaskInfoRes{}

	for _, info := range historys {
		mainTaskVO := CommentaryMainTaskBOToVO(info.MainTask)
		for _, subTask := range info.SubTasks {
			subTaskVO := CommentarySubTaskBOToVO(subTask)
			mainTaskVO.SubTasks = append(mainTaskVO.SubTasks, subTaskVO)
		}
		res.CommentaryTaskList = append(res.CommentaryTaskList, mainTaskVO)
	}

	return res
}

// CommentaryMainTaskBOToVO BO 转 VO (用于详情)
func CommentaryMainTaskBOToVO(bo *bo.CommentaryMainTaskBO) *vo.CommentaryMainTaskVO {
	if bo == nil {
		return nil
	}
	voObj := &vo.CommentaryMainTaskVO{
		Id:                      bo.Id,
		AppId:                   bo.AppId,
		Name:                    bo.Name,
		TenantId:                bo.TenantId,
		TaskType:                bo.TaskType,
		BizMode:                 bo.BizMode,
		BgmMode:                 bo.BgmMode,
		SubtitleMode:            bo.SubtitleMode,
		EraseMode:               bo.EraseMode,
		EraseEdition:            bo.EraseEdition,
		TargetLangId:            bo.TargetLangId,
		TargetResolution:        bo.TargetResolution,
		AspectRatio:             bo.AspectRatio,
		TargetDurationRangeType: bo.TargetDurationRangeType,
		TargetDurationRangeStr:  bo.TargetDurationRangeStr,
		Status:                  bo.Status,
		ErrMsg:                  bo.ErrMsg,
		PayOrderId:              bo.PayOrderId,
		PayOrderStatus:          bo.PayOrderStatus,
		TargetNumber:            bo.TargetNumber,
		//CompressVideoUrl:        bo.CompressVideoUrl,
		//OriginMergeVideoUrl:     bo.OriginMergeVideoUrl,
		VoiceIDList: bo.VoiceIDList,
		DubbingType: bo.DubbingType,
		RequestID:   bo.RequestID,
		OcrRectInfo: bo.OcrRectInfo,
		CreatedAt:   &bo.CreatedAt,
		UpdatedAt:   &bo.UpdatedAt,
	}
	return voObj
}

func CommentaryTaskFilterVOToBO(vo *vo.CommentaryTaskFilterVO) *bo.CommentaryTaskFilterBO {
	if vo == nil {
		return nil
	}
	return &bo.CommentaryTaskFilterBO{
		TargetLangIdList: vo.TargetLangIdList,
		BizModeList:      vo.BizModeList,
		EraseEditionList: vo.EraseEditionList,
		AspectRatioList:  vo.AspectRatioList,
	}
}

// BatchGetCommentaryTasksByOrderIdsReqVOToBO 批量获取解说任务请求 VO 转 BO
func BatchGetCommentaryTasksByOrderIdsReqVOToBO(vo *vo.BatchGetCommentaryTasksByOrderIdsReq) *bo.BatchGetCommentaryTasksByOrderIdsReqBO {
	if vo == nil {
		return nil
	}
	return &bo.BatchGetCommentaryTasksByOrderIdsReqBO{
		OrderIds: vo.OrderIds,
	}
}

// BatchGetCommentaryTasksByOrderIdsResBOToVO 批量获取解说任务响应 BO 转 VO
func BatchGetCommentaryTasksByOrderIdsResBOToVO(bo *bo.BatchGetCommentaryTasksByOrderIdsResBO) *vo.BatchGetCommentaryTasksByOrderIdsRes {
	if bo == nil {
		return nil
	}

	taskList := make([]*vo.CommentaryTaskDetails, len(bo.Tasks))
	for i, task := range bo.Tasks {
		taskList[i] = CommentaryTaskDetailsBOToVO(task)
	}

	return &vo.BatchGetCommentaryTasksByOrderIdsRes{
		TaskList: taskList,
	}
}

// CommentaryTaskDetailsBOToVO 解说任务详情 BO 转 VO
func CommentaryTaskDetailsBOToVO(bo *bo.CommentaryTaskDetailsBO) *vo.CommentaryTaskDetails {
	if bo == nil {
		return nil
	}

	// 从主任务中提取基本信息
	mainTask := bo.MainTask
	if mainTask == nil {
		return nil
	}

	// 遍历子任务汇总时长
	var totalDuration float64
	for _, subTask := range bo.SubTasks {
		if subTask != nil {
			totalDuration += subTask.VideoDuration
		}
	}

	return &vo.CommentaryTaskDetails{
		OrderId:     bo.OrderId,
		TenantId:    mainTask.TenantId,
		Status:      int(mainTask.Status),
		TaskId:      mainTask.Id,
		TaskName:    mainTask.Name,
		ProjectName: mainTask.Name, // 使用任务名称作为项目名称
		CreateTime:  mainTask.CreatedAt.Format("2006-01-02 15:04:05"),
		Duration:    int(totalDuration), // 从子任务中计算总时长
		BizMode:     int(mainTask.BizMode),
	}
}

func CommentaryMainTaskBOToBaseTask(task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, source int64, exm map[string]string) *pb.TaskBase {
	if task == nil {
		return nil
	}
	m := map[string]string{
		consts.BaseTaskCommentrayMainTaskID: strconv.FormatInt(task.Id, 10),
		consts.BaseTaskCommentrayBizMode:    strconv.FormatInt(int64(task.BizMode), 10),
		consts.BaseTaskCommentrayTaskType:   strconv.FormatInt(int64(task.TaskType), 10),
	}
	if subTask != nil && subTask.Id > 0 {
		m[consts.BaseTaskCommentraySubTaskID] = strconv.FormatInt(subTask.Id, 10)
	}
	maps.Copy(m, exm)
	return &pb.TaskBase{
		AppId:    task.AppId,
		TenantId: task.TenantId,
		Apikey:   task.ApiKey,
		Source:   source,
		ExtraMap: m,
	}
}
