package main

import (
	"business-workflow/internal/application/app"
	"business-workflow/internal/common/config"
	"business-workflow/internal/common/db"
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/controller/commentary_ctrl/impl"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/mw"
	"business-workflow/internal/temporal"
	"business-workflow/internal/timer"
	"business-workflow/internal/util/id_generator"
	"context"
	"time"

	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gcmd"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

func main() {
	// 数据库报错不需要重试
	cmd.Run(gctx.GetInitCtx())
}

var Version string

var (
	// Main is the main command.
	cmd = gcmd.Command{
		Name:  "main",
		Usage: "main",
		Brief: "start http server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			// 加载配置
			configFilePath := parser.GetOpt("config", "./configs/config.local.yaml").String()
			cfg, err := config.LoadConfig(ctx, configFilePath)
			if err != nil {
				g.Log().Errorf(ctx, "LoadConfig failed: %v", err)
				return err
			}
			g.Log().Infof(ctx, "LoadConfig success: %+v", cfg)

			err = config.InitBusinessConfig()
			if err != nil {
				g.Log().Errorf(ctx, "InitBusinessConfig failed: %v", err)
				return err
			}
			timerDConfigFilePath := parser.GetOpt("timer_config_file").String()

			// 初始化日志
			trace.InitLog(cfg.Logger.File, cfg.Logger.Path, Version, []interface{}{})
			temporal.InitTemporal()
			omni_engine.InitRPCClient(ctx)
			err = id_generator.InitIdGenerator(ctx, cfg.IdGen.Address)
			if err != nil {
				return err
			}

			// 数据库自动迁移（非生产环境） TODO 后续移除
			if cfg.Env != "prod" {
				db := db.GetDB()
				if err := db.AutoMigrate(
				&do.BusinessEngineTask{},
				&do.CommentaryMainTask{},
				&do.CommentarySubTask{},
				&do.CommentarySourceDetail{},
				&do.CommentarySubtitleItem{},
				&do.CommentaryEditOperationLog{},
				&do.WorkflowOcrTask{},
				&do.WorkflowEraseChunkTask{},
				&do.WorkflowMergeTask{},
			); err != nil {
					g.Log().Errorf(ctx, "Database migration failed: %v", err)
					return err
				}
				g.Log().Info(ctx, "Database migration completed")
			}

			// 初始化依赖服务
			globalApp, err := app.NewApp()
			if err != nil {
				return err
			}
			timerApp := timer.NewApp(ctx, globalApp.Svc, timerDConfigFilePath)

			// 初始化http服务
			s := g.Server()
			s.Use(trace.AccessLogMW)
			s.Use(mw.MiddlewareHandlerResponse)

			s.Group("/v1", func(group *ghttp.RouterGroup) {
				// Group middlewares.
				group.Middleware(trace.InjectDefaultHeaderMW)
				group.Middleware(middlewareCORS)
				//group.Middleware(ResponseMW)
				group.Bind(
					impl.NewCommentaryMainTaskCtrlImpl(globalApp.Svc),
					impl.NewCommentarySubTaskCtrlImpl(globalApp.Svc),
					impl.NewCommentarySubtitleItemCtrlImpl(globalApp.Svc),
				)
			})
			s.BindHandler("/health", func(r *ghttp.Request) {
				r.Response.Write()
				r.Response.WriteJson(g.Map{
					"time": time.Now().Format(time.RFC3339),
				})
			})

			s.BindHandler("/metrics", func(r *ghttp.Request) {
				promhttp.Handler().ServeHTTP(r.Response.Writer, r.Request)
			})

			s.SetAddr(cfg.Server.Address)
			s.SetOpenApiPath(cfg.Server.OpenapiPath)
			s.SetSwaggerPath(cfg.Server.SwaggerPath)

			// Just run the server.
			s.Run()
			timerApp.Stop()
			return nil
		},
	}
)

func middlewareCORS(r *ghttp.Request) {
	g.Log().Debugf(r.Context(), "middlewareCORS")
	r.Response.CORSDefault()
	r.Middleware.Next()
}
